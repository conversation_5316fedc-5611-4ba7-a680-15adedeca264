from datetime import time, datetime, timedelta, timezone
from decimal import Decimal
from typing import Any
import pytest
from dateutil.relativedelta import relativedelta
from mock import (
    patch,
)
from mock.mock import MagicMock
from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature import (
    BooksyGiftcardsEnabledFlag,
)
from lib.feature_flag.feature.payment import (
    CheckIfBGCAlreadyAssignedToAppointment,
)
from lib.feature_flag.feature.booksy_pay import BooksyPayFlag
from lib.feature_flag.feature.booking import BooksyGiftCardsComboServicesBugV0Flag
from lib.tests.utils import (
    create_or_set_booksy_gift_cards_settings_if_necessery,
    override_eppo_feature_flag,
    override_feature_flag,
)
from service.booking.tests.create_customer_appointment_tests import (
    AppointmentTestCaseMixin,
)
from service.tests import (
    BaseAsyncHTTPTest,
)
from webapps.booking.models import (
    Appointment,
)
from webapps.booking.notifications import SubBookingWithBGCCreatedNotification
from webapps.booking.notifications.appointment_created import AppointmentWithBGCCreatedNotification
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_custappt_data,
)
from webapps.business.enums import (
    PriceType,
)
from webapps.business.models import (
    Business,
    ServiceVariant,
    ServiceVariantPayment,
    Resource,
    ComboMembership,
    ComboType,
    Service,
)
from webapps.notification.elasticsearch import NotificationDocument
from webapps.pos.booksy_gift_cards.ports import BGCValidationResponse
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    BooksyGiftCard,
    PaymentRow,
    PaymentType,
    POS,
    POSPlan,
    Tip,
    Transaction,
)
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User
from webapps.business.baker_recipes import service_variant_recipe


@pytest.mark.django_db
@override_feature_flag({BooksyGiftcardsEnabledFlag: True, BooksyPayFlag: True})
class CreateCustomerAppointmentGiftCardTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):  # pylint: disable=too-many-public-methods
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )

        baker.make(Tip, pos=self.pos, rate=0)
        baker.make(Tip, pos=self.pos, rate=10, default=True)
        baker.make(Tip, pos=self.pos, rate=30)

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        self.adyen_pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            # not random just to force non-negative value of a fee (same for param below)
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(self.adyen_pos_plan)
        self.booksy_gift_cards_settings = create_or_set_booksy_gift_cards_settings_if_necessery(
            business=self.pos.business,
            future_value=True,
        )

    def _check_appointment_and_transaction_details(
        self, resp, deposit_amount, service_variant_price
    ):
        assert resp.code == 201
        appointment = Appointment.objects.filter(
            business_id=self.business.id,
            id=resp.json['appointment']['appointment_uid'],
        ).first()
        assert appointment.is_booksy_gift_card_appointment is True
        assert (
            Appointment.objects.filter(
                business_id=self.business.id,
            ).count()
            == 1
        )
        assert (
            Transaction.objects.filter(
                pos__business_id=self.business.id,
            ).count()
            == 1
        )

        transaction_info = self._check_appointment_and_transaction_info(resp, self.business.id)
        assert transaction_info['status_code'] == receipt_status.GIFT_CARD_DEPOSIT
        assert transaction_info['status_type'] == receipt_status.STATUS_TYPE__BOOKSY_GIFT_CARD
        assert len(transaction_info['payment_rows']) == 1
        payment_row = transaction_info['payment_rows'][0]
        assert Decimal(str(payment_row['amount'])) == deposit_amount
        assert payment_row['locked'] is True

        payment_row_db = PaymentRow.objects.filter(id=payment_row['id']).first()
        assert payment_row_db.amount == deposit_amount
        assert payment_row_db.status == receipt_status.GIFT_CARD_DEPOSIT

        txn = Transaction.objects.get(id=resp.json['appointment']['payment_info']['transaction_id'])
        assert txn.lock
        assert txn.total == service_variant_price
        assert payment_row_db.booksy_gift_cards is not None

    def test_200_create_without_prepayment_and_booksy_gift_card(self):
        self._test_booksy_gift_card_without_no_show_protection(Decimal('17.30'), Decimal('17.30'))

    def test_200_create_without_prepayment_and_booksy_gift_card_semi_auto(self):
        self.business.booking_mode = 'S'
        self.business.save()
        self._test_booksy_gift_card_without_no_show_protection(Decimal('17.30'), Decimal('17.30'))

    def test_200_create_without_prepayment_and_booksy_gift_card_cach_registry_disabled(self):
        self.pos.registers_shared_enabled = False
        self.pos.registers_enabled = True
        self.pos.save()
        self._test_booksy_gift_card_without_no_show_protection(Decimal('17.30'), Decimal('17.30'))

    def test_200_create_without_prepayment_and_booksy_gift_card_with_50_percent_of_price(self):
        self._test_booksy_gift_card_without_no_show_protection(Decimal('200.0'), Decimal('100.00'))

    def test_200_create_with_prepayment_and_booksy_gift_card(self):
        self._test_booksy_gift_card_with_no_show_protection(Decimal('17.30'), Decimal('17.30'))

    def test_200_create_with_prepayment_and_booksy_gift_card_with_preexisting_payment_type(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.BOOKSY_GIFT_CARD)
        self._test_booksy_gift_card_with_no_show_protection(Decimal('17.30'), Decimal('17.30'))

    def test_200_create_with_prepayment_and_booksy_gift_card_with_50_percent_of_price(self):
        self._test_booksy_gift_card_with_no_show_protection(Decimal('200.00'), Decimal('100.00'))

    def test_400_create_without_prepayment_and_booksy_gift_card_booksy_gift_cards_disabled(self):
        self.booksy_gift_cards_settings.accept_booksy_gift_cards = False
        self.booksy_gift_cards_settings.save()
        service_variant_price = deposit_amount = Decimal('17.30')
        assert self.business.pos_pay_by_app_enabled
        validate_booksy_gift_card_mock = MagicMock()
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            True, deposit_amount, None, ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        )
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=service_variant_price,
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors'][0]['description'] == (
            'Sorry, this business is not accepting Booksy Gift Cards yet,'
            ' please select another business.'
        )

    @patch('service.customer.my_booksy.is_in_experiment', MagicMock(return_value=False))
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def _test_booksy_gift_card_without_no_show_protection(
        self,
        service_variant_price: Decimal,
        deposit_amount: Decimal,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        assert self.business.pos_pay_by_app_enabled
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            True, deposit_amount, None, ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        )
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=service_variant_price,
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        self.assertIsNotNone(resp.json['appointment_payment']['gift_card_code'])
        self.assertIsNone(resp.json['appointment_payment']['gift_cards_ids'])
        assert (
            Appointment.objects.filter(
                business_id=self.business.id,
            ).count()
            == 0
        )
        assert (
            Transaction.objects.filter(
                pos__business_id=self.business.id,
            ).count()
            == 0
        )

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        self._check_appointment_and_transaction_details(resp, deposit_amount, service_variant_price)

        validate_booksy_gift_card_mock.assert_called_with(
            gift_card_code='123ABC123',
            user=self.user,
            value=service_variant_price,
        )

        use_booksy_gift_card_mock.assert_called_with(
            user_id=self.user.id,
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
            gift_card_hold=int(deposit_amount * 100),
        )

        booking_box_url = '/customer_api/my_booksy/'
        resp = self.fetch(booking_box_url, method='GET')
        self.assertEqual(resp.code, 200)
        self.assertEqual(
            resp.json['booking_box']['booksy_pay'],
            {'is_available': False, 'is_payment_window_open': False, 'is_paid': False},
        )

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    # pylint: disable=too-many-arguments
    # pylint: disable=too-many-positional-arguments
    def _test_booksy_gift_card_with_no_show_protection(
        self,
        service_variant_price: Decimal,
        deposit_amount: Decimal,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
        enable_booksy_gift_cards_if_not_enabled=True,  # pylint: disable=unused-argument
    ):
        assert self.business.pos_pay_by_app_enabled
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            True, deposit_amount, None, ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        )
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=service_variant_price,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('7.23'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        self.assertNotEqual(resp.json['appointment_payment']['prepayment_total'], '0.00')
        self.assertEqual(len(resp.json['appointment_payment']['payment_summary']['rows']), 0)

        body['gift_card_code'] = '123-abc-123'
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['prepayment_total'], '0.00')
        self.assertEqual(len(resp.json['appointment_payment']['payment_summary']['rows']), 2)
        self.assertEqual(
            resp.json['appointment_payment']['payment_summary']['rows'][-1]['key'], 'gift_card'
        )
        self.assertEqual(
            resp.json['appointment_payment']['payment_summary']['summary'][-1]['value'],
            str(service_variant_price - deposit_amount),
        )

        body.pop('gift_card_code')
        body['gift_cards_ids'] = ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        self.assertIsNone(resp.json['appointment_payment']['gift_card_code'])
        self.assertIsNotNone(resp.json['appointment_payment']['gift_cards_ids'])
        self._check_appointment_and_transaction_details(resp, deposit_amount, service_variant_price)

        validate_booksy_gift_card_mock.assert_called_with(
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
            user=self.user,
            value=service_variant_price,
        )

        use_booksy_gift_card_mock.assert_called_with(
            user_id=self.user.id,
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
            gift_card_hold=int(deposit_amount * 100),
        )

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    def test_400_create_without_prepayment_and_invalid_booksy_gift_card(
        self,
        mock_validate_booksy_gift_card_code: MagicMock,
    ):
        mock_validate_booksy_gift_card_code.return_value = BGCValidationResponse(
            is_valid=False, error_message='Card expired', gift_cards_ids=None
        )
        assert self.business.pos_pay_by_app_enabled
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert resp.json['errors'][0]['description'] == 'Card expired'
        assert (
            Appointment.objects.filter(
                business_id=self.business.id,
            ).count()
            == 0
        )
        assert (
            Transaction.objects.filter(
                pos__business_id=self.business.id,
            ).count()
            == 0
        )

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json['errors'][0]['description'] == 'Card expired'
        assert (
            Appointment.objects.filter(
                business_id=self.business.id,
            ).count()
            == 0
        )
        assert (
            Transaction.objects.filter(
                pos__business_id=self.business.id,
            ).count()
            == 0
        )

    @override_feature_flag({BooksyGiftcardsEnabledFlag: False})
    def test_400_create_without_prepayment_and_disabled_gift_cards(self):
        assert self.business.pos_pay_by_app_enabled
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert resp.json['errors'][0]['description'] == "Gift cards temporary disabled"
        assert (
            Appointment.objects.filter(
                business_id=self.business.id,
            ).count()
            == 0
        )
        assert (
            Transaction.objects.filter(
                pos__business_id=self.business.id,
            ).count()
            == 0
        )

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json['errors'][0]['description'] == "Gift cards temporary disabled"
        assert (
            Appointment.objects.filter(
                business_id=self.business.id,
            ).count()
            == 0
        )
        assert (
            Transaction.objects.filter(
                pos__business_id=self.business.id,
            ).count()
            == 0
        )

    @override_feature_flag({BooksyGiftcardsEnabledFlag: False})
    def test_hide_payment_summary_if_ff_disabled(self):
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body.pop('gift_card_code')
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 201
        self.assertNotIn('payment_summary', resp.json['appointment_payment'])

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_no_error_during_disable_tip_when_bgc(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        self.pos.tips_enabled = False
        self.pos.save()

        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 201

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_many_gift_cards_attached(
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['external_id_1', 'external_id_2'],
        )
        self.pos.tips_enabled = False
        self.pos.save()

        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body.pop('gift_card_code')
        body['gift_cards_ids'] = []
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        body['gift_cards_ids'] = [
            'bfc9e375-2c83-4c30-8713-b595ec8a3fc1',
            '287bc03f-4b49-4005-9708-19dbdb53988f',
        ]
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        self.assertEqual(resp.json['appointment_payment']['gift_cards_ids'], body['gift_cards_ids'])
        self.assertIsNone(resp.json['appointment_payment']['gift_card_code'])

        self._check_appointment_and_transaction_details(
            resp, Decimal('100.00'), just_service_variant.price
        )
        txn = Transaction.objects.get(appointment_id=resp.json['appointment']['appointment_uid'])
        self.assertEqual(txn.payment_rows.count(), 1)
        self.assertEqual(
            list(
                txn.payment_rows.first()
                .booksy_gift_cards.all()
                .values_list('external_id', flat=True)
            ),
            ['external_id_1', 'external_id_2'],
        )

        use_booksy_gift_card_mock.assert_called_with(
            user_id=self.user.id,
            gift_cards_ids=['external_id_1', 'external_id_2'],
            gift_card_hold=int(Decimal('100.00') * 100),
        )

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_no_error_if_price_starts_at(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        self.pos.tips_enabled = False
        self.pos.save()

        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.STARTS_AT,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = False

        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 201

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_error_if_price_not_defined(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        self.pos.tips_enabled = False
        self.pos.save()

        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.DONT_SHOW,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400

    @override_eppo_feature_flag({CheckIfBGCAlreadyAssignedToAppointment.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        # pylint: disable=line-too-long
        return_value=True,
    )
    def test_error_if_gift_card_already_assigned_somehow_ff_on(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        baker.make(BooksyGiftCard, external_id='9a488bbd-bc72-4673-8850-ec6ef2a8e8de')
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert not Appointment.objects.filter(business_id=self.business.id).exists()
        assert not Transaction.objects.filter(pos__business_id=self.business.id).exists()

    @override_eppo_feature_flag({CheckIfBGCAlreadyAssignedToAppointment.flag_name: False})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        # pylint: disable=line-too-long
        return_value=True,
    )
    def test_error_if_gift_card_already_assigned_somehow_ff_off(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        """
        That case should not occur in the application; rather, it represents the initial state
        of the application before the check is performed.
        """
        baker.make(BooksyGiftCard, external_id='9a488bbd-bc72-4673-8850-ec6ef2a8e8de')
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 201
        assert Appointment.objects.filter(business_id=self.business.id).exists()
        assert Transaction.objects.filter(pos__business_id=self.business.id).exists()

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_fixed_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        """Test with fixed price variants."""
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.FIXED), (50, PriceType.FIXED)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_starts_at_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        """Test with starts_at price variants."""
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.STARTS_AT), (50, PriceType.STARTS_AT), (50, PriceType.STARTS_AT)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_mixed_fixed_and_starts_at_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        """Test with mixed fixed and starts_at price variants."""
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.STARTS_AT), (50, PriceType.FIXED)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_mixed_free_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        """Test with mixed fixed and free price variants."""
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.FREE), (50, PriceType.FREE)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_mixed_varies_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        """Test with mixed fixed and varies price variants."""
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.VARIES), (50, PriceType.FREE), (50, PriceType.FREE)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_free_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FREE), (50, PriceType.FREE), (50, PriceType.FREE)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_combo_three_variants_mixed_dont_show_price(  # pylint: disable=line-too-long
        self,
        use_booksy_gift_card_mock,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock,
    ):
        """Test with mixed fixed and dont price variants."""
        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.DONT_SHOW), (50, PriceType.FREE), (50, PriceType.FREE)]
        )

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_multibooking_fixed(  # pylint: disable=line-too-long
        # pylint: disable=unused-argument
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        """
        Test if flow of making an appointment with combo_children property
        correctly extracts price types for single and combo service subbookings
        """

        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.STARTS_AT), (50, PriceType.FIXED)]
        )

        additional_subboking = self._create_service_variant_subbooking_dict(
            price_type=PriceType.FIXED, price=50, days_ahead=1
        )

        body['subbookings'].append(additional_subboking)

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_multibooking_starts_at(  # pylint: disable=line-too-long
        # pylint: disable=unused-argument
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        """
        Test if flow of making an appointment with combo_children property
        correctly extracts price types for single and combo service subbookings
        """

        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.STARTS_AT), (50, PriceType.FIXED)]
        )

        additional_subboking = self._create_service_variant_subbooking_dict(
            price_type=PriceType.STARTS_AT, price=50, days_ahead=1
        )

        body['subbookings'].append(additional_subboking)

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_multibooking_free(  # pylint: disable=line-too-long
        # pylint: disable=unused-argument
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        """
        Test if flow of making an appointment with combo_children property
        correctly extracts price types for single and combo service subbookings
        """

        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.STARTS_AT), (50, PriceType.FIXED)]
        )

        additional_subboking = self._create_service_variant_subbooking_dict(
            price_type=PriceType.FREE, price=50, days_ahead=1
        )

        body['subbookings'].append(additional_subboking)

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_multibooking_dont_show(  # pylint: disable=line-too-long
        # pylint: disable=unused-argument
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        """
        Test if flow of making an appointment with combo_children property
        correctly extracts price types for single and combo service subbookings
        """

        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.STARTS_AT), (50, PriceType.FIXED)]
        )

        additional_subboking = self._create_service_variant_subbooking_dict(
            price_type=PriceType.DONT_SHOW, price=50, days_ahead=1
        )

        body['subbookings'].append(additional_subboking)

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_multibooking__expanded_mixed_correct_price_types(  # pylint: disable=line-too-long
        # pylint: disable=unused-argument
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        """
        Test if flow of making an appointment with combo_children property
        correctly extracts price types for single and combo service subbookings
        """

        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.STARTS_AT), (50, PriceType.FIXED)]
        )

        additional_subbokings = [
            self._create_service_variant_subbooking_dict(
                price_type=price_type, price=price, days_ahead=days_ahead
            )
            for price_type, price, days_ahead in [
                (PriceType.FIXED, 50, 1),
                (PriceType.STARTS_AT, 50, 2),
            ]
        ]

        body['subbookings'].extend(additional_subbokings)

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @override_eppo_feature_flag({BooksyGiftCardsComboServicesBugV0Flag.flag_name: True})
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_create_customer_appointment_combo_service_booksy_gift_card_multibooking_expanded_mixed_incorrect_price_types(  # pylint: disable=line-too-long
        # pylint: disable=unused-argument
        self,
        use_booksy_gift_card_mock=None,
        validate_booksy_gift_card_mock=None,
    ):
        """
        Test if flow of making an appointment with combo_children property
        correctly extracts price types for single and combo service subbookings
        """

        self.validate_booksy_gift_card_mock = validate_booksy_gift_card_mock
        body = self._set_up_combo_service_test(
            [(50, PriceType.FIXED), (50, PriceType.DONT_SHOW), (50, PriceType.FIXED)]
        )

        additional_subbokings = [
            self._create_service_variant_subbooking_dict(
                price_type=price_type, price=price, days_ahead=days_ahead
            )
            for price_type, price, days_ahead in [
                (PriceType.FREE, 50, 1),
                (PriceType.STARTS_AT, 50, 2),
            ]
        ]

        body['subbookings'].extend(additional_subbokings)

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_notification_after_booking_with_bgc_only_staffer(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([self.staffer])

        body = self._get_standard_gift_card_body(just_service_variant)
        resp = self.fetch(self.url, method='POST', body=body)

        appt = Appointment.objects.get(id=resp.json['appointment']['appointment_uid'])
        SubBookingWithBGCCreatedNotification(appt.subbookings[0]).schedule_record.assert_success()
        AppointmentWithBGCCreatedNotification(appt).schedule_record.assert_skipped()

        NotificationDocument.refresh_index()
        assert (
            NotificationDocument.search()
            .query('term', notif_type='SubBookingWithBGCCreatedNotification')
            .query('term', business_id=self.business.id)
            .execute()
        )

        assert (
            not NotificationDocument.search()
            .query('term', notif_type='SubBookingCreatedNotification')
            .query('term', business_id=self.business.id)
            .execute()
        )

    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    def test_notification_after_booking_with_bgc_owner_and_staffer(
        self,
        use_booksy_gift_card_mock=None,  # pylint: disable=unused-argument
        validate_booksy_gift_card_mock=None,
    ):
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('100.00'),
            gift_cards_ids=['9a488bbd-bc72-4673-8850-ec6ef2a8e8de'],
        )
        staffer_user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='987654321',
        )
        staffer = baker.make(
            Resource,
            business=self.business,
            staff_user=staffer_user,
            staff_email='<EMAIL>',
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        self._make_resource_calendar(staffer)
        just_service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('100.00'),
        )
        just_service_variant.add_staffers([staffer])

        body = self._get_standard_gift_card_body(just_service_variant, staffer)
        resp = self.fetch(self.url, method='POST', body=body)

        appt = Appointment.objects.get(id=resp.json['appointment']['appointment_uid'])
        SubBookingWithBGCCreatedNotification(appt.subbookings[0]).schedule_record.assert_success()
        AppointmentWithBGCCreatedNotification(appt).schedule_record.assert_success()

        NotificationDocument.refresh_index()
        assert (
            NotificationDocument.search()
            .query('term', notif_type='SubBookingWithBGCCreatedNotification')
            .query('term', business_id=self.business.id)
            .execute()
        )

        assert (
            NotificationDocument.search()
            .query('term', notif_type='AppointmentWithBGCCreatedNotification')
            .query('term', business_id=self.business.id)
            .execute()
        )

    # Utility methods

    def _get_standard_gift_card_body(self, service_variant, staffer=None):
        body = build_custappt_data(
            variant=service_variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=staffer if staffer is not None else self.staffer,
            recurring=False,
        )
        body['gift_card_code'] = '123-abc-123'
        return body

    def _set_up_combo_service_variants(self, combo_variants: list[(int, PriceType)]):
        # Set up initial service
        service = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        service.add_staffers([self.staffer])

        # Set up combo service
        combo_variant = service_variant_recipe.make(
            service=service,
            service__business=self.business,
            service__combo_type=ComboType.SEQUENCE,
            type=None,
            price=None,
        )
        combo_variant.add_staffers([self.staffer])

        variants: list[ServiceVariant] = []
        for price, price_type in combo_variants:
            variant = service_variant_recipe.make(
                service__business=self.business,
                service__combo_type=ComboType.SEQUENCE,
                duration=relativedelta(minutes=45),
                time_slot_interval=relativedelta(minutes=15),
                price=price,
                type=price_type,
            )
            variant.add_staffers([self.staffer])
            baker.make(
                ComboMembership,
                combo=combo_variant,
                child=variant,
                _quantity=1,
            )
            variants.append(variant)
        return combo_variant, variants

    def _get_bgk_combo_service_body(
        self, combo_variant: ServiceVariant, child_variants: list[ServiceVariant]
    ) -> dict[str, Any]:
        start_date = datetime.now(tz=timezone.utc).replace(hour=10, minute=0, second=0) + timedelta(
            days=1
        )

        child_variants = [
            {
                'booked_from': (start_date + timedelta(hours=ind)).strftime('%Y-%m-%dT%H:%M'),
                'service_variant': {
                    'id': child_variant.id,
                    'mode': 'variant',
                },
                'staffer_id': -1,
            }
            for ind, child_variant in enumerate(child_variants)
        ]
        body = dict(
            {
                'subbookings': [
                    {
                        'combo_children': child_variants,
                        "booked_from": start_date.strftime('%Y-%m-%dT%H:%M'),
                        'service_variant': {
                            'id': combo_variant.id,
                            'mode': 'variant',
                        },
                        'staffer_id': -1,
                    },
                ],
                'compatibilities': {
                    'prepayment': True,
                },
                'gift_cards_ids': ['external_id_1'],
                'dry_run': True,
            }
        )
        return body

    def _create_service_variant_subbooking_dict(
        self, price_type: PriceType, price: float, days_ahead: int = 1
    ):
        """
        Creates a subbooking body dictionary

        Note: days ahead has to be set in case of adding more than one subboking,
        otherwise you'll get an error that this slot is already busy.
        """

        service = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        service.add_staffers([self.staffer])

        service_variant = service_variant_recipe.make(
            service=service,
            service__business=self.business,
            type=price_type,
            price=price,
        )
        service_variant.add_staffers([self.staffer])

        service_variant_data = {
            'id': service_variant.id,
            'mode': 'variant',
        }

        return {
            'combo_children': [],
            "booked_from": (
                datetime.now(tz=timezone.utc).replace(hour=10, minute=0, second=0)
                + timedelta(days=days_ahead)
            ).strftime('%Y-%m-%dT%H:%M'),
            'service_variant': service_variant_data,
            'staffer_id': -1,
        }

    def _set_up_combo_service_test(self, variants_price_types):
        """Utility method to set up combo service test and return request body."""
        self.validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            is_valid=True,
            balance=Decimal('200.00'),
            gift_cards_ids=['external_id_1', 'external_id_2'],
        )
        self.pos.tips_enabled = False
        self.pos.save()

        combo_variant, service_variants = self._set_up_combo_service_variants(variants_price_types)

        body = self._get_bgk_combo_service_body(
            combo_variant=combo_variant, child_variants=service_variants
        )
        return body

    @property
    def url(self):
        return self.appointments_url(self.business.id)
