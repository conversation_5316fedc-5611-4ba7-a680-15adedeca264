import datetime
import json
import typing as t
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from model_bakery import baker
from pytz import UTC
from rest_framework import status
from tornado.httpclient import HTTPResponse

from lib.tools import (
    l_b,
    sget_v2,
    tznow,
)
from service.booking.tests import get_before_and_after
from service.tests import BaseAsyncHTTPTest
from service.tests import dict_assert
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    AppointmentStatus,
    SubbookingServiceVariantMode,
)
from webapps.booking.models import Appointment, SubBooking
from webapps.booking.tests.utils import create_combo_service_variant
from webapps.business.elasticsearch.business_customer import BusinessCustomerDocument
from webapps.business.enums import PriceType, NoShowProtectionType
from webapps.business.models import (
    Resource,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.consents.models import Consent, ConsentForm
from webapps.consts import HOURS_TO_PAY_FOR_PREPAYMENT
from webapps.notification.enums import ScheduleState
from webapps.notification.models import NotificationSchedule
from webapps.pos.enums import PaymentTypeEnum, receipt_status, POSPlanPaymentTypeEnum
from webapps.pos.models import PaymentType, POS, Transaction, POSPlan
from webapps.stripe_integration.models import StripeAccount
from webapps.user.baker_recipes import user_recipe


class BaseCreateAppointmentTestCase(BaseAsyncHTTPTest):
    def tearDown(self):
        super().tearDown()

        Consent.all_objects.all().delete()
        ConsentForm.all_objects.all().delete()

    @staticmethod
    def get_path(business_id: int, dry_run=False) -> str:
        path = f'/business_api/me/businesses/{business_id}/appointments/'
        if dry_run:
            path = path + 'dry_run/'
        return path

    @property
    def req_appt_fields_in_resp(self):
        return {
            '_resource_selection_required',
            '_version',
            'actions',
            'appointment_id',
            'appointment_type',
            'appointment_uid',
            'booked_from',
            'booked_till',
            'business_note',
            'business_secret_note',
            'consent_forms',
            'customer',
            'customer_note',
            'external_source',
            'from_promo',
            'is_deposit_available',
            'join_meeting_url',
            'meeting_id',
            'partner_app_data',
            'payment_info',
            'repeating',
            'service_questions',
            'status',
            'subbookings',
            'total',
            'total_discount_amount',
            'total_tax_excluded',
            'total_value',
            'traveling',
            'type',
            'with_prepayment',
            'is_booksy_gift_card_appointment',
        }


@pytest.mark.django_db
class BaseAppointmentWithPrepaymentTestCase(BaseCreateAppointmentTestCase):
    def setUp(self):
        super().setUp()
        self.customer_user = user_recipe.make(
            cell_phone='+***********',
            email='<EMAIL>',
            payment_auto_accept=True,
        )
        self.bci = baker.make(
            'business.BusinessCustomerInfo',
            business=self.business,
            first_name='Name',
            last_name='Surname',
            cell_phone='+***********',
            email='<EMAIL>',
            user=self.customer_user,
            address_line_1='address 1',
            address_line_2='',
            city='New York',
            zipcode='12345',
        )
        self.bci.reindex()
        BusinessCustomerDocument.refresh_index()
        self.pos = baker.make(POS, business=self.business)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        self.pos.load_defaults()
        self.pos.tips_enabled = True
        self.pos.save()

        adyen_pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            # not random just to force non-negative value of a fee (same for param below)
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(adyen_pos_plan)

    def _standard_setup_for_prepayment(
        self,
        is_combo: t.Optional[bool] = False,
    ) -> t.Tuple[
        datetime.datetime,
        datetime.datetime,
        ServiceVariant,
        Resource,
    ]:
        return self._standard_setup(
            with_prepayment=True,
            is_combo=is_combo,
        )

    def _standard_setup_for_cancellation_fee(
        self,
        is_combo: t.Optional[bool] = False,
    ) -> t.Tuple[
        datetime.datetime,
        datetime.datetime,
        ServiceVariant,
        Resource,
    ]:
        return self._standard_setup(
            with_cancellation_fee=True,
            is_combo=is_combo,
        )

    def _standard_setup(
        self,
        with_cancellation_fee: t.Optional[bool] = False,
        with_prepayment: t.Optional[bool] = False,
        is_combo: t.Optional[bool] = False,
    ) -> t.Tuple[
        datetime.datetime,
        datetime.datetime,
        ServiceVariant,
        Resource,
    ]:
        if with_cancellation_fee and with_prepayment:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                "Only one type of no show protection can be used at once"
            )
        no_show_protection_type = None
        if with_cancellation_fee:
            no_show_protection_type = NoShowProtectionType.CANCELLATION_FEE
        elif with_prepayment:
            no_show_protection_type = NoShowProtectionType.PREPAYMENT
        return self._create_future_appointment_elements(
            no_show_protection_type=no_show_protection_type,
            is_combo=is_combo,
        )

    def _create_future_appointment_elements(
        self,
        no_show_protection_type: t.Optional[NoShowProtectionType] = None,
        is_combo: t.Optional[bool] = False,
    ) -> t.Tuple[
        datetime.datetime,
        datetime.datetime,
        ServiceVariant,
        Resource,
    ]:
        if is_combo:
            variant = create_combo_service_variant(
                business=self.business,
                staffer=baker.make(
                    Resource,
                    type=Resource.STAFF,
                    business=self.business,
                    active=True,
                ),
            )
        else:
            variant = baker.make(
                ServiceVariant,
                duration='0100',
                service=baker.make(
                    Service,
                    business=self.business,
                    gap_time=relativedelta(minutes=60),
                ),
                gap_hole_duration=relativedelta(minutes=30),
                gap_hole_start_after=relativedelta(minutes=5),
                type=PriceType.FIXED,
                price=17.30,
            )
        if no_show_protection_type:
            if no_show_protection_type == NoShowProtectionType.PREPAYMENT:
                payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE
            else:  # NoShowProtectionType.CANCELLATION_FEE
                payment_type = ServiceVariantPayment.CANCELLATION_FEE_TYPE

            if is_combo:
                variant_for_prepayment = variant.combo_children_list[0]
            else:
                variant_for_prepayment = variant
            baker.make(
                ServiceVariantPayment,
                service_variant=variant_for_prepayment,
                payment_type=payment_type,
                payment_amount=Decimal('9.99'),
                saving_type=ServiceVariantPayment.AMOUNT,
            )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
            staff_email='<EMAIL>',
        )
        variant.service.add_staffers([staffer])

        booked_from = get_before_and_after()[0]
        booked_till = booked_from + datetime.timedelta(hours=1)
        return booked_from, booked_till, variant, staffer

    def _get_standard_body(  # pylint: disable=[too-many-positional-arguments, too-many-arguments]
        self,
        booked_from: datetime.datetime,
        booked_till: datetime.datetime,
        variant: ServiceVariant,
        staffer: Resource,
        with_prepayment: t.Optional[bool] = None,
        with_service_variant: bool = True,
    ) -> dict:
        subbooking_body = {
            'appliance_id': None,
            'booked_from': booked_from.isoformat(),
            'booked_till': booked_till.isoformat(),
            'service_variant': None,
            'staffer_id': staffer.id,
            'type': Appointment.TYPE.BUSINESS,
        }
        if with_service_variant:
            subbooking_body['service_variant'] = {
                'mode': SubbookingServiceVariantMode.VARIANT,
                'id': variant.id,
            }

        body = {
            'customer': {
                'id': self.bci.id,
                'mode': ACMode.CUSTOMER_CARD,
            },
            'subbookings': [
                subbooking_body,
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }
        if with_prepayment in (True, False):
            body['with_prepayment'] = with_prepayment
        return body

    def _standard_check(
        self,
        resp: HTTPResponse,
        booked_from: datetime.datetime,
        booked_till: datetime.datetime,
        expected_status: AppointmentStatus,
    ):
        assert resp.code == status.HTTP_201_CREATED
        json_body = json.loads(l_b(resp.body))
        booking = SubBooking.objects.filter(
            id=resp.json['appointment']['subbookings'][0]['id'],
        ).first()
        assert booking

        assert len(json_body['appointment']['subbookings']) == 1
        assert json_body['appointment']['customer']['mode'] == ACMode.CUSTOMER_CARD

        assert booking.booked_from == booked_from.replace(tzinfo=UTC)
        assert booking.booked_till == booked_till.replace(tzinfo=UTC)
        assert booking.appointment.status == expected_status
        self._check_prepayment_deadline(resp)
        assert booking.appointment._version is not None  # pylint: disable=protected-access
        assert booking.appointment.business_id == self.business.id
        assert (
            json_body['appointment']['_version']
            == booking.appointment._version  # pylint: disable=protected-access
        ), 'Wrong booking version returned'

        diff = set(json_body['appointment'].keys()) ^ self.req_appt_fields_in_resp
        diff.difference_update({'repeating_series'})  # ignore
        assert not diff, f"Reponse doesn't have keys {diff} in appointment"

    @staticmethod
    def _check_prepayment_deadline(resp: HTTPResponse):
        appointment = Appointment.objects.filter(
            id=resp.json['appointment']['appointment_uid'],
        ).first()
        if sget_v2(appointment, ['status']) != AppointmentStatus.PENDING_PAYMENT:
            return
        assert (
            resp.json['appointment']['prepayment_deadline']
            == (
                appointment.created
                + datetime.timedelta(
                    hours=HOURS_TO_PAY_FOR_PREPAYMENT,
                )
            )
            .astimezone(appointment.business.get_timezone())
            .isoformat()[:16]
        )

    @staticmethod
    def _check_no_notification_created():
        assert not NotificationSchedule.objects.filter(
            task_id__contains="call_for_prepayment",
        ).exists()

    @staticmethod
    def _check_payment_details(resp: HTTPResponse):
        payment_info = resp.json['appointment']['payment_info']
        transaction_info = payment_info['transaction_info']
        assert transaction_info['status_type'] == receipt_status.STATUS_TYPE__CALL_FOR_DEPOSIT
        assert len(transaction_info['payment_rows']) == 1
        assert transaction_info['status_code'] == receipt_status.CALL_FOR_PREPAYMENT
        assert transaction_info['total'] == '$17.30'
        assert transaction_info['already_paid'] == '$9.99'
        assert transaction_info['remaining'] == '$7.31'

        transaction_id = payment_info['transaction_id']
        transaction = Transaction.objects.filter(
            id=transaction_id,
        ).first()
        assert transaction.total == Decimal('17.30')
        assert transaction.transaction_type == Transaction.TRANSACTION_TYPE__PAYMENT

    @staticmethod
    def check_possible_customer_actions(actions: dict):
        assert not actions['accept']
        assert not actions['change']
        assert actions['cancel']
        assert actions['pay']

    @staticmethod
    def check_possible_business_actions(actions: dict):
        assert not actions['change']
        assert actions['cancel']
        assert not actions['change_time_or_note']

    @staticmethod
    def _add_combo_children_fields_to_body(
        body: dict,
        booked_from: datetime.datetime,
        variant_combo_parent: ServiceVariant,
    ):
        subbooking_body = body['subbookings'][0]
        body_combo_children_list = []
        last_booked_from = booked_from
        for combo_children in variant_combo_parent.combo_children_list:
            new_booked_from = last_booked_from
            new_booked_till = last_booked_from + combo_children.time_slot_interval
            body_combo_children_list.append(
                {
                    "service_variant": {
                        'id': combo_children.id,
                        'version': combo_children.version,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    "booked_from": new_booked_from.isoformat(),
                    "booked_till": new_booked_till.isoformat(),
                    "staffer_id": combo_children.staffer_ids[0],
                    "appliance_id": -1,
                }
            )
            last_booked_from = new_booked_till
        subbooking_body['combo_children'] = body_combo_children_list

    def _prepare_stripe_account(self):
        baker.make(StripeAccount, pos=self.business.pos, kyc_verified_at_least_once=True)

    @staticmethod
    def get_path_appointment__business_action(business_id: int, appointment_id: int) -> str:
        return f'/business_api/me/businesses/{business_id}/appointments/{appointment_id}/action/?'
