import uuid

import pytest
import responses
from django.conf import settings
from mock import patch
from model_bakery import baker
from rest_framework import status

from lib.test_utils import (
    create_subbooking,
    increase_appointment_next_id,
)
from service.pos.tests.common import POSTestsMixin
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import BookingChange
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.notification.scenarios import BookingChangedScenario
from webapps.pos import enums
from webapps.pos.enums import (
    CARD_TYPE__APPLE_PAY,
    CARD_TYPE__GOOGLE_PAY,
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    PaymentMethod,
    PaymentRow,
    PaymentType,
    POS,
    Receipt,
    Transaction,
    TransactionRow,
    TransactionTip,
)
from webapps.pos.provider.fake import _CARDS
from webapps.pos.serializers import CustomerTransactionActionRequest
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.models import StripeAccount
from webapps.structure.models import Region
from webapps.user.models import (
    User,
    UserProfile,
)


@pytest.mark.django_db
class CustomerTransactionActionHandlerBaseTestCase(
    POSTestsMixin,
    BaseAsyncHTTPTest,
):
    def setUp(self):
        super().setUp()
        self.user.email = '<EMAIL>'
        self.user.cell_phone = '+***********'
        self.user.save()
        self.region = baker.make(Region, type='zip', name='B')
        baker.make(
            BusinessCustomerInfo,
            user=self.user,
        )
        baker.make(
            UserProfile,
            profile_type=UserProfile.Type.CUSTOMER,
            user=self.user,
        )

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            commissions_enabled=True,
            tips_enabled=True,
        )
        self.create_default_tax_rate_20(self.pos)
        self.pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        increase_appointment_next_id()
        self.booking = create_subbooking(business=self.business)[0]
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        self.txn = baker.make(
            Transaction,
            customer_card=baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='Jan',
                last_name='Matejko',
                cell_phone='1234567890',
            ),
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=baker.make(User),
            customer=self.user,
            subtotal=100,
            taxed_subtotal_services=100,
            taxed_subtotal_products=127,
            discounted_subtotal_services=108,
            discounted_subtotal_products=127,
            subtotal_services=108,
            subtotal_products=127,
            total=127,
            appointment_id=self.booking.appointment_id,
        )
        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=self.txn,
            service_variant=service_variant,
            item_price=127,
            total=127,
        )

    @property
    def url(self):
        return f'/customer_api/me/transactions/{self.txn.id}/action/'


class CustomerTransactionActionHandlerTestCase(CustomerTransactionActionHandlerBaseTestCase):
    def setUp(self):
        super().setUp()
        receipt = baker.make(
            Receipt,
            transaction=self.txn,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            payment_type=self.pba,
            already_paid=0,
        )
        self.txn.latest_receipt = receipt
        self.txn.save()

        baker.make(
            TransactionTip,
            transaction=self.txn,
            rate=0,
            amount=0,
            type=SimpleTip.TIP_TYPE__PERCENT,
        )

        self.payment_row = PaymentRow.create_with_status(
            receipt=receipt,
            status=receipt_status.CALL_FOR_PAYMENT,
            payment_type=self.pba,
            amount=self.txn.total,
        )
        self.payment_row.save()

    @staticmethod
    def mock_auth_success():
        responses.add(
            responses.POST,
            settings.ADYEN_AUTH_URL,
            json={
                "pspReference": "123123123",
                "resultCode": "Authorised",
                "authCode": "65496",
            },
        )

    def test_set_tip_old_format(self):
        body = {
            'action': enums.CUSTOMER_ACTION__SET_TIP_RATE,
            'tip_rate': 20,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_set_tip_both_format(self):
        body = {
            'action': enums.CUSTOMER_ACTION__SET_TIP_RATE,
            'tip_rate': 230,
            'tip': {
                'rate': 230,
                'type': 'H',
            },
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200
        data = resp.json['transaction']
        assert data['tip']['rate'] == '230.00'
        assert data['tip']['type'] == 'H'

        assert 'booking' in data.keys()
        assert 'multibooking' in data.keys()
        assert data['appointment_uid'] == self.booking.appointment_id

    def test_set_tip_new_format(self):
        body = {
            'action': enums.CUSTOMER_ACTION__SET_TIP_RATE,
            'tip': {
                'rate': 230,
                'type': 'H',
            },
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200
        data = resp.json['transaction']
        assert data['tip']['rate'] == '230.00'
        assert data['tip']['type'] == 'H'

    @responses.activate
    def test_pay_with_card(self):
        self.mock_auth_success()

        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'payment_method': card.id,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_200_OK)

        assert not BookingChange.objects.filter(
            metadata__icontains=BookingChangedScenario.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT,
        ).exists()

    @responses.activate
    def test_pay_with_card_wrong_provider_business_adyen(self):
        self.mock_auth_success()

        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
            tokenized_pm_id=uuid.uuid4(),
        )

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'payment_method': card.id,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(resp.json['errors']), 1)
        self.assertEqual(resp.json['errors'][0]['code'], 'wrong_payment_provider')

    @responses.activate
    def test_pay_with_card_wrong_provider_business_stripe(self):
        self.business.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.business.pos.save()

        baker.make(
            StripeAccount,
            pos=self.business.pos,
            kyc_verified_at_least_once=True,
            external_id='123',
        )

        self.mock_auth_success()

        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
            tokenized_pm_id=uuid.uuid4(),
        )

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'payment_method': card.id,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(resp.json['errors']), 1)
        self.assertEqual(resp.json['errors'][0]['code'], 'wrong_payment_provider')

    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_pay_with_both_methods_google(self, tokenized_mock):
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: True,
            CARD_TYPE__APPLE_PAY: False,
        }

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': CARD_TYPE__GOOGLE_PAY,
                'token': '123123123',
            },
            'payment_method': card.id,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_pay_with_both_methods_apple(self, tokenized_mock):
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: False,
            CARD_TYPE__APPLE_PAY: True,
        }

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': CARD_TYPE__APPLE_PAY,
                'token': '123123123',
            },
            'payment_method': card.id,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

    @responses.activate
    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_pay_with_google_pay(self, tokenized_mock):
        self.mock_auth_success()
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: True,
            CARD_TYPE__APPLE_PAY: False,
        }

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': CARD_TYPE__GOOGLE_PAY,
                'token': '123123123',
            },
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200

        assert not BookingChange.objects.filter(
            metadata__icontains=BookingChangedScenario.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT,
        ).exists()

    @responses.activate
    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_pay_with_apple_pay(self, tokenized_mock):
        self.mock_auth_success()
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: False,
            CARD_TYPE__APPLE_PAY: True,
        }

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': 'apple_pay',
                'token': '123123123',
            },
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200

        assert not BookingChange.objects.filter(
            metadata__icontains=BookingChangedScenario.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT,
        ).exists()

    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_external_payment_method_serialzer(self, tokenized_mock):
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: True,
            CARD_TYPE__APPLE_PAY: True,
        }

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': CARD_TYPE__GOOGLE_PAY,
                'token': '123123123',
            },
            'row_id': self.payment_row.id,
        }

        #
        serializer = CustomerTransactionActionRequest(
            data=body,
            instance=self.txn,
            context={
                'user': self.user,
            },
        )

        serializer.is_valid()

        assert serializer.is_valid()

        data = serializer.validated_data
        assert isinstance(data['external_payment_method'], PaymentMethod)
        assert data['external_payment_method'].card_type == (CARD_TYPE__GOOGLE_PAY)
        assert data['external_payment_method'].provider == 'adyen_ee'

    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_validate_pba_amount(self, tokenized_mock):
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: True,
        }

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': CARD_TYPE__GOOGLE_PAY,
                'token': '123123123',
            },
            'row_id': self.payment_row.id,
        }

        serializer = CustomerTransactionActionRequest(
            data=body,
            instance=self.txn,
            context={
                'user': self.user,
            },
        )

        serializer.is_valid()

        assert serializer.is_valid()

    @patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_validate_pba_amount_for_zero_value(self, tokenized_mock):
        self.payment_row.amount = 0
        self.payment_row.save()
        tokenized_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: True,
        }

        body = {
            'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
            'external_payment_method': {
                'partner': CARD_TYPE__GOOGLE_PAY,
                'token': '123123123',
            },
            'row_id': self.payment_row.id,
        }

        serializer = CustomerTransactionActionRequest(
            data=body,
            instance=self.txn,
            context={
                'user': self.user,
            },
        )

        serializer.is_valid()

        assert not serializer.is_valid()
        assert serializer.errors['payment_rows'][0].code == 'pay_by_app_minimal_amount'


class CustomerTransactionActionHandlerPendingPaymentTestCase(
    CustomerTransactionActionHandlerBaseTestCase,
):
    def setUp(self):
        super().setUp()
        receipt = baker.make(
            Receipt,
            transaction=self.txn,
            status_code=receipt_status.CALL_FOR_PREPAYMENT,
            payment_type=self.pba,
            already_paid=0,
        )
        self.txn.latest_receipt = receipt
        self.txn.save()

        baker.make(
            TransactionTip,
            transaction=self.txn,
            rate=0,
            amount=0,
            type=SimpleTip.TIP_TYPE__PERCENT,
        )

        self.payment_row = PaymentRow.create_with_status(
            receipt=receipt,
            status=receipt_status.CALL_FOR_PREPAYMENT,
            payment_type=self.pba,
            amount=self.txn.total,
        )
        self.payment_row.save()

    def test_set_tip_new_format(self):
        body = {
            'action': enums.CUSTOMER_ACTION__SET_TIP_RATE,
            'tip': {
                'rate': 230,
                'type': 'H',
            },
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200
        data = resp.json['transaction']
        assert data['tip']['rate'] == '230.00'
        assert data['tip']['type'] == 'H'

    def test_set_tip_old_format(self):
        body = {
            'action': enums.CUSTOMER_ACTION__SET_TIP_RATE,
            'tip_rate': 20,
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_set_tip_both_format(self):
        body = {
            'action': enums.CUSTOMER_ACTION__SET_TIP_RATE,
            'tip_rate': 230,
            'tip': {
                'rate': 230,
                'type': 'H',
            },
            'row_id': self.payment_row.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200
        data = resp.json['transaction']
        assert data['tip']['rate'] == '230.00'
        assert data['tip']['type'] == 'H'

        assert 'booking' in data.keys()
        assert 'multibooking' in data.keys()
        assert data['appointment_uid'] == self.booking.appointment_id
