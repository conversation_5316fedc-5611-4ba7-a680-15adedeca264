# pylint: disable=too-many-lines,too-many-statements
import datetime
import functools
import time
import uuid
from datetime import timedelta
from decimal import Decimal

import pytest
import pytz
import responses
from dateutil import tz
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils.translation import gettext as _
from freezegun import freeze_time
from mock import patch
from mock.mock import MagicMock
from model_bakery import baker
from rest_framework import status
from segment.analytics import Client

from lib.feature_flag.feature import (
    DisableBGCStatusChangeIfFailedPayment,
    LoyaltyProgramFlag,
    TurnTrackerFlag,
)
from lib.payment_providers.entities import PortResponse
from lib.payment_providers.enums import ResponseEntityType
from lib.payment_providers.mocks import (
    get_expires_soon_tokenized_payment_method_mock,
    get_invalid_tokenized_payment_method_mock,
    get_valid_tokenized_payment_method_mock,
)
from lib.test_utils import create_subbooking, increase_appointment_next_id, spy_mock
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from lib.tools import tznow
from service.pos.business_transactions import (
    BusinessTransactionSeriesDetailsHandler,
    BusinessTransactionsHandler,
)
from service.pos.tests import create_receipt
from service.pos.tests.common import POSTestsMixin
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import service_recipe, service_variant_recipe
from webapps.business.enums import ComboType, PriceType
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceAddOn,
    ServiceAddOnUse,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.service_promotions import update_subbooking_surcharge
from webapps.experiment_v3.models import ExperimentSlot
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.invoicing.models import (
    Buyer,
    CustomerInvoice,
    Seller,
)
from webapps.invoicing.tests.common import (
    french_certification_enabled,
)
from webapps.market_pay.models import AccountHolder
from webapps.notification.base import Channel
from webapps.notification.enums import DeeplinkFeature, NotificationTarget
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import StripeAccountHolder
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status as rs,
)
from webapps.pos.models import (
    POS,
    POSPlan,
    PaymentMethod,
    PaymentRow,
    PaymentType,
    Receipt,
    TaxRate,
    Tip,
    Transaction,
    TransactionRow,
    TransactionTip,
)
from webapps.pos.provider.fake import _CARDS
from webapps.pos.serializers import PaymentRowsSummarySerializer
from webapps.pos.tip_calculations import SimpleTip, UNASSIGNED_STAFFER_ID
from webapps.premium_services.public import (
    SubBookingSurchargeData,
    SubBookingSurchargeRepository,
    SurchargeType,
)
from webapps.segment.utils import assert_events_triggered
from webapps.sequencing_number.enums import SALES_DOCUMENT
from webapps.sequencing_number.models import SequenceRecord
from webapps.stripe_app.models import PaymentIntent
from webapps.stripe_integration.enums import StripePaymentIntentMetadata
from webapps.stripe_integration.models import StripeAccount
from webapps.turntracker.baker_recipes import tt_settings_on_recipe
from webapps.turntracker.enums import FinishTurnSource
from webapps.turntracker.models import FinishTurn
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User
from webapps.voucher.models import Voucher, VoucherServiceVariant, VoucherTemplate
from webapps.warehouse.models import (
    Commodity,
    CommodityStockLevel,
    Supply,
    SupplyRow,
    Warehouse,
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseDocumentType,
    WarehouseFormula,
    WarehouseFormulaRow,
)


class BusinessTransactionsHandlerBaseTestClass(POSTestsMixin, BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'


@pytest.mark.django_db
class BusinessTransactionsHandlerAddonsTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=self.default_tax_rate,
            description="",
        )
        self.stock = baker.make(
            CommodityStockLevel,
            warehouse=self.warehouse,
            commodity=self.commodity,
            remaining_volume=10,
        )
        ############ create some unused data #############
        unused_business = baker.make(
            Business,
            name='unused',
        )
        unused_service = baker.make(Service, business=unused_business)
        # unused addon:
        baker.make(
            ServiceAddOn,
            name='unused addon',
            business=unused_business,
            price=12.50,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[unused_service],
        )

        ######### used data #############################
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        # business_with_staffer_mock.return_value = self.business
        service_1 = baker.make(Service, business=self.business)
        service_2 = baker.make(Service, business=self.business)

        service_variant = baker.make(
            ServiceVariant,
            service=service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        service_variant_2 = baker.make(
            ServiceVariant,
            service=service_2,
            duration=relativedelta(minutes=15),
            price=100,
            type=PriceType.FIXED,
        )
        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        booked_from2 = datetime.datetime(2019, 1, 1, 11, 0, tzinfo=pytz.UTC)

        booking_1, self.booking_2 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant,
                },
                {
                    'booked_from': booked_from2,
                    'booked_till': booked_from2 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant_2,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings

        addon_1 = baker.make(
            ServiceAddOn,
            name='some addon 1',
            business=self.business,
            price=12.50,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[service_variant.service],
        )
        self.addon_1_1 = baker.make(
            ServiceAddOn,
            name='some addon 1_1',
            business=self.business,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[],
        )
        addon_2 = baker.make(
            ServiceAddOn,
            name='some addon 2',
            business=self.business,
            price=33.00,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[service_variant_2.service],
        )

        # addon_use_1
        baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=booking_1,
            service_addon=addon_1,
            price=12.50,
            quantity=1,
            services_ids=[service_variant.service.id],
        )
        addon_use_2 = baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=booking_1,
            service_addon=addon_2,
            price=33.00,
            quantity=2,
            services_ids=[service_variant_2.service.id],
        )
        # addon_use_3
        baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=self.booking_2,
            service_addon=addon_2,
            price=33.00,
            quantity=2,
            services_ids=[service_variant_2.service.id],
        )
        addon_use_4 = baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=self.booking_2,
            service_addon=addon_1,
            price=33.00,
            quantity=2,
            services_ids=[service_variant_2.service.id],
        )

        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]

        # create transaction
        self.body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                # crate Tr.Row for booking_1 and Tr.Row for Addon, which is in booking
                {
                    'booking_id': booking_1.id,
                },
                {
                    'booking_id': self.booking_2.id,
                },
                # add extra service_variant in Checkout; should be created Tr.Row with ServiceV...
                {
                    'service_variant_id': service_variant_2.id,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [],
            'addons': [
                # add extra ServiceAddOn; should be created Tr.Row with ServiceAddOn
                {
                    'service_addon_id': self.addon_1_1.id,
                    'quantity': 3,
                    'discount_rate': 3,
                    'commission_staffer_id': self.staffers[1].id,
                },
                # correct quantity and price of Addon in `booking_1`
                {
                    'service_addon_use_id': addon_use_2.id,
                    'item_price': 30,
                    'quantity': 3,
                    'commission_staffer_id': self.staffers[1].id,
                },
                # remove Addon from Tr.rows, but still keep it in self.booking_2
                {
                    'service_addon_use_id': addon_use_4.id,
                    'quantity': 0,
                },
            ],
        }

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_addons(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business

        resp = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        # 4 addons + 2 subbokings + 1 service variant == 7 tr. rows
        self.assertEqual(len(resp.json['transaction']['rows']), 7)
        self.assertEqual(
            len([row for row in resp.json['transaction']['rows'] if row['addon_use']]), 4
        )
        self.assertEqual(resp.json['transaction']['total_unformatted'], '444.15')
        self.assertEqual(
            sum(row['total'] for row in resp.json['transaction']['rows'] if row['addon_use']), 168.5
        )
        self.assertListEqual(
            [bool(i['addon_use']) for i in resp.json['transaction']['rows']],
            [False, True, True, False, True, False, True],
        )
        self.booking_2.refresh_from_db()
        booking_2_addons = self.booking_2.addons_set.all().values('quantity', 'price')
        self.assertCountEqual(
            booking_2_addons,
            [
                {'quantity': 2, 'price': Decimal('33.00')},
                {'quantity': 2, 'price': Decimal('33.00')},
            ],
        )

        commission_staffers = [
            row['commission_staffer_id']
            for row in resp.json['transaction']['rows']
            if row['addon_use']
        ]
        self.assertListEqual(
            commission_staffers,
            [
                self.staffers[0].id,
                self.staffers[1].id,
                self.staffers[0].id,
                self.staffers[1].id,
            ],
        )

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_addons_dry_run(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/dry_run/'

        resp = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        # 4 addons + 2 subbokings + 1 service variant == 7 tr. rows
        self.assertEqual(len(resp.json['transaction']['rows']), 7)
        self.assertEqual(
            len([row for row in resp.json['transaction']['rows'] if row['addon_use']]), 3
        )
        self.assertEqual(resp.json['transaction']['total_unformatted'], '444.15')
        self.assertEqual(
            sum(row['total'] for row in resp.json['transaction']['rows'] if row['addon_use']), 168.5
        )
        self.assertListEqual(
            [bool(i['addon_use']) for i in resp.json['transaction']['rows']],
            [False, True, True, False, True, False, False],
        )
        self.assertEqual(resp.json['transaction']['rows'][-1]['addon'], self.addon_1_1.id)

        self.booking_2.refresh_from_db()
        booking_2_addons = self.booking_2.addons_set.all().values('quantity', 'price')
        self.assertCountEqual(
            booking_2_addons,
            [
                {'quantity': 2, 'price': Decimal('33.00')},
                {'quantity': 2, 'price': Decimal('33.00')},
            ],
        )

        commission_staffers = [
            row['commission_staffer_id']
            for row in resp.json['transaction']['rows']
            if row['addon_use']
        ]
        self.assertListEqual(
            commission_staffers,
            [
                self.staffers[0].id,
                self.staffers[1].id,
                self.staffers[0].id,
            ],
        )


@pytest.mark.django_db
class BusinessTransactionsHandlerPeakHoursTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=Decimal('10.00'))
        self.create_cash_payment_type(pos)

        service_variant_1 = baker.make(
            ServiceVariant,
            service__business=self.business,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        service_variant_2 = baker.make(
            ServiceVariant,
            service__business=self.business,
            duration=relativedelta(minutes=15),
            price=100,
            type=PriceType.FIXED,
        )

        self.appointment = create_appointment(
            [
                {
                    'service_variant': service_variant_1,
                },
                {
                    'service_variant': service_variant_2,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        )

        for subbooking in self.appointment.subbookings:
            amount = subbooking.service_data.service_variant_price * Decimal('0.20')
            update_subbooking_surcharge(
                subbooking,
                SubBookingSurchargeData(
                    surcharge_type=SurchargeType.PREMIUM_HOURS.value,
                    rate=Decimal('20'),
                    amount=amount,
                    _resolved_price=subbooking.service_data.service_variant_price + amount,
                ),
            )
            SubBookingSurchargeRepository().update_for_subbookings(
                {
                    subbooking.id: subbooking.surcharge,
                }
            )
            subbooking.save(override=True)

        self.body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': subbooking.id,
                }
                for subbooking in self.appointment.subbookings
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [],
            'addons': [],
        }

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_peak_hours_dry_run(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/dry_run/'

        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertEqual(len(response.json['transaction']['rows']), 2)
        self.assertEqual(response.json['transaction']['rows'][0]['item_price'], 150)
        self.assertEqual(response.json['transaction']['rows'][1]['item_price'], 120)
        self.assertEqual(response.json['transaction']['total_unformatted'], '270.00')

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_peak_hours(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business

        response = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertEqual(len(response.json['transaction']['rows']), 2)
        self.assertEqual(response.json['transaction']['rows'][0]['item_price'], 150)
        self.assertEqual(response.json['transaction']['rows'][1]['item_price'], 120)
        self.assertEqual(response.json['transaction']['total_unformatted'], '270.00')


@pytest.mark.django_db
class BusinessTransactionsHandlerFinishTurnsTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)

        pos = baker.make(POS, business=self.business, active=True)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        service_1 = baker.make(Service, business=self.business)
        service_2 = baker.make(Service, business=self.business)

        service_variant = baker.make(
            ServiceVariant,
            service=service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        service_variant_2 = baker.make(
            ServiceVariant,
            service=service_2,
            duration=relativedelta(minutes=15),
            price=100,
            type=PriceType.FIXED,
        )
        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        booked_from2 = datetime.datetime(2019, 1, 1, 11, 0, tzinfo=pytz.UTC)

        self.booking_1, self.booking_2 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant,
                },
                {
                    'booked_from': booked_from2,
                    'booked_till': booked_from2 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant_2,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings

        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]

        # create transaction
        self.body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking_1.id,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [],
            'from_turntracker': True,
        }

    @override_eppo_feature_flag({TurnTrackerFlag.flag_name: True})
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_finish_turns_for_turntracker_checkout(self, business_with_staffer_mock):
        tt_settings_on_recipe.make(business_id=self.business.id)
        business_with_staffer_mock.return_value = self.business

        response = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertTrue(
            FinishTurn.objects.filter(
                business_id=self.business.id, source=FinishTurnSource.CHECKOUT
            ).exists()
        )

    @override_eppo_feature_flag({TurnTrackerFlag.flag_name: True})
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_finish_turns_for_turntracker_checkout_with_combo(
        self, business_with_staffer_mock
    ):
        tt_settings_on_recipe.make(business_id=self.business.id)
        child1 = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            duration=relativedelta(minutes=10),
        )
        child2 = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            duration=relativedelta(minutes=20),
        )
        combo = service_variant_recipe.make(
            service=service_recipe.make(combo_type=ComboType.SEQUENCE, business=self.business),
        )
        baker.make(
            'business.ComboMembership', combo=combo, child=child1, order=1, gap_time=relativedelta()
        )
        baker.make(
            'business.ComboMembership', combo=combo, child=child2, order=2, gap_time=relativedelta()
        )
        combo_booking = create_appointment(
            [
                {
                    'booked_from': tznow() - timedelta(hours=3),
                    'service_variant': combo,
                    'combo_children': [
                        {
                            'service_variant': child1,
                            'booked_from': tznow() - timedelta(hours=2),
                        },
                        {
                            'service_variant': child2,
                            'booked_from': tznow() - timedelta(hours=1),
                        },
                    ],
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings[0]
        business_with_staffer_mock.return_value = self.business

        self.body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': combo_booking.combo_children[0].id,
                },
                {
                    'booking_id': combo_booking.combo_children[1].id,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [],
            'from_turntracker': True,
        }

        response = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertTrue(
            FinishTurn.objects.filter(
                business_id=self.business.id,
                subbooking_id=combo_booking.combo_children[0].id,
                source=FinishTurnSource.CHECKOUT,
            ).exists()
        )
        self.assertTrue(
            FinishTurn.objects.filter(
                business_id=self.business.id,
                subbooking_id=combo_booking.combo_children[1].id,
                source=FinishTurnSource.CHECKOUT,
            ).exists()
        )


# pylint: disable=too-many-instance-attributes
@pytest.mark.django_db
class BusinessTransactionsHandlerRowHashesTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=self.default_tax_rate,
            description="",
        )

        self.pos = baker.make(POS, business=self.business, active=True)
        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=self.pos)
        self.create_cash_payment_type(self.pos)

        self.service_1 = baker.make(Service, business=self.business)
        self.service_2 = baker.make(Service, business=self.business)

        self.service_variant_1 = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        self.service_variant_2 = baker.make(
            ServiceVariant,
            service=self.service_2,
            duration=relativedelta(minutes=15),
            price=100,
            type=PriceType.FIXED,
        )
        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        booked_from2 = datetime.datetime(2019, 1, 1, 11, 0, tzinfo=pytz.UTC)

        self.booking_1, self.booking_2 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': self.service_variant_1,
                },
                {
                    'booked_from': booked_from2,
                    'booked_till': booked_from2 + datetime.timedelta(minutes=15),
                    'service_variant': self.service_variant_2,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings

        self.addon_1 = baker.make(
            ServiceAddOn,
            name='some addon 1',
            business=self.business,
            price=12.50,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[self.service_variant_1.service],
        )
        self.addon_2 = baker.make(
            ServiceAddOn,
            name='some addon 2',
            business=self.business,
            price=33.00,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[self.service_variant_2.service],
        )
        self.addon_use_1 = baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=self.booking_1,
            service_addon=self.addon_1,
            price=12.50,
            quantity=1,
            services_ids=[self.service_variant_1.service.id],
        )
        self.addon_use_2 = baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=self.booking_1,
            service_addon=self.addon_2,
            price=33.00,
            quantity=2,
            services_ids=[self.service_variant_2.service.id],
        )
        self.addon_use_3 = baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=self.booking_2,
            service_addon=self.addon_2,
            price=33.00,
            quantity=2,
            services_ids=[self.service_variant_2.service.id],
        )

        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]

        # create transaction
        self.body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                # Crate Tr.Row for bookings and addons used in bookings.
                {
                    'booking_id': self.booking_1.id,
                    'row_hash_uuid': 'ed64aece6b724aa5823d8b292ddd3e13',
                },
                {
                    'booking_id': self.booking_2.id,
                    'row_hash_uuid': 'ed64aece6b724aa5823d8b292ddd3e14',
                },
                # Add extra service_variant in Checkout.
                {
                    'service_variant_id': self.service_variant_2.id,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [],
            'addons': [
                # Already added addons
                {
                    'service_addon_use_id': self.addon_use_1.id,
                    'quantity': 3,
                    'discount_rate': 3,
                    'commission_staffer_id': self.staffers[1].id,
                    'row_hash_uuid': 'ed64aece6b724aa5823d8b292ddd3e12',
                },
                {
                    'service_addon_use_id': self.addon_use_2.id,
                    'item_price': 30,
                    'quantity': 3,
                    'commission_staffer_id': self.staffers[1].id,
                    'row_hash_uuid': '832f2ea169594574b69a70746f09c0e6',
                },
                {
                    'service_addon_use_id': self.addon_use_3.id,
                    'item_price': 10,
                    'quantity': 1,
                    'commission_staffer_id': self.staffers[1].id,
                    'row_hash_uuid': '832f2ea169594574b69a70746f09c0e6',
                },
            ],
        }

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_row_hashes(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business

        resp = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        # 3 addons + 2 subbokings + 1 service variant == 6 tr. rows
        self.assertEqual(len(resp.json['transaction']['rows']), 6)

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_row_hashes_dry_run(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/dry_run/'

        resp = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        # 3 addons + 2 subbokings + 1 service variant == 6 tr. rows
        tr_rows = resp.json['transaction']['rows']
        self.assertEqual(len(resp.json['transaction']['rows']), 6)
        old_row_hashes = [row['row_hash_uuid'] for row in tr_rows]
        old_row_hashes_iter = iter(old_row_hashes)

        # Generation of a new payload in dry_run with an additional product
        self.body['bookings'] = [
            {'booking_id': self.booking_1.id, 'row_hash_uuid': next(old_row_hashes_iter)},
            {'booking_id': self.booking_2.id, 'row_hash_uuid': next(old_row_hashes_iter)},
            {
                'service_variant_id': self.service_variant_2.id,
                'row_hash_uuid': next(old_row_hashes_iter),
            },
        ]
        self.body['addons'] = [
            {
                'service_addon_use_id': self.addon_use_1.id,
                'quantity': 3,
                'discount_rate': 3,
                'commission_staffer_id': self.staffers[1].id,
                'row_hash_uuid': next(old_row_hashes_iter),
            },
            {
                'service_addon_use_id': self.addon_use_2.id,
                'item_price': 30,
                'quantity': 3,
                'commission_staffer_id': self.staffers[1].id,
                'row_hash_uuid': next(old_row_hashes_iter),
            },
            {
                'service_addon_use_id': self.addon_use_3.id,
                'item_price': 10,
                'quantity': 1,
                'commission_staffer_id': self.staffers[1].id,
                'row_hash_uuid': next(old_row_hashes_iter),
            },
        ]
        self.body['products'] = [{'product_id': self.commodity.id, 'warehouse': self.warehouse.id}]
        resp = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        # 3 addons + 2 subbokings + 1 service variant + 1 product == 7 tr. rows
        self.assertEqual(len(resp.json['transaction']['rows']), 7)
        self.assertListEqual(
            [(row['row_hash_uuid'] in old_row_hashes) for row in resp.json['transaction']['rows']],
            [True, True, True, True, True, True, False],
        )


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class BusinessTransactionsHandlerTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=self.default_tax_rate,
            description="",
        )
        self.stock = baker.make(
            CommodityStockLevel,
            warehouse=self.warehouse,
            commodity=self.commodity,
            remaining_volume=10,
        )

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_issued_payments(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business

        bookings = [
            create_subbooking(
                business=self.business,
                booking_kws={
                    'type': Appointment.TYPE.BUSINESS,
                    'booked_from': tznow() - datetime.timedelta(hours=1),
                    'booked_till': tznow(),
                    'status': Appointment.STATUS.FINISHED,
                    'source': self.biz_booking_src,
                    'updated_by': self.user,
                },
            )[0]
            for _ in range(4)
        ]
        create_receipt(pos, bookings[0], rs.PAYMENT_FAILED)
        create_receipt(pos, bookings[1], rs.PAYMENT_CANCELED)
        create_receipt(pos, bookings[2], rs.PAYMENT_SUCCESS)

        url = (
            f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
            f'?transaction_type=sales&status_type=nav_issued'
        )

        resp = self.fetch(url)

        fetched_txns = [b['multibooking'] for b in resp.json['transactions']]

        assert resp.code == 200
        assert resp.json['count'] == 1
        assert bookings[0].appointment_id not in fetched_txns
        assert bookings[1].appointment_id not in fetched_txns
        assert bookings[2].appointment_id in fetched_txns
        assert bookings[3].appointment_id not in fetched_txns
        assert resp.json['transactions'][0]['appointment_uid'] == (
            resp.json['transactions'][0]['multibooking']
        )

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        new_transaction_id = resp.json['transaction']['id']

        new_business_document = SequenceRecord.objects.filter(
            business=self.business,
            type=SALES_DOCUMENT,
            related_document_id=resp.json['transaction']['id'],
        ).first()

        self.commodity.refresh_from_db()
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 9 == self.stock.full_packages_left
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['products'][0]['quantity'] = 2
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200

        edit_transaction_id = resp.json['transaction']['id']

        edited_business_document = SequenceRecord.objects.filter(
            business=self.business,
            type=SALES_DOCUMENT,
            related_document_id=resp.json['transaction']['id'],
        ).first()
        assert new_transaction_id != edit_transaction_id
        assert new_business_document.id == edited_business_document.id

        self.commodity.refresh_from_db()
        current_quantity = self.commodity.get_stock_level(
            self.warehouse,
        ).remaining_volume
        assert current_quantity == 8
        assert 'commissions_enabled' in resp.json['transaction']

    @override_feature_flag({LoyaltyProgramFlag: True})
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_post_transaction_with_loyalty_program(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)
        self.create_default_tax_rate_20(pos)
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_card_id': bci.id,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    "quantity": 1,
                    "item_price": 96,
                    "discount_rate": 0,
                    "commission_staffer_id": 46446,
                    "service_name": "Montant libre",
                    "row_hash_uuid": "f936e03c955642b0b84669861696e8aa",
                },
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        new_transaction_id = resp.json['transaction']['id']

        new_business_document = SequenceRecord.objects.filter(
            business=self.business,
            type=SALES_DOCUMENT,
            related_document_id=resp.json['transaction']['id'],
        ).first()

        self.commodity.refresh_from_db()
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 9 == self.stock.full_packages_left
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['products'][0]['quantity'] = 2
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200

        edit_transaction_id = resp.json['transaction']['id']

        edited_business_document = SequenceRecord.objects.filter(
            business=self.business,
            type=SALES_DOCUMENT,
            related_document_id=resp.json['transaction']['id'],
        ).first()
        assert new_transaction_id != edit_transaction_id
        assert new_business_document.id == edited_business_document.id

        self.commodity.refresh_from_db()
        current_quantity = self.commodity.get_stock_level(
            self.warehouse,
        ).remaining_volume
        assert current_quantity == 8
        assert 'commissions_enabled' in resp.json['transaction']

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes_product_consumption(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)
        warehouse = baker.make(Warehouse, business=self.business)

        business_with_staffer_mock.return_value = self.business
        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
        )
        commodity_used_0 = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=2,
        )
        commodity_used_1 = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=1,
        )

        booked_from = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        booking_1, _, _ = create_subbooking(
            business=self.business,
            booking_kws={
                'booked_from': booked_from,
                'booked_till': booked_from + datetime.timedelta(minutes=15),
                'status': Appointment.STATUS.ACCEPTED,
                'service_variant': service_variant,
            },
        )
        stock_0 = baker.make(
            CommodityStockLevel,
            warehouse=warehouse,
            commodity=commodity_used_0,
            remaining_volume=10,
        )
        stock_1 = baker.make(
            CommodityStockLevel,
            warehouse=warehouse,
            commodity=commodity_used_1,
            remaining_volume=20,
        )

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking_1.id,
                    'service_variant_id': booking_1.service_variant.id,
                    'quantity': 1,
                    'item_price': 30,
                    'discount_rate': 0,
                }
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [],
            'commodity_usage': [
                {
                    'commodity': commodity_used_0.id,
                    'count': 4,
                    'warehouse': warehouse.id,
                },
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        commodity_used_0.refresh_from_db()
        stock_0.refresh_from_db()
        assert stock_0.remaining_volume == 6
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        body['discount_rate'] = 20
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200

        commodity_used_0.refresh_from_db()
        stock_0.refresh_from_db()
        assert stock_0.remaining_volume == 6

        # edit transaction
        body['commodity_usage'] = [
            {
                'commodity': commodity_used_0.id,
                'count': 2,
                'warehouse': warehouse.id,
            },
            {
                'commodity': commodity_used_1.id,
                'count': 4,
                'warehouse': warehouse.id,
            },
        ]
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200

        commodity_used_0.refresh_from_db()
        stock_0.refresh_from_db()
        assert stock_0.remaining_volume == 8

        commodity_used_1.refresh_from_db()
        stock_1.refresh_from_db()
        assert stock_1.remaining_volume == 16

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes_no_product_at_warehouse_same(
        self,
        business_with_staffer_mock,
    ):
        self.stock.remaining_volume = 1
        self.stock.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        self.commodity.refresh_from_db()
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 0 == self.stock.full_packages_left
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['products'][0]['quantity'] = 2
        body['dry_run'] = True
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        txn_hash = resp.json['transaction']['txn_hash']
        body['txn_hash'] = txn_hash
        body['dry_run'] = False
        resp = self.fetch(url, method='PUT', body=body)
        assert 'is not available in the quantity of' in resp.json['errors'][0]['description']
        assert resp.code == 400

        body['products'][0]['quantity'] = 1  # as on receipt
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 0

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes_no_product_at_warehouse_less(
        self,
        business_with_staffer_mock,
    ):
        self.stock.remaining_volume = 1
        self.stock.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        self.commodity.refresh_from_db()
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 0 == self.stock.full_packages_left
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['products'][0]['quantity'] = 2
        body['dry_run'] = True
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        txn_hash = resp.json['transaction']['txn_hash']
        body['txn_hash'] = txn_hash
        body['dry_run'] = False
        resp = self.fetch(url, method='PUT', body=body)
        assert 'is not available in the quantity of' in resp.json['errors'][0]['description']
        assert resp.code == 400

        body['products'][0]['quantity'] = 0  # less than on receipt
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 1

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_no_product_at_warehouse_less_package_5(
        self,
        business_with_staffer_mock,
    ):
        commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product with big package',
            total_pack_capacity=5,
        )
        stock = baker.make(
            CommodityStockLevel,
            warehouse=self.warehouse,
            commodity=commodity,
            remaining_volume=10,  # 2 packages
        )
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        commodity.refresh_from_db()
        stock.refresh_from_db()
        assert stock.remaining_volume == 5
        assert stock.full_packages_left == 1
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['products'][0]['quantity'] = 3
        body['dry_run'] = True
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        txn_hash = resp.json['transaction']['txn_hash']
        body['txn_hash'] = txn_hash
        body['dry_run'] = False
        resp = self.fetch(url, method='PUT', body=body)
        assert 'is not available in the quantity of' in resp.json['errors'][0]['description']
        assert resp.code == 400

        body['products'][0]['quantity'] = 0  # less than on receipt
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        stock.refresh_from_db()
        assert stock.remaining_volume == 10

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes_for_imported_pos_product(
        self,
        business_with_staffer_mock,
    ):
        CommodityStockLevel.objects.filter(id=self.stock.id).update(
            history_change=[],
        )
        self.stock.refresh_from_db()
        assert not self.stock.history_change

        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        self.commodity.refresh_from_db()
        self.stock.refresh_from_db()
        assert self.stock.remaining_volume == 9 == self.stock.full_packages_left
        assert 'commissions_enabled' in resp.json['transaction']
        assert len(self.stock.history_change) == 2

        # single key change
        assert set(self.stock.history_change[-1]['change'].keys()).issuperset(
            {
                'remaining_volume',
            }
        )
        remaining_volume_change = self.stock.history_change[-1]['change']['remaining_volume']
        assert remaining_volume_change['new_value'] == 9
        assert remaining_volume_change['old_value'] == 10

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_stock_changes_commodity_control_enabled_locally(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 15,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        assert 'not available in the quantity' in resp.json['errors'][0]['description']

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_stock_changes_commodity_control_disabled_locally(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)
        self.commodity.enable_stock_control = False
        self.commodity.save()

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 15,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_stock_changes_commodity_control_disabled_globally(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=False,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 15,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_stock_changes_commodity_control_disabled_globally_and_locally(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=False,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)
        self.commodity.enable_stock_control = False
        self.commodity.save()

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 15,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes_with_empty_stock(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201, resp.json

        self.commodity.refresh_from_db()
        current_quantity = self.commodity.get_stock_level(
            self.warehouse,
        ).remaining_volume
        assert current_quantity == 9
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['tip_rate'] = 10
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200, resp.json

        self.commodity.refresh_from_db()
        current_quantity = self.commodity.get_stock_level(
            self.warehouse,
        ).remaining_volume
        assert current_quantity == 9
        assert 'commissions_enabled' in resp.json['transaction']

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_transaction_stock_changes_with_negative_stock(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        self.create_cash_payment_type(pos)

        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        self.commodity.refresh_from_db()
        current_quantity = self.commodity.get_stock_level(
            self.warehouse,
        ).remaining_volume
        assert current_quantity == 9
        assert 'commissions_enabled' in resp.json['transaction']

        # edit transaction
        self.stock.remaining_volume = 0
        self.stock.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{resp.json["transaction"]["id"]}'
        )
        body['tip_rate'] = 10
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200, resp.json

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_canceled_deposits(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business

        bkings = [
            create_subbooking(
                business=self.business,
                booking_kws={
                    'type': Appointment.TYPE.BUSINESS,
                    'booked_from': tznow() - datetime.timedelta(hours=1),
                    'booked_till': tznow(),
                    'status': Appointment.STATUS.FINISHED,
                    'source': self.biz_booking_src,
                    'updated_by': self.user,
                },
            )[0]
            for _ in range(6)
        ]

        make_receipt = functools.partial(
            create_receipt,
            pos=pos,
            txn_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        make_receipt(booking=bkings[0], status=rs.DEPOSIT_CANCEL_AWAITING)
        make_receipt(booking=bkings[1], status=rs.DEPOSIT_CANCEL_FAILED)
        make_receipt(booking=bkings[2], status=rs.DEPOSIT_CHARGE_CANCELED)
        make_receipt(booking=bkings[3], status=rs.DEPOSIT_CHARGE_FAILED)
        make_receipt(booking=bkings[4], status=rs.DEPOSIT_AUTHORISATION_FAILED)
        make_receipt(booking=bkings[5], status=rs.PAYMENT_CANCELED)

        url = (
            f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
            f'?transaction_type=deposits&status_type=nav_canceled'
        )

        resp = self.fetch(url)

        fetched_ids = [b['multibooking'] for b in resp.json['transactions']]

        assert resp.code == 200
        assert resp.json['count'] == 3
        assert bkings[0].appointment_id not in fetched_ids
        assert bkings[1].appointment_id in fetched_ids
        assert bkings[2].appointment_id in fetched_ids
        assert bkings[3].appointment_id not in fetched_ids
        assert bkings[4].appointment_id not in fetched_ids
        assert bkings[5].appointment_id in fetched_ids

    def test_put_for_booksy_gift_card_partially_paid_appointment_going_to_succeed(self):
        customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
            email="<EMAIL>",
        )
        baker.make(Buyer, customer=customer)
        pos = baker.make(POS, business=self.business, active=True)
        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
        )
        booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'source': self.biz_booking_src,
                'updated_by': self.user,
                'service_variant': service_variant,
            },
        )[0]
        payment_type = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.SPLIT)
        booksy_gift_card = baker.make(
            PaymentType,
            pos=pos,
            code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
        )
        transaction = baker.make(
            Transaction,
            appointment_id=booking.appointment_id,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=100,
        )
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{transaction.id}'

        bgc_receipt = baker.make(
            Receipt,
            transaction=transaction,
            payment_type=payment_type,
            receipt_number='AAA/123',
            status_code=rs.GIFT_CARD_DEPOSIT,
        )
        payment_row_bgc = baker.make(
            PaymentRow,
            amount=20,
            receipt=bgc_receipt,
            payment_type=booksy_gift_card,
            status=rs.GIFT_CARD_DEPOSIT,
        )

        transaction.latest_receipt = bgc_receipt
        transaction.save()
        body = {
            'payment_type_code': payment_type.code,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'multibooking': booking.appointment_id,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'service_variant_id': booking.service_variant.id,
                    'service_name': 'mohawk',
                    'quantity': 1,
                    'item_price': 100,
                    'discount_rate': 0,
                }
            ],
            'payment_rows': [
                {
                    'amount': payment_row_bgc.amount,
                    'id': payment_row_bgc.id,
                    'locked': payment_row_bgc.locked,
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'service_amount': "20.00",
                },
                {
                    'amount': "80.00",
                    'payment_type_code': PaymentTypeEnum.CASH,
                },
            ],
            'id': transaction.id,
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [],
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        new_transaction = Transaction.objects.get(id=resp.json['transaction']['id'])
        bgc_payment = new_transaction.payment_rows.get(
            payment_type__code=PaymentTypeEnum.BOOKSY_GIFT_CARD
        )

        assert new_transaction.latest_receipt.status_code == rs.PAYMENT_SUCCESS
        assert bgc_payment.status == rs.PAYMENT_SUCCESS

    def _test_put_for_booksy_gift_card_partially_paid_base(self):
        customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='+***********',
            user=self.user,
        )
        baker.make(Buyer, customer=customer)
        pos = baker.make(POS, business=self.business, active=True)
        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
        )

        booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'source': self.biz_booking_src,
                'updated_by': self.user,
                'service_variant': service_variant,
            },
        )[0]

        payment_type = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.SPLIT)
        booksy_gift_card = baker.make(
            PaymentType,
            pos=pos,
            code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
        )
        transaction = baker.make(
            Transaction,
            appointment_id=booking.appointment_id,
            pos=pos,
            customer_card=customer,
            customer=self.user,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=100,
        )
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{transaction.id}'

        bgc_receipt = baker.make(
            Receipt,
            transaction=transaction,
            payment_type=payment_type,
            receipt_number='AAA/123',
            status_code=rs.GIFT_CARD_DEPOSIT,
        )
        payment_row_bgc = baker.make(
            PaymentRow,
            amount=20,
            receipt=bgc_receipt,
            payment_type=booksy_gift_card,
            status=rs.GIFT_CARD_DEPOSIT,
        )

        transaction.latest_receipt = bgc_receipt
        transaction.save()
        body = {
            'payment_type_code': payment_type.code,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'multibooking': booking.appointment_id,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'service_variant_id': booking.service_variant.id,
                    'service_name': 'mohawk',
                    'quantity': 1,
                    'item_price': 100,
                    'discount_rate': 0,
                }
            ],
            'payment_rows': [
                {
                    'amount': payment_row_bgc.amount,
                    'id': payment_row_bgc.id,
                    'locked': payment_row_bgc.locked,
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'service_amount': "20.00",
                },
                {
                    'amount': "80.00",
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                },
            ],
            'id': transaction.id,
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [],
            'customer_card_id': customer.id,
            'customer_id': None,
        }

        resp = self.fetch(url, method='PUT', body=body)
        return resp

    @override_eppo_feature_flag({DisableBGCStatusChangeIfFailedPayment.flag_name: False})
    def test_put_for_booksy_gift_card_partially_paid_appointment_going_to_call_for_payment_flag_off(
        self,
    ):  # pylint: disable=line-too-long
        resp = self._test_put_for_booksy_gift_card_partially_paid_base()
        assert resp.code == 200
        new_transaction = Transaction.objects.get(id=resp.json['transaction']['id'])
        bgc_payment = new_transaction.payment_rows.get(
            payment_type__code=PaymentTypeEnum.BOOKSY_GIFT_CARD
        )

        assert new_transaction.latest_receipt.status_code == rs.CALL_FOR_PAYMENT
        assert bgc_payment.status == rs.GIFT_CARD_DEPOSIT

    @override_eppo_feature_flag({DisableBGCStatusChangeIfFailedPayment.flag_name: True})
    def test_put_for_booksy_gift_card_partially_paid_appointment_going_to_call_for_payment_flag_on(
        self,
    ):  # pylint: disable=line-too-long
        resp = self._test_put_for_booksy_gift_card_partially_paid_base()
        assert resp.code == 200
        new_transaction = Transaction.objects.get(id=resp.json['transaction']['id'])
        bgc_payment = new_transaction.payment_rows.get(
            payment_type__code=PaymentTypeEnum.BOOKSY_GIFT_CARD
        )

        assert new_transaction.latest_receipt.status_code == rs.CALL_FOR_PAYMENT
        assert bgc_payment.status == rs.PAYMENT_SUCCESS

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_double_save_transaction(
        self,
        analytics_track_mock,
        analytics_identify_mock,
        business_with_staffer_mock,
    ):
        """Two edit widows opened the same transaction. In one 'save' was
        clicked. In second we should get error while trying to save."""
        self.user.cell_phone = '+12345678'  # for analytics
        self.user.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )

        self.create_cash_payment_type(pos)
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        org_resp = self.fetch(self.url, method='POST', body=body)
        assert org_resp.code == 201, org_resp.json

        assert analytics_track_mock.call_count == 1
        assert analytics_identify_mock.call_count == 0
        assert_events_triggered(
            {
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                }
            },
            segment_track_mock=analytics_track_mock,
        )

        transactions = Transaction.objects.all().count()
        assert transactions == 1

        # save transaction
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{org_resp.json["transaction"]["id"]}'
        )
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        transactions = Transaction.objects.all().count()
        assert transactions == 2  # Transaction make copy, and archive old one

        # and again try to save the original one
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'pos/transactions/{org_resp.json["transaction"]["id"]}'
        )
        resp = self.fetch(url, method='PUT', body=body)

        transactions = Transaction.objects.all().count()
        assert transactions == 2
        assert resp.code == 400
        assert resp.json['errors'][0]['code'] == 'archived'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_killswitch_for_business_transaction_completed(
        self,
        analytics_track_mock,
        analytics_identify_mock,
        business_with_staffer_mock,
    ):
        from webapps.kill_switch.models import KillSwitch

        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.PAYMENT_TRANSACTION_COMPLETED,
            is_killed=True,
        )
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.CHECKOUT_TRANSACTION_COMPLETED,
            is_killed=True,
        )
        self.user.cell_phone = '+12345678'
        self.user.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )

        self.create_cash_payment_type(pos)
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        org_resp = self.fetch(self.url, method='POST', body=body)
        assert org_resp.code == 201, org_resp.json

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_prevent_multiple_post_requests(
        self,
        business_with_staffer_mock,
    ):
        """Multiple HTTP POST request caused duplicated transactions to be
        created. The BusinessTransactionsHandler request handler has a lock
        set up to prevent such situations. First POST request should create
        transaction and latter requests should not.
        """
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        self.create_cash_payment_type(pos)

        warehouse = baker.make(Warehouse, business=self.business, is_default=True)
        commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
            name='Test product',
            tax_rate=self.default_tax_rate,
        )
        baker.make(
            CommodityStockLevel,
            warehouse=warehouse,
            commodity=commodity,
            remaining_volume=10,
        )

        business_with_staffer_mock.return_value = self.business

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
        )

        booked_from = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        booking_1, _, _ = create_subbooking(
            business=self.business,
            booking_kws={
                'booked_from': booked_from,
                'booked_till': booked_from + datetime.timedelta(minutes=15),
                'status': Appointment.STATUS.ACCEPTED,
                'service_variant': service_variant,
            },
        )

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking_1.id,
                    'service_variant_id': booking_1.service_variant.id,
                    'quantity': 1,
                    'item_price': 30,
                    'discount_rate': 0,
                }
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                }
            ],
        }

        # We send the same HTTP POST request in a very short period of time
        # Second request should be blocked
        resp_1 = self.fetch(self.url, method='POST', body=body)
        resp_2 = self.fetch(self.url, method='POST', body=body)
        assert resp_1.code == 201
        assert resp_2.code == 409
        assert resp_2.json['errors'][0]['code'] == 'conflict'
        assert resp_2.json['errors'][0]['description'] == 'lock_error'

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_list_with_tips(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business

        bkings = [
            create_subbooking(
                business=self.business,
                booking_kws={
                    'type': Appointment.TYPE.BUSINESS,
                    'booked_from': tznow() - datetime.timedelta(hours=1),
                    'booked_till': tznow(),
                    'status': Appointment.STATUS.FINISHED,
                    'updated_by': self.user,
                    'source': self.biz_booking_src,
                },
            )[0]
            for _ in range(6)
        ]

        make_receipt = functools.partial(
            create_receipt,
            pos=pos,
            txn_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        make_receipt(booking=bkings[0], status=rs.DEPOSIT_CANCEL_AWAITING)
        make_receipt(booking=bkings[1], status=rs.DEPOSIT_CANCEL_FAILED)
        make_receipt(booking=bkings[2], status=rs.DEPOSIT_CHARGE_CANCELED)
        make_receipt(booking=bkings[3], status=rs.DEPOSIT_CHARGE_FAILED)
        make_receipt(booking=bkings[4], status=rs.DEPOSIT_AUTHORISATION_FAILED)
        make_receipt(booking=bkings[5], status=rs.PAYMENT_CANCELED)

        resp = self.fetch(self.url)

        assert resp.code == 200
        assert resp.json['count'] == 6

        for i in range(0, 6):
            assert resp.json['transactions'][i]['tip']['rate'] == '0.00'
            assert resp.json['transactions'][i]['tip']['type'] == 'P'

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_list_without_tips(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True, tips_enabled=False)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business

        bkings = [
            create_subbooking(
                business=self.business,
                booking_kws={
                    'type': Appointment.TYPE.BUSINESS,
                    'booked_from': tznow() - datetime.timedelta(hours=1),
                    'booked_till': tznow(),
                    'status': Appointment.STATUS.FINISHED,
                    'updated_by': self.user,
                    'source': self.biz_booking_src,
                },
            )[0]
            for _ in range(6)
        ]

        make_receipt = functools.partial(
            create_receipt,
            pos=pos,
            txn_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        make_receipt(booking=bkings[0], status=rs.DEPOSIT_CANCEL_AWAITING)
        make_receipt(booking=bkings[1], status=rs.DEPOSIT_CANCEL_FAILED)
        make_receipt(booking=bkings[2], status=rs.DEPOSIT_CHARGE_CANCELED)
        make_receipt(booking=bkings[3], status=rs.DEPOSIT_CHARGE_FAILED)
        make_receipt(booking=bkings[4], status=rs.DEPOSIT_AUTHORISATION_FAILED)
        make_receipt(booking=bkings[5], status=rs.PAYMENT_CANCELED)

        resp = self.fetch(self.url)

        assert resp.code == 200
        assert resp.json['count'] == 6

        for i in range(0, 6):
            assert 'tip' not in resp.json['transactions'][i]
            assert 'tip_rate' not in resp.json['transactions'][i]

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_payment_with_package(
        self,
        business_with_staffer_mock,
    ):
        package = self._package_test_setup()

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                },
            ],
            'commodity_usage': [
                {
                    'commodity': self.commodity.id,
                    'count': 4,
                    'warehouse': self.warehouse.id,
                },
            ],
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PACKAGE,
                    'voucher_id': package.id,
                    'voucher_service_id': self.variant.id,
                    'amount': 0,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': self.commodity.id,
                    'quantity': 1,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'commissions_enabled' in resp.json['transaction']

        # create transaction
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        # transaction successfull

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_payment_with_package_broken_request(
        self,
        business_with_staffer_mock,
    ):
        package = self._package_test_setup()

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                },
            ],
            'commodity_usage': [
                {
                    'id': self.commodity.id,
                    'count': 4,
                    'warehouse': self.warehouse.id,
                },
            ],
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PACKAGE,
                    'voucher_id': package.id,
                    'voucher_service_id': self.variant.id,
                    'amount': 0,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'commodity_usage.0.commodity',
                        'description': 'This field is required.',
                        'code': 'required',
                    }
                ],
            },
        )

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_payment_with_package_and_formula(
        self,
        business_with_staffer_mock,
    ):
        package = self._package_test_setup()

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=self.commodity,
                    count=3,
                ),
            ],
        )
        self.commodity.soft_delete()

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                },
            ],
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PACKAGE,
                    'voucher_id': package.id,
                    'voucher_service_id': self.variant.id,
                    'amount': 0,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 0,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'id' in (resp.json['transaction']['default_commodity_usage'][0]['commodity'])

        # create transaction
        body['dry_run'] = False
        body['commodity_usage'] = [
            {
                'commodity': self.commodity.id,
                'count': 4,
                'warehouse': self.warehouse.id,
            },
        ]

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        # transaction successfull
        self.stock.refresh_from_db()
        assert self.stock.total_packages_left == 6

    def _package_test_setup(self):
        pos = baker.make_recipe(
            'webapps.pos.pos_recipe',
            business=self.business,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        baker.make(
            PaymentType,
            code=PaymentTypeEnum.PACKAGE,
            pos=pos,
        )

        package_template = baker.make(
            VoucherTemplate,
            type=Voucher.VOUCHER_TYPE__PACKAGE,
            valid_till=VoucherTemplate.DAYS_30,
            pos=pos,
        )

        service = baker.make(Service, business=self.business)
        self.variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
        )
        self.booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'service_variant': self.variant,
                'source': self.biz_booking_src,
                'updated_by': self.user,
            },
        )[0]

        package = baker.make(
            Voucher,
            pos=pos,
            voucher_template=package_template,
            current_balance=1000,
            valid_from=tznow() + datetime.timedelta(days=1),
            valid_till=tznow() + datetime.timedelta(days=30),
            status=Voucher.ACTIVE,
        )
        baker.make(
            VoucherServiceVariant,
            voucher=package,
            service_variant=self.variant,
            amount=2,
            item_price=19.28,
        )
        return package

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_with_search_query(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True, tips_enabled=False)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business

        service = baker.make(Service, business=self.business, name='Service_A')
        service_variant = baker.make(
            ServiceVariant, service=service, duration=relativedelta(minutes=15)
        )

        booking_1 = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
            },
        )[0]
        booking_2 = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
                'service_name': 'Service_B',
            },
        )[0]

        make_receipt = functools.partial(
            create_receipt,
            pos=pos,
            txn_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            status=rs.PAYMENT_SUCCESS,
        )
        txn_1 = make_receipt(booking=booking_1)
        txn_1.customer_data = 'Jan Matejko, 1234567890, <EMAIL>'
        txn_1.save()

        baker.make(
            TransactionRow,
            transaction=txn_1,
            service_variant=service_variant,
        )
        baker.make(
            TransactionRow,
            transaction=txn_1,
            service_variant=service_variant,
        )

        txn_2 = make_receipt(booking=booking_2)
        txn_2.customer_data = 'Anna Kowalska, 987654321, <EMAIL>'
        txn_2.save()
        baker.make(
            TransactionRow,
            transaction=txn_2,
            subbooking=booking_2,
        )

        resp = self.fetch(self.url + '?query=Service_A')
        assert resp.code == 200, resp.json
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id in ids
        assert txn_2.id not in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=Service_B')
        assert resp.code == 200, resp.json
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id not in ids
        assert txn_2.id in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=Service')
        assert resp.code == 200, resp.json
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id in ids
        assert txn_2.id in ids
        assert len(ids) == 2

        resp = self.fetch(self.url + '?query=Jan', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id in ids
        assert txn_2.id not in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=Kowalska', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id not in ids
        assert txn_2.id in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=<EMAIL>', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id not in ids
        assert txn_2.id in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=1234567890', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [t['id'] for t in resp.json['transactions']]
        assert txn_1.id in ids
        assert txn_2.id not in ids
        assert len(ids) == 1

    @patch.object(Business, 'get_timezone')
    def test_get_hidden_user_data(
        self,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True, tips_enabled=False)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
            email="<EMAIL>",
        )

        service = baker.make(Service, business=self.business, name='Service_A')
        service_variant = baker.make(
            ServiceVariant, service=service, duration=relativedelta(minutes=15)
        )

        booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
            },
        )[0]

        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=pos,
        )

        txn = baker.make(
            Transaction,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
            customer_card=bci,
            customer_data='Jan Matejko, 1234567890, <EMAIL>',
            total=99.99,
        )
        baker.make(
            TransactionRow,
            transaction=txn,
            service_variant=service_variant,
        )

        receipt = baker.make(
            Receipt,
            status_code=rs.PAYMENT_SUCCESS,
            transaction=txn,
            already_paid=99.99,
        )

        baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            amount=100,
        )

        txn.latest_receipt = receipt
        txn.save()

        user_advanced = baker.make(User)
        baker.make(
            Resource,
            staff_user=user_advanced,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_ADVANCED,
        )
        self.session = user_advanced.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        resp = self.fetch(self.url)
        assert resp.code == 200, resp.json
        transaction = resp.json['transactions'][0]
        assert transaction['customer_info']['phone'] == 'LOCKED'
        assert transaction['customer_info']['email'] == 'LOCKED'
        assert '1234567890' not in transaction['customer_data']
        assert '<EMAIL>' not in transaction['customer_data']

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_edit_voucher_in_past(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)

        egift_template = baker.make(
            VoucherTemplate,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=VoucherTemplate.DAYS_30,
            pos=pos,
        )

        business_with_staffer_mock.return_value = self.business

        # dry-run create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'vouchers': [
                {
                    'voucher_template_id': egift_template.id,
                    'valid_from': (tznow() + relativedelta(days=6)).strftime('%Y-%m-%d'),
                    'item_price': 123,
                    'voucher_customer': None,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201

        new_transaction_id = resp.json['transaction']['id']
        with freeze_time(tznow() + relativedelta(days=settings.SESSION_AGE_DAYS - 1)):
            # edit transaction
            url = (
                f'/business_api/me/businesses/{self.business.id}/'
                f'pos/transactions/{new_transaction_id}'
            )
            resp = self.fetch(url, method='PUT', body=body)
            assert resp.code == status.HTTP_200_OK

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_bci_from_another_business(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            products_stock_enabled=True,
        )
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        bci_another_business = baker.make(BusinessCustomerInfo)

        self.create_cash_payment_type(pos)
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_card_id': bci_another_business.id,
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400, resp.json
        assert resp.json['errors'][0]['code'] == 'does_not_exist'

        time.sleep(2)  # because of lock
        body['customer_card_id'] = bci.id
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_discount_rate_exists_global_item_discounts_disabled(
        self,
        business_with_staffer_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            global_discount_enabled=False,
            item_discount_enabled=False,
        )
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
            },
        )[0]

        self.create_cash_payment_type(pos)
        business_with_staffer_mock.return_value = self.business
        baker.make(
            TaxRate,
            pos=pos,
            default_for_service=True,
            rate=20,
        )

        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_card_id': bci.id,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 200,
                    'discount_rate': 0,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        assert 'discount_rate' in resp.json['transaction']

    @french_certification_enabled(certification_enabled=True)
    def test_french_certification_business_without_seller(self):

        resp = self.fetch(self.url, method='POST', body={})

        self.assertEqual(resp.code, 400)
        self.assertListEqual(
            resp.json['errors'],
            [
                {
                    'code': 'missing_seller_data',
                    'type': 'validation',
                    'field': 'non_field_errors',
                    'description': _(
                        "You need to provide your tax info.\n"
                        "Please open Booksy in a browser and add missing information."
                    ),
                }
            ],
        )

    @french_certification_enabled(certification_enabled=True)
    def test_french_certification_business_with_missing_seller_fields(self):
        seller = fc_seller_recipe.make(business=self.business)
        seller.siret = None
        seller.naf_code_of_the_issuer = None
        seller.save()

        resp = self.fetch(self.url, method='POST', body={})

        self.assertEqual(resp.code, 400)
        field_error = resp.json['errors'][0].pop('field')
        self.assertListEqual(
            resp.json['errors'],
            [
                {
                    'code': 'missing_seller_data',
                    'type': 'validation',
                    'description': _(
                        "You need to provide your tax info.\n"
                        "Please open Booksy in a browser and add missing information."
                    ),
                }
            ],
        )
        self.assertSetEqual(
            {'siret', 'naf_code_of_the_issuer'}, set(field_error.replace(" ", "").split(","))
        )

    @french_certification_enabled(certification_enabled=True)
    def test_french_certification_business_with_correct_seller_data(self):
        fc_seller_recipe.make(business=self.business)
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=pos)
        self.create_cash_payment_type(pos)
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)

        self.assertEqual(resp.code, 201)


@pytest.mark.django_db
class BusinessTransactionsHandlerTipTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)
        self.tax_rate = baker.make(
            TaxRate,
            pos=self.pos,
            default_for_service=True,
            rate=20,
        )
        self.product = baker.make(
            Commodity,
            business=self.business,
        )
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
        )

        self.tip = baker.make(Tip, pos=self.pos, default=True, rate=61)

        service = baker.make(Service, business=self.business)
        self.variant = baker.make(ServiceVariant, duration='0100', service=service)

        self.booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'service_variant': self.variant,
                'source': self.biz_booking_src,
                'updated_by': self.user,
            },
        )[0]

        baker.make(PaymentType, code=PaymentTypeEnum.CHECK, pos=self.pos)
        self.payment_type_code = PaymentTypeEnum.CHECK
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        baker.make(Resource, staff_user=self.user, business=self.business)

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_tip_old_format(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip_rate': 5,
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        assert data['tip']['rate'] == '61.00'
        assert data['tip']['type'] == 'P'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_tip_new_format(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 5,
                'type': 'P',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        assert data['tip']['rate'] == '5.00'
        assert data['tip']['type'] == 'P'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_tip_new_format_rest_of(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 800,
                'type': 'R',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_tip_new_format_hand_of(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        assert data['tip']['rate'] == '600.00'
        assert data['tip']['type'] == 'H'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_tip_new_format_diff(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip_rate': 50,
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        assert data['tip']['rate'] == '600.00'
        assert data['tip']['type'] == 'H'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_formula(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        warehouse = baker.make(Warehouse, business=self.business)
        commodity_used = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
        )
        commodity_sold = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
            net_price=100,
            gross_price=123,
            tax=23,
            tax_rate=baker.make(
                TaxRate,
                pos=self.pos,
                rate=23.00,
            ),
        )

        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=23,
            gross_price=28.29,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_used,
            quantity=100,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=27,
            gross_price=33.21,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_sold,
            quantity=50,
        )

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=commodity_used,
                    count=3,
                ),
                baker.make(
                    WarehouseFormulaRow,
                    commodity=baker.make(Commodity),
                    count=8,
                ),
            ],
        )
        used_level = commodity_used.get_stock_level(warehouse)
        used_remaining_before = used_level.remaining_volume
        assert used_remaining_before
        sold_level = commodity_sold.get_stock_level(warehouse)
        sold_remaining_before = sold_level.remaining_volume
        assert sold_remaining_before

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                }
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [
                {
                    'commodity': commodity_used.id,
                    'count': 5,
                    'warehouse': warehouse.id,
                },
            ],
        }

        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        txn_hash = resp.json['transaction'].get('txn_hash')
        assert txn_hash
        body['txn_hash'] = txn_hash

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        assert commodity_usage[1]['commodity']['id']
        assert commodity_usage[1]['count'] == 8
        assert 'commodity_usage' in data
        assert data['rows'][1]['warehouse'] == warehouse.id
        assert data['rows'][1]['warehouse_name'] == warehouse.name

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        assert commodity_usage[1]['commodity']['id']
        assert commodity_usage[1]['count'] == 8
        commodity_usage = data['commodity_usage']
        assert len(commodity_usage) == 1
        reported_row = commodity_usage[0]
        assert reported_row['commodity']['id'] == commodity_used.id
        assert 'name' in reported_row['commodity']
        assert 'volume_unit' in reported_row['commodity']
        assert reported_row['count'] == 5
        assert reported_row['warehouse'] == warehouse.id
        usage_rows = WarehouseFormulaRow.objects.filter(
            transactions__id__in=[resp.json['transaction']['id']],
        )
        assert usage_rows
        assert usage_rows.count() == 1
        usage_row = usage_rows.first()
        assert usage_row.commodity == commodity_used
        assert usage_row.count == 5
        assert usage_row.warehouse == warehouse
        assert usage_row.transactions.all().count() == 1
        assert usage_row.transactions.first().id == resp.json['transaction']['id']
        rw_document_row = WarehouseDocumentRow.objects.get(
            commodity=commodity_used,
        )
        assert rw_document_row.commodity_name == commodity_used.name
        rw_document = rw_document_row.document
        used_level.refresh_from_db()
        used_remaining_after = used_level.remaining_volume
        assert used_remaining_after == used_remaining_before - Decimal(usage_row.count)
        assert rw_document.warehouse == warehouse
        assert rw_document.type == 'RW'
        assert rw_document.transaction.id == resp.json['transaction']['id']
        assert rw_document_row.net_price == 23
        assert float(rw_document_row.gross_price) == 28.29
        assert float(rw_document_row.tax) == 23
        wz_document_row = WarehouseDocumentRow.objects.get(
            commodity=commodity_sold,
        )
        sold_level.refresh_from_db()
        sold_remaining_after = sold_level.remaining_volume
        assert sold_remaining_after < sold_remaining_before
        wz_document = wz_document_row.document
        assert wz_document.warehouse == warehouse
        assert wz_document.type == 'WZ'
        assert wz_document.transaction.id == resp.json['transaction']['id']
        assert wz_document_row.net_price == Decimal('90.00')
        assert wz_document_row.commodity_name == commodity_sold.name
        assert wz_document_row.gross_price == Decimal('110.7')
        assert wz_document_row.tax == Decimal('20.70')
        assert wz_document.number == 'SR/1'
        assert rw_document.number == 'PE/1'

        assert (
            WarehouseDocument.objects.filter(
                warehouse__business=self.business,
                type='RW',
            ).count()
            == 1
        )

        time.sleep(2)  # because of lock
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            # no bookings
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                }
            ],
            'customer_card_id': self.bci.id,
            # no commodity_usage
        }
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        wz_document_2_row = WarehouseDocumentRow.objects.filter(
            commodity=commodity_sold,
        ).last()
        assert wz_document_2_row.document.number == 'SR/2'

        time.sleep(2)  # because of lock
        booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'service_variant': self.variant,
                'source': self.biz_booking_src,
                'updated_by': self.user,
            },
        )[0]

        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                }
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [
                {
                    'commodity': commodity_used.id,
                    'count': 5,
                    'warehouse': warehouse.id,
                },
            ],
        }
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        assert (
            WarehouseDocument.objects.filter(
                warehouse__business=self.business,
                type='RW',
            ).count()
            == 2
        )

        rw_document_2 = WarehouseDocument.objects.get(
            transaction__id=resp.json['transaction']['id'],
            type=WarehouseDocumentType.RW,
        )
        assert rw_document_2.number == 'PE/2'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_formula_bad_txn_hash(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        warehouse = baker.make(Warehouse, business=self.business)
        commodity_used = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
        )
        commodity_sold = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
        )

        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=23,
            gross_price=28.29,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_used,
            quantity=100,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=27,
            gross_price=33.21,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_sold,
            quantity=50,
        )

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=commodity_used,
                    count=3,
                ),
                baker.make(
                    WarehouseFormulaRow,
                    commodity=baker.make(Commodity),
                    count=8,
                ),
            ],
        )
        used_level = commodity_used.get_stock_level(warehouse)
        used_remaining_before = used_level.remaining_volume
        assert used_remaining_before
        sold_level = commodity_sold.get_stock_level(warehouse)
        sold_remaining_before = sold_level.remaining_volume
        assert sold_remaining_before

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                }
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [
                {
                    'commodity': commodity_used.id,
                    'count': 5,
                    'warehouse': warehouse.id,
                },
            ],
        }

        body['txn_hash'] = 'asdadasdasdadasd'
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'txn_hash',
                        'description': 'Checkout timeout',
                        'code': 'invalid',
                    }
                ],
            },
        )

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_formula_and_change_during_checkout(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        warehouse = baker.make(Warehouse, business=self.business)
        commodity_used = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
        )
        commodity_sold = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
        )

        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=23,
            gross_price=28.29,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_used,
            quantity=100,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=27,
            gross_price=33.21,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_sold,
            quantity=50,
        )

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=commodity_used,
                    count=3,
                ),
                baker.make(
                    WarehouseFormulaRow,
                    commodity=baker.make(Commodity),
                    count=8,
                ),
            ],
        )

        additional_commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
        )
        service = baker.make(Service, business=self.business)
        additional_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            price=50,
        )
        baker.make(
            WarehouseFormula,
            service_variants=[additional_variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=additional_commodity,
                    count=1,
                    warehouse=warehouse,
                ),
            ],
        )

        used_level = commodity_used.get_stock_level(warehouse)
        used_remaining_before = used_level.remaining_volume
        assert used_remaining_before
        sold_level = commodity_sold.get_stock_level(warehouse)
        sold_remaining_before = sold_level.remaining_volume
        assert sold_remaining_before

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                    'service_variant_id': self.variant.id,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                },
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [
                {
                    'commodity': commodity_used.id,
                    'count': 5,
                    'warehouse': warehouse.id,
                },
            ],
        }

        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        txn_hash = resp.json['transaction'].get('txn_hash')
        assert txn_hash
        assert len(resp.json['transaction']['commodity_usage']) == 1
        body['txn_hash'] = txn_hash

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        assert commodity_usage[1]['commodity']['id']
        assert commodity_usage[1]['count'] == 8
        assert 'commodity_usage' in data

        body['dry_run'] = True
        body['bookings'].append(
            {
                'service_variant_id': additional_variant.id,
                'quantity': 1,
                'item_price': additional_variant.price,
                'discount_rate': 0,
            }
        )
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        assert len(resp.json['transaction']['default_commodity_usage']) == 3
        commodity_usage = resp.json['transaction']['commodity_usage']
        assert len(commodity_usage) == 2
        assert commodity_usage[1]['commodity']['id'] == additional_commodity.id
        assert commodity_usage[1]['count'] == 1

        body_v0 = dict(body)
        body_v0['dry_run'] = True
        body_v0['bookings'].append(
            {
                'service_variant_id': additional_variant.id,
                'quantity': 1,
                'item_price': additional_variant.price,
                'discount_rate': 0,
            }
        )
        body_v0['commodity_usage'].append(
            {
                'commodity': additional_commodity.id,
                'count': 1,
                'warehouse': warehouse.id,
            }
        )
        resp = self.fetch(self.url, method='POST', body=body_v0)
        assert resp.code == 201, resp.json
        commodity_usage = resp.json['transaction']['commodity_usage']
        assert commodity_usage[1]['count'] == 2

        body['dry_run'] = True
        body['bookings'].remove(
            {
                'service_variant_id': additional_variant.id,
                'quantity': 1,
                'item_price': additional_variant.price,
                'discount_rate': 0,
            }
        )
        body['bookings'].remove(
            {
                'service_variant_id': additional_variant.id,
                'quantity': 1,
                'item_price': additional_variant.price,
                'discount_rate': 0,
            }
        )
        body_v1 = dict(body)
        body_v1['commodity_usage'].append(
            {
                'commodity': additional_commodity.id,
                'count': 1,
                'warehouse': warehouse.id,
            }
        )
        resp = self.fetch(self.url, method='POST', body=body_v1)
        assert resp.code == 201, resp.json
        assert len(resp.json['transaction']['commodity_usage']) == 1
        dict_assert(
            resp.json['transaction']['commodity_usage'],
            [
                {
                    'commodity': {
                        'id': commodity_used.id,
                        'total_pack_capacity': commodity_used.total_pack_capacity,
                        'name': commodity_used.name,
                        'volume_unit': commodity_used.volume_unit,
                    },
                    'count': 5.0,
                    'warehouse': warehouse.id,
                }
            ],
        )

        body_v2 = dict(body)
        body_v2['bookings'].append(
            {
                'service_variant_id': additional_variant.id,
                'quantity': 1,
                'item_price': additional_variant.price,
                'discount_rate': 0,
            }
        )
        resp = self.fetch(self.url, method='POST', body=body_v2)
        assert resp.code == 201, resp.json
        body_v2['commodity_usage'].remove(
            {
                'commodity': additional_commodity.id,
                'count': 1,
                'warehouse': warehouse.id,
            }
        )
        body_v2['commodity_usage'].remove(
            {
                'commodity': additional_commodity.id,
                'count': 1,
                'warehouse': warehouse.id,
            }
        )
        body_v2['commodity_usage'].append(
            {
                'commodity': additional_commodity.id,
                'count': 5,
                'warehouse': warehouse.id,
            }
        )
        body_v2['bookings'].remove(
            {
                'service_variant_id': additional_variant.id,
                'quantity': 1,
                'item_price': additional_variant.price,
                'discount_rate': 0,
            }
        )
        resp = self.fetch(self.url, method='POST', body=body_v2)
        assert resp.code == 201, resp.json
        dict_assert(
            resp.json['transaction']['commodity_usage'][1],
            {
                'commodity': {
                    'id': additional_commodity.id,
                    'total_pack_capacity': additional_commodity.total_pack_capacity,
                    'name': additional_commodity.name,
                    'volume_unit': additional_commodity.volume_unit,
                },
                'count': 4.0,
                'warehouse': warehouse.id,
            },
        )
        # dry_run = True so no documents created
        commodity_usage = resp.json['transaction']['commodity_usage']
        assert not WarehouseDocumentRow.objects.filter(
            commodity__id=commodity_usage[0]['commodity']['id'],
        )

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_formula_no_warehouse_in_body_request(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        net_price = 20
        commodity_used = baker.make(
            Commodity,
            business=self.business,
            tax_rate=self.tax_rate,
            net_price=net_price,
            tax=net_price * self.tax_rate.rate / 100,
        )
        net_price = 50
        tax = net_price * self.tax_rate.rate / 100
        commodity_sold = baker.make(
            Commodity,
            tax_rate=self.tax_rate,
            business=self.business,
            net_price=net_price,
            gross_price=net_price + tax,
            tax=tax,
        )

        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=23,
            gross_price=28.29,
            tax=23,
            warehouse=self.warehouse,
            commodity=commodity_used,
        )

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=commodity_used,
                    count=3,
                ),
                baker.make(
                    WarehouseFormulaRow,
                    commodity=baker.make(Commodity),
                    count=8,
                ),
            ],
        )

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    # no warehouse
                }
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [
                {
                    'commodity': commodity_used.id,
                    'count': 5,
                    'warehouse': self.warehouse.id,
                },
            ],
        }

        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        assert commodity_usage[1]['commodity']['id']
        assert commodity_usage[1]['count'] == 8
        assert 'commodity_usage' in data

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        assert commodity_usage[1]['commodity']['id']
        assert commodity_usage[1]['count'] == 8
        commodity_usage = data['commodity_usage']
        assert len(commodity_usage) == 1
        reported_row = commodity_usage[0]
        assert reported_row['commodity']['id'] == commodity_used.id
        assert 'name' in reported_row['commodity']
        assert 'volume_unit' in reported_row['commodity']
        assert reported_row['count'] == 5
        assert reported_row['warehouse'] == self.warehouse.id
        usage_rows = WarehouseFormulaRow.objects.filter(
            transactions__id__in=[resp.json['transaction']['id']],
        )
        assert usage_rows
        assert usage_rows.count() == 1
        usage_row = usage_rows.first()
        assert usage_row.commodity == commodity_used
        assert usage_row.count == 5
        assert usage_row.warehouse == self.warehouse
        assert usage_row.transactions.all().count() == 1
        assert usage_row.transactions.first().id == (resp.json['transaction']['id'])
        rw_document_row = WarehouseDocumentRow.objects.get(commodity=commodity_used)
        rw_document = rw_document_row.document
        assert rw_document.warehouse == self.warehouse
        assert rw_document.type == 'RW'
        assert rw_document.transaction.id == resp.json['transaction']['id']
        assert rw_document_row.net_price == Decimal('23.00')
        assert rw_document_row.gross_price == Decimal('28.29')
        assert rw_document_row.tax == Decimal('23.00')
        wz_document_row = WarehouseDocumentRow.objects.get(
            commodity=commodity_sold,
        )
        wz_document = wz_document_row.document
        assert wz_document.warehouse == self.warehouse
        assert wz_document.type == 'WZ'
        assert wz_document.transaction.id == resp.json['transaction']['id']
        assert wz_document_row.net_price == Decimal('45.00')
        assert wz_document_row.gross_price == Decimal('54.00')
        assert wz_document_row.tax == Decimal('9.00')
        assert data['rows'][1]['tax_amount'] == '$9.00'

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_formula_request_without_commodity_usage(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        commodity_used = baker.make(
            Commodity,
            business=self.business,
        )
        commodity_sold = baker.make(
            Commodity,
            business=self.business,
        )

        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=23,
            gross_price=28.29,
            tax=23,
            warehouse=self.warehouse,
            commodity=commodity_used,
        )

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=commodity_used,
                    count=3,
                    warehouse=self.warehouse,
                ),
            ],
        )

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    # no warehouse
                }
            ],
            'customer_card_id': self.bci.id,
        }

        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        assert 'commodity_usage' not in data

        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        commodity_usage = data['default_commodity_usage']
        assert commodity_usage[0]['commodity']['id']
        assert commodity_usage[0]['count'] == 3
        commodity_usage = data['commodity_usage']
        assert len(commodity_usage) == 1
        reported_row = commodity_usage[0]
        assert reported_row['commodity']['id'] == commodity_used.id
        assert 'name' in reported_row['commodity']
        assert 'volume_unit' in reported_row['commodity']
        assert reported_row['count'] == 3
        assert reported_row['warehouse'] == self.warehouse.id
        usage_rows = WarehouseFormulaRow.objects.filter(
            transactions__id__in=[resp.json['transaction']['id']],
        )
        assert usage_rows
        assert usage_rows.count() == 1
        usage_row = usage_rows.first()
        assert usage_row.commodity == commodity_used
        assert usage_row.count == 3
        assert usage_row.warehouse == self.warehouse
        assert usage_row.transactions.all().count() == 1
        assert usage_row.transactions.first().id == resp.json['transaction']['id']
        rw_document_row = WarehouseDocumentRow.objects.get(commodity=commodity_used)
        rw_document = rw_document_row.document
        assert rw_document.warehouse == self.warehouse
        assert rw_document.type == 'RW'
        assert rw_document.transaction.id == resp.json['transaction']['id']
        assert rw_document_row.net_price == 23
        assert float(rw_document_row.gross_price) == 28.29
        assert float(rw_document_row.tax) == 23
        wz_document_row = WarehouseDocumentRow.objects.get(
            commodity=commodity_sold,
        )
        wz_document = wz_document_row.document
        assert wz_document.warehouse == self.warehouse
        assert wz_document.type == 'WZ'
        assert wz_document.transaction.id == resp.json['transaction']['id']
        assert wz_document_row.net_price == 0
        assert float(wz_document_row.gross_price) == 0
        assert float(wz_document_row.tax) == 0

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_formula_no_warehouse(
        self,
        business_with_staffer_mock,
    ):
        self.warehouse.delete()
        business_with_staffer_mock.return_value = self.business
        commodity_used = baker.make(
            Commodity,
            business=self.business,
        )
        commodity_sold = baker.make(
            Commodity,
            business=self.business,
        )

        baker.make(
            WarehouseFormula,
            service_variants=[self.variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    commodity=commodity_used,
                    count=3,
                ),
                baker.make(
                    WarehouseFormulaRow,
                    commodity=baker.make(Commodity),
                    count=8,
                ),
            ],
        )

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 600,
                'type': 'H',
            },
            'discount_rate': 10,
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    # no warehouse
                }
            ],
            'customer_card_id': self.bci.id,
        }

        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400, resp.json
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'products.0.non_field_errors',
                        'description': 'No warehouse specified, no default warehouse',
                        'code': 'invalid',
                    }
                ]
            },
        )

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_products_only(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        warehouse = baker.make(Warehouse, business=self.business)
        tax_rate = baker.make(TaxRate, rate=23.00, pos=self.pos)
        commodity_sold = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
            net_price=100,
            gross_price=123,
            tax=23,
            tax_rate=tax_rate,
        )

        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=27,
            gross_price=33.21,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_sold,
            quantity=50,
        )
        sold_level = commodity_sold.get_stock_level(warehouse)
        sold_remaining_before = sold_level.remaining_volume
        assert sold_remaining_before

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [],
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                }
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [],
        }

        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        assert resp.json['transaction']['total_unformatted'] == '123.00'
        assert resp.json['transaction']['rows'][0]['tax_rate'] == '23%'
        assert resp.json['transaction']['rows'][0]['tax_amount'] == '$23.00'
        assert resp.json['transaction']['rows'][0]['tax_included'] == '23.00'
        assert resp.json['transaction']['rows'][0]['warehouse'] == warehouse.id

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        assert resp.json['transaction']['total_unformatted'] == '123.00'
        assert resp.json['transaction']['rows'][0]['tax_rate'] == '23%'
        assert resp.json['transaction']['rows'][0]['tax_amount'] == '$23.00'
        assert resp.json['transaction']['rows'][0]['tax_included'] == '23.00'
        assert resp.json['transaction']['rows'][0]['warehouse'] == warehouse.id

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_products_only_with_some_professional(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business
        warehouse = baker.make(Warehouse, business=self.business)
        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )

        commodity_sold = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
            net_price=100,
            gross_price=123,
            tax=23,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=27,
            gross_price=33.21,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_sold,
            quantity=50,
        )
        sold_level = commodity_sold.get_stock_level(warehouse)
        sold_remaining_before = sold_level.remaining_volume
        assert sold_remaining_before

        commodity_sold_pro = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=30,
            net_price=100,
            gross_price=123,
            tax=23,
            product_type=Commodity.TYPE_PRO,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=27,
            gross_price=33.21,
            tax=23,
            warehouse=warehouse,
            commodity=commodity_sold_pro,
            quantity=10,
        )
        sold_level = commodity_sold.get_stock_level(warehouse)
        sold_remaining_before = sold_level.remaining_volume
        assert sold_remaining_before

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            # dry_run set in test
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [],
            'products': [
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                },
                {
                    'discount_rate': 0,
                    'product_id': commodity_sold_pro.id,
                    'quantity': 1,
                    'warehouse': warehouse.id,
                },
            ],
            'customer_card_id': self.bci.id,
            'commodity_usage': [],
        }

        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400, resp.json
        error = resp.json['errors'][0]
        dict_assert(
            error,
            {
                'field': 'non_field_errors',
                'description': f"Can't sell professional {commodity_sold_pro.name} product. "
                f"Only retail products can be sold.",
                'code': 'invalid',
            },
        )


@pytest.mark.django_db
class BusinessTransactionSeriesDetailsHandlerTestCase(BusinessTransactionsHandlerBaseTestClass):
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionSeriesDetailsHandler, 'business_with_advanced_staffer')
    def test_get(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True)
        payment_type = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CREDIT_CARD)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business

        trn0 = baker.make(Transaction, pos=pos, parent_txn=None, total=1)
        baker.make(TransactionTip, transaction=trn0, amount=0)
        trn1 = baker.make(Transaction, pos=pos, parent_txn=trn0, total=2)
        baker.make(TransactionTip, transaction=trn1, amount=0)
        trn2 = baker.make(Transaction, pos=pos, parent_txn=trn1, total=3)
        baker.make(TransactionTip, transaction=trn2, amount=0)
        trn3 = baker.make(Transaction, pos=pos, parent_txn=trn2, total=4)
        baker.make(TransactionTip, transaction=trn3, amount=0)
        trn4 = baker.make(Transaction, pos=pos, parent_txn=trn3, total=5)
        baker.make(TransactionTip, transaction=trn4, amount=0)
        txns = [trn0, trn1, trn2, trn3, trn4]

        for txn in txns:
            receipt = baker.make(
                Receipt,
                transaction=txn,
                payment_type=payment_type,
                already_paid=txn.total,
            )
            txn.latest_receipt = receipt
            txn.save()

            baker.make(
                PaymentRow,
                amount=txn.total,
                receipt=receipt,
                status=rs.PAYMENT_SUCCESS,
                payment_type=payment_type,
            )

        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{trn2.id}/series'

        resp = self.fetch(url)

        assert resp.code == 200
        assert len(resp.json['transactions']) == 5

        for txn, trn in zip(resp.json['transactions'], txns):
            assert txn['id'] == trn.id
            assert 'tip' not in txn

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionSeriesDetailsHandler, 'business_with_advanced_staffer')
    def test_get_with_tips(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        business_with_staffer_mock.return_value = self.business
        cash = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
        trn0 = baker.make(Transaction, pos=pos, parent_txn=None, total=1)
        trn1 = baker.make(Transaction, pos=pos, parent_txn=trn0, total=2)
        trn2 = baker.make(Transaction, pos=pos, parent_txn=trn1, total=3)
        trn3 = baker.make(Transaction, pos=pos, parent_txn=trn2, total=4)
        trn4 = baker.make(Transaction, pos=pos, parent_txn=trn3, total=5)
        txns = [trn0, trn1, trn2, trn3, trn4]

        for txn in txns:
            receipt = baker.make(
                Receipt,
                transaction=txn,
                payment_type=cash,
                already_paid=txn.total,
            )
            txn.latest_receipt = receipt
            txn.save()

            pr = PaymentRow.create_with_status(
                receipt=receipt,
                status=rs.PAYMENT_SUCCESS,
                payment_type=cash,
                amount=txn.total,
            )
            pr.save()

            baker.make(
                TransactionTip, transaction=txn, rate=0, amount=0, type=SimpleTip.TIP_TYPE__PERCENT
            )

        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{trn2.id}/series'

        resp = self.fetch(url)

        assert resp.code == 200
        assert len(resp.json['transactions']) == 5

        for txn, trn in zip(resp.json['transactions'], txns):
            assert txn['id'] == trn.id
            assert txn['tip']['rate'] == '0.00'
            assert txn['tip']['type'] == 'P'


@pytest.mark.django_db
class BusinessTransactionsHandlerValidateChangesCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)
        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=20)
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
        )

        self.tip = baker.make(Tip, pos=self.pos, default=True, rate=61)

        self.booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'source': self.biz_booking_src,
                'updated_by': self.user,
            },
        )[0]

        baker.make(PaymentType, code=PaymentTypeEnum.CHECK, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.SQUARE, pos=self.pos)
        self.payment_type_code = PaymentTypeEnum.CHECK

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_post_transaction_incomplete(
        self,
        business_with_staffer_mock,
    ):
        """
        TransactionSerializer.validate used to set incomplete key in two places.
        Key was removed and validation moved form endpoint to Serializer.
        Test is checking if API behaviour is still same.
        First case: no rows.
        """
        business_with_staffer_mock.return_value = self.business

        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'tip_rate': 5,
            'discount_rate': 10,
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400
        print(resp.json)

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_post_transaction_incomplete_2(
        self,
        business_with_staffer_mock,
    ):
        """
        TransactionSerializer.validate used to set incomplete key in two places.
        Key was removed and validation moved form endpoint to Serializer.
        Test is checking if API behaviour is still same.
        First case: no prices in bookings.
        """
        business_with_staffer_mock.return_value = self.business
        commodity = baker.make(
            Commodity, business=self.business, net_price=740.77, gross_price=740.77
        )
        # create transaction
        body = {
            'payment_type_code': self.payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 5,
                'type': 'P',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': commodity.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 400, resp.json
        print(resp.json)


@pytest.mark.django_db
class BusinessTransactionHandlerPBAAutoAcceptTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        increase_appointment_next_id()
        super().setUp()
        self.user.cell_phone = '+***********'
        self.user.save()
        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
            payment_auto_accept=True,
            service_fee=0,
        )
        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=20)
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        self.product = baker.make(
            Commodity, business=self.business, net_price=330.11, gross_price=350.11
        )
        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=350.11,
            gross_price=350.11,
            tax=0.0,
            warehouse=self.warehouse,
            commodity=self.product,
        )
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='+***********',
            user=self.user,
        )

        self.card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
            default=True,
            tokenized_pm_id=uuid.uuid4(),
        )

        self.tip = baker.make(Tip, pos=self.pos, default=True, rate=61)

        self.booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'source': self.biz_booking_src,
                'updated_by': self.user,
            },
        )[0]

        self.cash = baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=self.pos)
        self.pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        self.split = baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=self.pos)

        self.body_call_for_payment = {
            'payment_type_code': self.pba.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 00,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

    @staticmethod
    def mock_auth_success():
        responses.add(
            responses.POST,
            settings.ADYEN_AUTH_URL,
            json={
                "pspReference": "123123123",
                "resultCode": "Authorised",
                "authCode": "65496",
            },
        )

    @staticmethod
    def mock_capture_success():
        data = {
            "pspReference": "233233233",
            "response": "[capture-received]",
        }
        responses.add(responses.POST, settings.ADYEN_CAPTURE_URL, json=data)

    def check_deeplink_parameters(self, txn: Transaction, mocked_deeplink: MagicMock):
        deeplink_path = f'{NotificationTarget.TRANSACTION_CALL_FOR_PAYMENT}/{txn.id}'
        self.assertEqual(
            mocked_deeplink.mock.call_args[0][0]['fields']['data'],
            {
                'mobile_deeplink': deeplink_path,
                '$ios_deeplink_path': deeplink_path,
                '$deeplink_path': deeplink_path,
                '$marketing_title': 'Payment Links',
            },
        )
        deeplink_fields = mocked_deeplink.mock.call_args[0][0]['fields']
        self.assertEqual(deeplink_fields['feature'], DeeplinkFeature.MOBILE_PAYMENT)
        self.assertEqual(deeplink_fields['channel'], Channel.Type.SMS)

    @responses.activate
    @patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_customer_wallet', MagicMock())
    @patch(
        'webapps.payment_providers.ports.payment_ports.PaymentProvidersPaymentPort.get_tokenized_pm'
    )
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_simple_pba_transaction_turn_on_but_invalid_card_status(
        self,
        business_with_staffer_mock,
        get_tokenized_pm_mock,
    ):
        self.mock_auth_success()
        get_tokenized_pm_mock.return_value = PortResponse(
            entity=get_invalid_tokenized_payment_method_mock().entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )
        business_with_staffer_mock.return_value = self.business
        self.user.payment_auto_accept = True
        self.user.save()

        # create transaction
        body = {
            'payment_type_code': self.pba.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        transaction = resp.json['transaction']
        assert len(transaction.get('payment_rows', [])) == 1
        txn = Transaction.objects.get(id=transaction['id'])
        pr = txn.payment_rows.get()
        assert pr.status == rs.CALL_FOR_PAYMENT

    @responses.activate
    @patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_customer_wallet', MagicMock())
    @patch(
        'webapps.payment_providers.ports.payment_ports.PaymentProvidersPaymentPort.get_tokenized_pm'
    )
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_simple_pba_transaction_turn_on_but_expires_soon_card_status(
        self,
        business_with_staffer_mock,
        get_tokenized_pm_mock,
    ):
        self.mock_auth_success()
        self.mock_capture_success()
        get_tokenized_pm_mock.return_value = PortResponse(
            entity=get_expires_soon_tokenized_payment_method_mock().entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )
        business_with_staffer_mock.return_value = self.business
        self.user.payment_auto_accept = True
        self.user.save()

        # create transaction
        body = {
            'payment_type_code': self.pba.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json
        transaction = resp.json['transaction']
        assert len(transaction.get('payment_rows', [])) == 1
        txn = Transaction.objects.get(id=transaction['id'])
        pr = txn.payment_rows.get()
        assert pr.status == rs.PAYMENT_SUCCESS

    @patch('webapps.payments.utils.get_tokenized_pm_internal_status', return_value='V')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    @patch(
        'webapps.payment_providers.ports.payment_ports.PaymentProvidersPaymentPort.get_tokenized_pm'
    )
    def test_simple_pba_transaction_turn_on_wrong_card(
        self,
        get_tokenized_pm_mock,
        business_with_staffer_mock,
        _,
    ):
        """Test if receipt automaticly updates to PaymentSucess"""
        self.card.provider = PaymentProviderEnum.STRIPE_PROVIDER
        self.card.save()
        business_with_staffer_mock.return_value = self.business

        get_tokenized_pm_mock.return_value = PortResponse(
            entity=get_invalid_tokenized_payment_method_mock().entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

        self.user.payment_auto_accept = True
        self.user.save()

        # create transaction
        body = {
            'payment_type_code': self.pba.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertTrue(resp.json)

        transaction = resp.json['transaction']
        self.assertEqual(len(transaction.get('payment_rows', [])), 1)

        txn = Transaction.objects.get(id=transaction['id'])
        pr = txn.payment_rows.get()
        self.assertEqual(pr.status, rs.CALL_FOR_PAYMENT)
        self.assertEqual(pr.payment_type, self.pba)
        self.assertEqual(pr.amount, Decimal('450.11'))

        receipt = txn.latest_receipt

        self.assertEqual(receipt.payment_type, self.pba)
        self.assertEqual(receipt.status_code, rs.CALL_FOR_PAYMENT)

    @patch('webapps.payments.utils.get_tokenized_pm_internal_status', return_value='V')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    @patch(
        'webapps.payment_providers.ports.payment_ports.PaymentProvidersPaymentPort.get_tokenized_pm'
    )
    def test_split_pba_transaction_turn_off(
        self,
        get_tokenized_pm_mock,
        business_with_staffer_mock,
        _,
    ):
        business_with_staffer_mock.return_value = self.business

        get_tokenized_pm_mock.return_value = PortResponse(
            entity=get_valid_tokenized_payment_method_mock().entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

        # create transaction
        body = {
            'payment_type_code': self.split.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'payment_rows': [
                {
                    'payment_type_code': self.pba.code,
                    'amount': 410.11,
                },
                {
                    'payment_type_code': self.cash.code,
                    'amount': 40,
                },
            ],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        transaction = resp.json['transaction']
        assert len(transaction['payment_rows']) == 2

        txn = Transaction.objects.get(id=transaction['id'])
        prs = txn.payment_rows.all().order_by('payment_type__code')
        assert prs[0].status == rs.PAYMENT_SUCCESS
        assert prs[0].payment_type == self.cash
        assert not prs[0].payment_link

        assert prs[1].status == rs.CALL_FOR_PAYMENT
        assert prs[1].payment_type == self.pba
        assert not prs[1].payment_link

        receipt = txn.latest_receipt
        assert receipt.payment_type == self.split
        assert receipt.status_code == rs.CALL_FOR_PAYMENT

    @responses.activate
    @patch('webapps.payments.utils.get_tokenized_pm_internal_status', return_value='V')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    @patch(
        'webapps.payment_providers.ports.payment_ports.PaymentProvidersPaymentPort.get_tokenized_pm'
    )
    def test_split_pba_transaction_turn_on(
        self,
        get_tokenized_pm_mock,
        business_with_staffer_mock,
        _,
    ):
        self.mock_auth_success()
        self.mock_capture_success()
        business_with_staffer_mock.return_value = self.business

        get_tokenized_pm_mock.return_value = PortResponse(
            entity=get_valid_tokenized_payment_method_mock().entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

        self.user.payment_auto_accept = True
        self.user.save()

        # create transaction
        body = {
            'payment_type_code': self.split.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'booking': self.booking.id,
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'payment_rows': [
                {
                    'payment_type_code': self.pba.code,
                    'amount': 410.11,
                },
                {
                    'payment_type_code': self.cash.code,
                    'amount': 40,
                },
            ],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        transaction = resp.json['transaction']
        assert len(transaction['payment_rows']) == 2

        txn = Transaction.objects.get(id=transaction['id'])
        prs = txn.payment_rows.all().order_by('payment_type__code')
        assert prs[0].status == rs.PAYMENT_SUCCESS
        assert prs[0].payment_type == self.cash
        assert not prs[0].payment_link

        assert prs[1].status == rs.PAYMENT_SUCCESS
        assert prs[1].payment_type == self.pba
        assert not prs[1].payment_link

        receipt = txn.latest_receipt
        assert receipt.payment_type == self.split
        assert receipt.status_code == rs.PAYMENT_SUCCESS

    @responses.activate
    @patch('webapps.payments.utils.get_tokenized_pm_internal_status', return_value='V')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    @patch(
        'webapps.payment_providers.ports.payment_ports.PaymentProvidersPaymentPort.get_tokenized_pm'
    )
    def test_create_transaction_with_invalid_booking_id_in_body(
        self,
        get_tokenized_pm_mock,
        business_with_staffer_mock,
        _,
    ):
        get_tokenized_pm_mock.return_value = PortResponse(
            entity=get_invalid_tokenized_payment_method_mock().entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

        self.mock_auth_success()
        self.mock_capture_success()
        business_with_staffer_mock.return_value = self.business
        # internal_status_mock.return_value = 'V'

        self.user.payment_auto_accept = True
        self.user.save()
        create_subbooking(business=self.business)

        body = {
            'payment_type_code': self.split.code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'booking': self.booking.appointment_id,  # it's multibooking id
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': 0,
                'type': 'P',
            },
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
            'payment_rows': [
                {
                    'payment_type_code': self.pba.code,
                    'amount': 410.11,
                },
                {
                    'payment_type_code': self.cash.code,
                    'amount': 40,
                },
            ],
            'customer_card_id': self.bci.id,
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201
        transaction = resp.json['transaction']
        assert len(transaction['payment_rows']) == 2
        assert Transaction.objects.count() == 1

        # TODO: when fix from 65743 will be done
        # please remove above assertion and uncomment below code
        # resp = self.fetch(url, method='POST', body=body)
        # assert resp.code == 400
        # assert resp.json['errors'][0]['code'] == 'does_not_exist'
        #
        # body['booking'] = booking_2.id
        # resp = self.fetch(url, method='POST', body=body)
        # assert resp.code == 400
        # assert resp.json == {
        #     'errors': [{
        #         'field': 'non_field_errors',
        #         'description': (
        #             'All bookings should be from the same multibooking'
        #         ),
        #         'code': 'invalid',
        #     }]
        # }

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_update_transaction_with_invalid_booking_id_in_body(
        self,
        business_with_staffer_mock,
    ):
        business_with_staffer_mock.return_value = self.business

        self.user.payment_auto_accept = True
        self.user.save()

        # create transaction
        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'multibooking': self.booking.appointment_id,
            'tip_rate': 0,
            'discount_rate': 0,
            'bookings': [
                {
                    'booking_id': self.booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'products': [{'discount_rate': 0, 'product_id': self.product.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 201
        assert Transaction.objects.all().count() == 1
        transaction_id = resp.json['transaction']['id']

        body['booking'] = body.pop('multibooking')
        body['products'][0]['quantity'] = 2
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{transaction_id}'
        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200
        assert Transaction.objects.all().count() == 2

        # TODO: when fix from 65743 will be done
        # please remove above assertion and uncomment below code
        # assert resp.code == 400
        # assert resp.json['errors'][0]['code'] == 'does_not_exist'

    def _check_experiment_extra_data(self, transaction_id):
        slot = ExperimentSlot.objects.filter(relation_id=self.user.id).first()
        self.assertEqual(slot.extra_data, {'transaction_id': transaction_id})


@pytest.mark.django_db
class BusinessTransactionCreateInvoiceHandlerTests(BaseAsyncHTTPTest):
    url = (
        '/business_api/me/businesses/{business_id}/pos/transactions/'
        '{transaction_id}/create_invoice/'
    )

    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        baker.make(Seller, business=self.business)
        self.pos = baker.make(POS, business=self.business, active=True)
        self.owner.name = 'Owner staffer'
        self.owner.save()

    def test_post(self):
        customer = baker.make(BusinessCustomerInfo, business=self.business)
        buyer = baker.make(Buyer, customer=customer)

        payment_type = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.BANK_TRANSFER)
        transaction = baker.make(Transaction, pos=self.pos)
        baker.make(
            TransactionRow,
            transaction=transaction,
            name_line_1='First item name',
            quantity=2,
            discounted_item_price=Decimal('10.50'),
            tax_rate=Decimal('23.00'),
        )
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=transaction.id,
        )

        # Can't create invoice for transaction without receipt
        resp = self.fetch(url, method='POST', body={'buyer_id': buyer.id})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors']

        # Attaching prepaid receipt to transaction
        prepaid_receipt = baker.make(
            Receipt,
            transaction=transaction,
            payment_type=payment_type,
            receipt_number='AAA/123',
            status_code=rs.PREPAYMENT_SUCCESS,
        )
        baker.make(
            PaymentRow,
            receipt=prepaid_receipt,
            status=rs.PREPAYMENT_SUCCESS,
            payment_type=payment_type,
        )
        transaction.latest_receipt = prepaid_receipt
        transaction.save()

        # It should not be allowed to create invoice for only prepaid trans.
        resp = self.fetch(url, method='POST', body={'buyer_id': buyer.id})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors']

        # Attaching fully paid receipt to transaction
        receipt = baker.make(
            Receipt,
            transaction=transaction,
            payment_type=payment_type,
            receipt_number='AAA/123',
            status_code=rs.PAYMENT_SUCCESS,
        )
        baker.make(
            PaymentRow,
            receipt=receipt,
            status=rs.PAYMENT_SUCCESS,
            payment_type=payment_type,
        )
        transaction.latest_receipt = receipt
        transaction.save()

        # Creating an invoice
        resp = self.fetch(url, method='POST', body={'buyer_id': buyer.id})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        invoice = CustomerInvoice.objects.filter(id=resp.json['id']).first()
        assert invoice

        # Creating second invoice for the same transaction should fail
        resp = self.fetch(url, method='POST', body={'buyer_id': buyer.id})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors']

        # Soft deletion of old invoice should allow creation of new one
        invoice.is_paid = False
        invoice.soft_delete()
        resp = self.fetch(url, method='POST', body={'buyer_id': buyer.id})
        assert resp.code == status.HTTP_200_OK, resp.json
        assert resp.json


# pylint: disable=too-many-public-methods
@pytest.mark.django_db
class PaymentRowListingHandlerTestCase(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business, active=True)
        self.url = f'/business_api/me/businesses/{self.business.id}/pos/payment_rows/'

    @staticmethod
    def make_receipt(txn, status_code, payment_type, parent_pr=None):
        receipt = baker.make(
            Receipt,
            status_code=status_code,
            transaction=txn,
        )
        pr = baker.make(
            PaymentRow,
            status=status_code,
            receipt=receipt,
            payment_type=payment_type,
            parent_payment_row=parent_pr,
            amount=100,
        )

        return receipt, pr

    def test_get_cash(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr.id
        assert payment_rows[0]['transaction_id'] == txn.id

    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_get_with_search_query(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        bci_1 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
            email="<EMAIL>",
        )

        bci_2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Anna',
            last_name='Kowalska',
            cell_phone='987654321',
            email="<EMAIL>",
        )

        business_with_staffer_mock.return_value = self.business
        service = baker.make(Service, business=self.business, name='Service_A')
        service_variant = baker.make(
            ServiceVariant, service=service, duration=relativedelta(minutes=15)
        )

        booking_1 = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
            },
        )[0]
        booking_2 = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
                'service_name': 'Service_B',
            },
        )[0]

        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        txn_1 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking_1.appointment_id,
            customer_card=bci_1,
            customer_data='Jan Matejko, 1234567890, <EMAIL>',
        )
        baker.make(
            TransactionRow,
            transaction=txn_1,
            service_variant=service_variant,
        )
        receipt_1, pr_1 = self.make_receipt(
            txn=txn_1,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        txn_1.latest_receipt = receipt_1
        txn_1.save()

        txn_2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking_2.appointment_id,
            customer_card=bci_2,
            customer_data='Anna Kowalska, 987654321, <EMAIL>',
        )

        baker.make(
            TransactionRow,
            transaction=txn_2,
            subbooking=booking_2,
        )

        receipt_2, pr_2 = self.make_receipt(
            txn=txn_2,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        txn_2.latest_receipt = receipt_2
        txn_2.save()

        resp = self.fetch(self.url + '?query=Service_A', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id in ids
        assert pr_2.id not in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=Service_B', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id not in ids
        assert pr_2.id in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=Service', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id in ids
        assert pr_2.id in ids
        assert len(ids) == 2

        resp = self.fetch(self.url + '?query=Jan', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id in ids
        assert pr_2.id not in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=Kowalska', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id not in ids
        assert pr_2.id in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=<EMAIL>', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id not in ids
        assert pr_2.id in ids
        assert len(ids) == 1

        resp = self.fetch(self.url + '?query=1234567890', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [pr['id'] for pr in resp.json['payment_rows']]
        assert pr_1.id in ids
        assert pr_2.id not in ids
        assert len(ids) == 1

    @patch.object(Business, 'get_timezone')
    def test_get_hidden_user_data(
        self,
        get_timezone_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
            email="<EMAIL>",
        )
        service = baker.make(Service, business=self.business, name='Service_A')
        service_variant = baker.make(
            ServiceVariant, service=service, duration=relativedelta(minutes=15)
        )

        booking = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'booked_from': tznow() - datetime.timedelta(hours=1),
                'booked_till': tznow(),
                'status': Appointment.STATUS.FINISHED,
                'updated_by': self.user,
                'source': self.biz_booking_src,
            },
        )[0]

        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
            customer_card=bci,
            customer_data='Jan Matejko, 1234567890, <EMAIL>',
        )
        baker.make(
            TransactionRow,
            transaction=txn,
            service_variant=service_variant,
        )
        receipt, _pr = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )

        txn.latest_receipt = receipt
        txn.save()

        user_advanced = baker.make(User)
        baker.make(
            Resource,
            staff_user=user_advanced,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_ADVANCED,
        )
        self.session = user_advanced.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['payment_rows'][0]['customer_info']['email'] == 'LOCKED'
        assert resp.json['payment_rows'][0]['customer_info']['phone'] == 'LOCKED'

    def test_get_deleted_cash(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn, status_code=rs.ARCHIVED, payment_type=cash, parent_pr=pr
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_get_deleted_transaction_cash(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            deleted=tznow(),  # deleted transaction
        )
        receipt, _ = self.make_receipt(txn=txn, status_code=rs.PAYMENT_SUCCESS, payment_type=cash)
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_get_edited_cash(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        # SUCCESS
        _receipt1, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )

        # ARCHIVED
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.ARCHIVED,
            payment_type=cash,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        # NEW TRANSACTION
        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        txn2.latest_receipt = receipt3
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_pba_call_for_payment(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_call_for_payment_3ds(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT_3DS,
            payment_type=pba,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_success(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn, status_code=rs.PAYMENT_SUCCESS, payment_type=pba, parent_pr=pr
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_cancelled(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=pba,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['amount'] == '0.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_cancelled_cash_success(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=pba,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        txn2.latest_receipt = receipt3
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_pba_cancelled_cfp(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=pba,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        txn2.latest_receipt = receipt3
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_pba_cancelled_success(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=pba,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=pba,
            parent_pr=pr3,
        )
        txn2.latest_receipt = receipt4
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_pba_failed(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_FAILED,
            payment_type=pba,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['amount'] == '0.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_failed_cancelled(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_FAILED,
            payment_type=pba,
            parent_pr=pr1,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=pba,
            parent_pr=pr2,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['amount'] == '0.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_failed_cancelled_2nd_attempt_cfp(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_FAILED,
            payment_type=pba,
            parent_pr=pr1,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=pba,
            parent_pr=pr2,
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_success_sent_for_refund(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=pba,
            parent_pr=pr1,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.SENT_FOR_REFUND,
            payment_type=pba,
            parent_pr=pr2,
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_success_refunded(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=pba,
            parent_pr=pr1,
        )

        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.SENT_FOR_REFUND,
            payment_type=pba,
            parent_pr=pr2,
        )

        receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.REFUNDED,
            payment_type=pba,
            parent_pr=pr3,
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_success_chargeback(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=pba,
            parent_pr=pr1,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK,
            payment_type=pba,
            parent_pr=pr2,
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['amount'] == '-100.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_success_chargeback_reverse(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=pba,
            parent_pr=pr1,
        )

        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK,
            payment_type=pba,
            parent_pr=pr2,
        )

        receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK_REVERSED,
            payment_type=pba,
            parent_pr=pr3,
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr2.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_pba_success_second_chargeback(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=pba,
            parent_pr=pr1,
        )

        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK,
            payment_type=pba,
            parent_pr=pr2,
        )

        _receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK_REVERSED,
            payment_type=pba,
            parent_pr=pr3,
        )
        receipt5, pr5 = self.make_receipt(
            txn=txn,
            status_code=rs.SECOND_CHARGEBACK,
            payment_type=pba,
            parent_pr=pr4,
        )

        txn.latest_receipt = receipt5
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 4
        assert payment_rows[3]['id'] == pr2.id
        assert payment_rows[3]['transaction_id'] == txn.id
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr4.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_prepayment_call_for_prepayment(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, _pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_get_prepayment_call_for_prepayment_3ds(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, _pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT_3DS,
            payment_type=prepayment,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_get_prepayment_authorisation(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_get_prepayment_success(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_prepayment_success_update_with_booking(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        ############
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        receipt4, _pr4 = self.make_receipt(
            txn=txn, status_code=rs.ARCHIVED, payment_type=prepayment, parent_pr=pr3
        )
        txn.latest_receipt = receipt4
        txn.save()

        #############
        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt5, pr5 = self.make_receipt(
            txn=txn2, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )
        txn2.latest_receipt = receipt5
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_prepayment_success_update_with_booking_multiple_times(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        ############
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        receipt4, _pr4 = self.make_receipt(
            txn=txn, status_code=rs.ARCHIVED, payment_type=prepayment, parent_pr=pr3
        )
        txn.latest_receipt = receipt4
        txn.save()

        #############
        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        _receipt5, pr5 = self.make_receipt(
            txn=txn2, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )
        receipt6, _pr6 = self.make_receipt(
            txn=txn2, status_code=rs.ARCHIVED, payment_type=prepayment, parent_pr=pr5
        )
        txn2.latest_receipt = receipt6
        txn2.save()

        txn3 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn2,
        )
        receipt7, pr7 = self.make_receipt(
            txn=txn3, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr5
        )

        txn3.latest_receipt = receipt7
        txn3.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr7.id
        assert payment_rows[0]['transaction_id'] == txn3.id

    def test_get_prepayment_authorisation_failed(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_FAILED,
            payment_type=prepayment,
            parent_pr=pr,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_get_prepayment_failed(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_FAILED, payment_type=prepayment, parent_pr=pr2
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_prepayment_sent_for_refund(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.SENT_FOR_REFUND, payment_type=prepayment, parent_pr=pr3
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_prepayment_refunded(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        _receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.SENT_FOR_REFUND, payment_type=prepayment, parent_pr=pr3
        )
        receipt5, pr5 = self.make_receipt(
            txn=txn, status_code=rs.REFUNDED, payment_type=prepayment, parent_pr=pr4
        )

        txn.latest_receipt = receipt5
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_prepayment_chargeback(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK, payment_type=prepayment, parent_pr=pr3
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[0]['amount'] == '-100.00'

    def test_get_prepayment_chargeback_reversed(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        _receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK, payment_type=prepayment, parent_pr=pr3
        )
        receipt5, pr5 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK_REVERSED, payment_type=prepayment, parent_pr=pr4
        )

        txn.latest_receipt = receipt5
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr4.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[0]['amount'] == '100.00'

    def test_get_prepayment_second_chargeback(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        _receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK, payment_type=prepayment, parent_pr=pr3
        )
        receipt5, pr5 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK_REVERSED, payment_type=prepayment, parent_pr=pr4
        )
        _receipt6, pr6 = self.make_receipt(
            txn=txn, status_code=rs.SECOND_CHARGEBACK, payment_type=prepayment, parent_pr=pr5
        )

        txn.latest_receipt = receipt5
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 4
        assert payment_rows[3]['id'] == pr3.id
        assert payment_rows[3]['transaction_id'] == txn.id
        assert payment_rows[2]['id'] == pr4.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr5.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr6.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[0]['amount'] == '-100.00'

    def test_get_prepayment_success_100_percent(self):
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, _pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )
        txn2.latest_receipt = receipt4
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_cash_prepayment(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, _pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )
        txn2.latest_receipt = receipt4
        txn2.save()

        pr5 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt4,
            payment_type=cash,
        )

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_cfp_prepayment(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, _pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )
        txn2.latest_receipt = receipt4
        txn2.save()

        pr5 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt4,
            payment_type=pba,
        )

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_pba_prepayment(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )

        pr5 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt4,
            payment_type=pba,
        )

        receipt5, _pr6 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr4
        )

        pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt5,
            payment_type=pba,
            parent_payment_row=pr5,
        )
        txn2.latest_receipt = receipt5
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr7.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_pba_prepayment_send_for_refund(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )

        pr5 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt4,
            payment_type=pba,
        )

        receipt5, pr6 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr4
        )

        pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt5,
            payment_type=pba,
            parent_payment_row=pr5,
        )

        receipt6, pr8 = self.make_receipt(
            txn=txn2, status_code=rs.SENT_FOR_REFUND, payment_type=prepayment, parent_pr=pr6
        )

        _pr9 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt6,
            payment_type=pba,
            parent_payment_row=pr7,
        )

        txn2.latest_receipt = receipt6
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr7.id
        assert payment_rows[1]['transaction_id'] == txn2.id
        assert payment_rows[0]['id'] == pr8.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_pba_prepayment_refunded(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )

        pr5 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt4,
            payment_type=pba,
        )

        receipt5, pr6 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr4
        )

        pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt5,
            payment_type=pba,
            parent_payment_row=pr5,
        )

        receipt6, pr8 = self.make_receipt(
            txn=txn2, status_code=rs.SENT_FOR_REFUND, payment_type=prepayment, parent_pr=pr6
        )

        pr9 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt6,
            payment_type=pba,
            parent_payment_row=pr7,
        )

        receipt7, pr10 = self.make_receipt(
            txn=txn2, status_code=rs.REFUNDED, payment_type=prepayment, parent_pr=pr8
        )

        _pr11 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt7,
            payment_type=pba,
            parent_payment_row=pr9,
        )

        txn2.latest_receipt = receipt7
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr7.id
        assert payment_rows[1]['transaction_id'] == txn2.id
        assert payment_rows[0]['id'] == pr10.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_pba_refunded_prepayment(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )

        pr5 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt4,
            payment_type=pba,
        )

        receipt5, pr6 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr4
        )

        pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt5,
            payment_type=pba,
            parent_payment_row=pr5,
        )

        receipt6, pr8 = self.make_receipt(
            txn=txn2, status_code=rs.SENT_FOR_REFUND, payment_type=pba, parent_pr=pr7
        )

        pr9 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt6,
            payment_type=prepayment,
            parent_payment_row=pr6,
        )

        receipt7, pr10 = self.make_receipt(
            txn=txn2, status_code=rs.REFUNDED, payment_type=pba, parent_pr=pr8
        )

        _pr11 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt7,
            payment_type=prepayment,
            parent_payment_row=pr9,
        )

        txn2.latest_receipt = receipt7
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr7.id
        assert payment_rows[1]['transaction_id'] == txn2.id
        assert payment_rows[0]['id'] == pr10.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_pba_refunded_prepayment_refunded(self):  # pylint: disable=too-many-locals
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )

        pr5 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt4,
            payment_type=pba,
        )

        receipt5, pr6 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr4
        )

        pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt5,
            payment_type=pba,
            parent_payment_row=pr5,
        )

        receipt6, pr8 = self.make_receipt(
            txn=txn2, status_code=rs.SENT_FOR_REFUND, payment_type=prepayment, parent_pr=pr6
        )

        pr9 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt6,
            payment_type=pba,
            parent_payment_row=pr7,
        )

        receipt7, pr10 = self.make_receipt(
            txn=txn2, status_code=rs.REFUNDED, payment_type=prepayment, parent_pr=pr8
        )

        pr11 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt7,
            payment_type=pba,
            parent_payment_row=pr9,
        )

        receipt8 = baker.make(
            Receipt,
            status_code=rs.SENT_FOR_REFUND,
            transaction=txn2,
        )

        pr12 = baker.make(
            PaymentRow,
            status=rs.REFUNDED,
            receipt=receipt8,
            payment_type=prepayment,
            parent_payment_row=pr10,
            amount=100,
        )
        pr13 = baker.make(
            PaymentRow,
            status=rs.SENT_FOR_REFUND,
            receipt=receipt8,
            payment_type=pba,
            parent_payment_row=pr11,
        )

        receipt9 = baker.make(
            Receipt,
            status_code=rs.REFUNDED,
            transaction=txn2,
        )

        _pr14 = baker.make(
            PaymentRow,
            status=rs.REFUNDED,
            receipt=receipt9,
            payment_type=prepayment,
            parent_payment_row=pr12,
            amount=100,
        )
        pr15 = baker.make(
            PaymentRow,
            status=rs.REFUNDED,
            receipt=receipt9,
            payment_type=pba,
            parent_payment_row=pr13,
        )

        txn2.latest_receipt = receipt9
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 4
        assert payment_rows[3]['id'] == pr3.id
        assert payment_rows[3]['transaction_id'] == txn.id
        assert payment_rows[2]['id'] == pr7.id
        assert payment_rows[2]['transaction_id'] == txn2.id
        assert payment_rows[1]['id'] == pr10.id
        assert payment_rows[1]['transaction_id'] == txn2.id
        assert payment_rows[0]['id'] == pr15.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_payment(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        check = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CHECK,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )

        pr2 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=check,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_split_payment_pba_cfp(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        pr2 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr1.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_split_payment_pba_success(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt = baker.make(
            Receipt,
            status_code=rs.CALL_FOR_PAYMENT,
            transaction=txn,
        )

        pr1 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            amount=100,
        )
        pr2 = baker.make(
            PaymentRow,
            status=rs.CALL_FOR_PAYMENT,
            receipt=receipt,
            payment_type=pba,
        )

        receipt2 = baker.make(
            Receipt,
            status_code=rs.CALL_FOR_PAYMENT,
            transaction=txn,
        )

        _pr3 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt2,
            payment_type=cash,
            parent_payment_row=pr1,
        )
        pr4 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt2,
            payment_type=pba,
            parent_payment_row=pr2,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr1.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_split_payment_pba_sent_for_refund(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )

        pr2 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
        )

        _receipt2, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PAYMENT_SUCCESS, payment_type=pba, parent_pr=pr1
        )

        pr4 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            parent_payment_row=pr2,
        )

        receipt3, pr6 = self.make_receipt(
            txn=txn, status_code=rs.SENT_FOR_REFUND, payment_type=pba, parent_pr=pr3
        )

        _pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            parent_payment_row=pr4,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr2.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr6.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_cf_call_for_deposit(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        receipt, _pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_cf_authorisation_success(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
            parent_pr=pr,
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_cf_deposit_charge_success(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.DEPOSIT_CHARGE_SUCCESS,
            payment_type=pba,
            parent_pr=pr2,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_cf_deposit_charge_success_sent_for_refund(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.DEPOSIT_CHARGE_SUCCESS,
            payment_type=pba,
            parent_pr=pr2,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.SENT_FOR_REFUND,
            payment_type=pba,
            parent_pr=pr3,
        )
        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id

    def test_cf_deposit_charge_cancelled(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
            parent_pr=pr,
        )
        receipt3, _pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.DEPOSIT_CHARGE_CANCELED,
            payment_type=pba,
            parent_pr=pr2,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 0

    def test_cf_deposit_charge_failed(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_DEPOSIT,
            payment_type=pba,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.DEPOSIT_CHARGE_FAILED,
            payment_type=pba,
            parent_pr=pr2,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_donation_success(self):
        pba = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=pba,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.DONATION_SUCCESS,
            payment_type=pba,
            parent_pr=pr,
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_square_pending(self):
        square = baker.make(
            PaymentType,
            code=PaymentTypeEnum.SQUARE,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=square,
        )

        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_square_success(self):
        square = baker.make(
            PaymentType,
            code=PaymentTypeEnum.SQUARE,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=square,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=square,
            parent_pr=pr,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_pending(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_success(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn, status_code=rs.PAYMENT_SUCCESS, payment_type=stripe, parent_pr=pr
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_cancelled(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=stripe,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['amount'] == '0.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_cancelled_cash_success(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=stripe,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=cash,
        )
        txn2.latest_receipt = receipt3
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_stripe_cancelled_cfp(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=stripe,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        txn2.latest_receipt = receipt3
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_stripe_cancelled_success(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        receipt2, _pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=stripe,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn2,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=stripe,
            parent_pr=pr3,
        )
        txn2.latest_receipt = receipt4
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_stripe_failed(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=stripe,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_FAILED,
            payment_type=stripe,
            parent_pr=pr1,
        )
        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['amount'] == '0.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_failed_cancelled(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_FAILED,
            payment_type=stripe,
            parent_pr=pr1,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=stripe,
            parent_pr=pr2,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['amount'] == '0.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_failed_cancelled_2nd_attempt_cfp(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_FAILED,
            payment_type=stripe,
            parent_pr=pr1,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_CANCELED,
            payment_type=stripe,
            parent_pr=pr2,
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_success_sent_for_refund(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=stripe,
            parent_pr=pr1,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.SENT_FOR_REFUND,
            payment_type=stripe,
            parent_pr=pr2,
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_success_refunded(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PAYMENT,
            payment_type=stripe,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=stripe,
            parent_pr=pr1,
        )

        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.SENT_FOR_REFUND,
            payment_type=stripe,
            parent_pr=pr2,
        )

        receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.REFUNDED,
            payment_type=stripe,
            parent_pr=pr3,
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_success_chargeback(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=stripe,
            parent_pr=pr1,
        )

        receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK,
            payment_type=stripe,
            parent_pr=pr2,
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['amount'] == '-100.00'
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_success_chargeback_reverse(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=stripe,
            parent_pr=pr1,
        )

        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK,
            payment_type=stripe,
            parent_pr=pr2,
        )

        receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK_REVERSED,
            payment_type=stripe,
            parent_pr=pr3,
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr2.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_stripe_success_second_chargeback(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PAYMENT_SUCCESS,
            payment_type=stripe,
            parent_pr=pr1,
        )

        _receipt3, pr3 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK,
            payment_type=stripe,
            parent_pr=pr2,
        )

        _receipt4, pr4 = self.make_receipt(
            txn=txn,
            status_code=rs.CHARGEBACK_REVERSED,
            payment_type=stripe,
            parent_pr=pr3,
        )
        receipt5, pr5 = self.make_receipt(
            txn=txn,
            status_code=rs.SECOND_CHARGEBACK,
            payment_type=stripe,
            parent_pr=pr4,
        )

        txn.latest_receipt = receipt5
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 4
        assert payment_rows[3]['id'] == pr2.id
        assert payment_rows[3]['transaction_id'] == txn.id
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr4.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_split_stripe_prepayment(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, _pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )
        txn2.latest_receipt = receipt4
        txn2.save()

        pr5 = baker.make(
            PaymentRow,
            status=rs.PENDING,
            receipt=receipt4,
            payment_type=stripe,
        )

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_stripe_prepayment_2(self):
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        prepayment = baker.make(
            PaymentType,
            code=PaymentTypeEnum.PREPAYMENT,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_PREPAYMENT,
            payment_type=prepayment,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PREPAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr2
        )
        txn.latest_receipt = receipt3
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr3
        )

        pr5 = baker.make(
            PaymentRow,
            status=rs.PENDING,
            receipt=receipt4,
            payment_type=stripe,
        )

        receipt5, _pr6 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=prepayment, parent_pr=pr4
        )

        pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt5,
            payment_type=stripe,
            parent_payment_row=pr5,
        )
        txn2.latest_receipt = receipt5
        txn2.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr7.id
        assert payment_rows[0]['transaction_id'] == txn2.id

    def test_get_split_payment_stripe_cfp(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        pr2 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
        )
        txn.latest_receipt = receipt
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr1.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_split_payment_stripe_success(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt = baker.make(
            Receipt,
            status_code=rs.PENDING,
            transaction=txn,
        )

        pr1 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            amount=100,
        )
        pr2 = baker.make(
            PaymentRow,
            status=rs.PENDING,
            receipt=receipt,
            payment_type=stripe,
        )

        receipt2 = baker.make(
            Receipt,
            status_code=rs.PAYMENT_SUCCESS,
            transaction=txn,
        )

        _pr3 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt2,
            payment_type=cash,
            parent_payment_row=pr1,
        )
        pr4 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt2,
            payment_type=stripe,
            parent_payment_row=pr2,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr1.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_split_payment_stripe_sent_for_refund(self):
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt, pr1 = self.make_receipt(
            txn=txn,
            status_code=rs.PENDING,
            payment_type=stripe,
        )

        pr2 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
        )

        _receipt2, pr3 = self.make_receipt(
            txn=txn, status_code=rs.PAYMENT_SUCCESS, payment_type=stripe, parent_pr=pr1
        )

        pr4 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            parent_payment_row=pr2,
        )

        receipt3, pr6 = self.make_receipt(
            txn=txn, status_code=rs.SENT_FOR_REFUND, payment_type=stripe, parent_pr=pr3
        )

        _pr7 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=cash,
            parent_payment_row=pr4,
        )
        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr2.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr6.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_booksy_pay_success(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_SUCCESS,
            payment_type=booksy_pay,
            parent_pr=pr,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_booksy_pay_failed(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_FAILED,
            payment_type=booksy_pay,
            parent_pr=pr,
        )

        txn.latest_receipt = receipt2
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 1
        assert payment_rows[0]['id'] == pr2.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_booksy_pay_sent_for_refund(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_SUCCESS,
            payment_type=booksy_pay,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.SENT_FOR_REFUND, payment_type=booksy_pay, parent_pr=pr2
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_booksy_pay_refunded(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_SUCCESS,
            payment_type=booksy_pay,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.SENT_FOR_REFUND, payment_type=booksy_pay, parent_pr=pr2
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.REFUNDED, payment_type=booksy_pay, parent_pr=pr3
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id

    def test_get_booksy_pay_chargeback(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_SUCCESS,
            payment_type=booksy_pay,
            parent_pr=pr,
        )
        receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK, payment_type=booksy_pay, parent_pr=pr2
        )

        txn.latest_receipt = receipt3
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr3.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[0]['amount'] == '-100.00'

    def test_get_booksy_pay_chargeback_reversed(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_SUCCESS,
            payment_type=booksy_pay,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK, payment_type=booksy_pay, parent_pr=pr2
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK_REVERSED, payment_type=booksy_pay, parent_pr=pr3
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 3
        assert payment_rows[2]['id'] == pr2.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr3.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[0]['amount'] == '100.00'

    def test_get_booksy_pay_second_chargeback(self):
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        _receipt2, pr2 = self.make_receipt(
            txn=txn,
            status_code=rs.BOOKSY_PAY_SUCCESS,
            payment_type=booksy_pay,
            parent_pr=pr,
        )
        _receipt3, pr3 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK, payment_type=booksy_pay, parent_pr=pr2
        )
        receipt4, pr4 = self.make_receipt(
            txn=txn, status_code=rs.CHARGEBACK_REVERSED, payment_type=booksy_pay, parent_pr=pr3
        )
        _receipt5, pr5 = self.make_receipt(
            txn=txn, status_code=rs.SECOND_CHARGEBACK, payment_type=booksy_pay, parent_pr=pr4
        )

        txn.latest_receipt = receipt4
        txn.save()

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        payment_rows = resp.json['payment_rows']
        assert len(payment_rows) == 4
        assert payment_rows[3]['id'] == pr2.id
        assert payment_rows[3]['transaction_id'] == txn.id
        assert payment_rows[2]['id'] == pr3.id
        assert payment_rows[2]['transaction_id'] == txn.id
        assert payment_rows[1]['id'] == pr4.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr5.id
        assert payment_rows[0]['transaction_id'] == txn.id
        assert payment_rows[0]['amount'] == '-100.00'

    def test_get_cash_booksy_pay(self):
        """
        Flow:
        1. Pay with Booksy Pay (CALL_FOR_BOOKSY_PAY, BOOKSY_PAY_SUCCESS)
        2. Checkout (PAYMENT_SUCCESS)
        3. Extra cash payment (PAYMENT_SUCCESS)
        """
        cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        booksy_pay = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        _receipt, pr = self.make_receipt(
            txn=txn,
            status_code=rs.CALL_FOR_BOOKSY_PAY,
            payment_type=booksy_pay,
        )
        receipt2, pr2 = self.make_receipt(
            txn=txn, status_code=rs.BOOKSY_PAY_SUCCESS, payment_type=booksy_pay, parent_pr=pr
        )
        txn.latest_receipt = receipt2
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
        )
        receipt3, _pr3 = self.make_receipt(
            txn=txn2, status_code=rs.PAYMENT_SUCCESS, payment_type=booksy_pay, parent_pr=pr2
        )
        txn2.latest_receipt = receipt3
        txn2.save()

        pr4 = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt3,
            payment_type=cash,
        )

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        payment_rows = resp.json['payment_rows']

        assert len(payment_rows) == 2
        assert payment_rows[1]['id'] == pr2.id
        assert payment_rows[1]['transaction_id'] == txn.id
        assert payment_rows[0]['id'] == pr4.id
        assert payment_rows[0]['transaction_id'] == txn2.id


@pytest.mark.django_db
class PaymentRowSummaryHandlerTestCase(BaseAsyncHTTPTest):
    # pylint: disable=expression-not-assigned

    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business, active=True)
        self.url = f'/business_api/me/businesses/{self.business.id}/pos/payment_rows_summary/'
        self.cash = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
        )
        self.credit_card = baker.make(
            PaymentType,
            code=PaymentTypeEnum.CREDIT_CARD,
            pos=self.pos,
        )
        self.scope_data = [
            [self.cash],
            [self.credit_card],
            [self.cash, self.cash],
            [self.credit_card, self.credit_card],
            [self.cash, self.credit_card],
        ]

    def make_receipt(self, payment_type, deleted=False):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            deleted=tznow() if deleted else None,
        )
        receipt = baker.make(
            Receipt,
            status_code=rs.PAYMENT_SUCCESS,
            transaction=txn,
        )
        payment_row = baker.make(
            PaymentRow,
            status=rs.PAYMENT_SUCCESS,
            receipt=receipt,
            payment_type=payment_type,
            parent_payment_row=None,
            amount=100,
        )
        txn.latest_receipt = receipt
        txn.save()
        return receipt, payment_row

    def make_failed_receipt(self, payment_type):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            status_code=rs.PAYMENT_FAILED,
            transaction=txn,
        )
        payment_row = baker.make(
            PaymentRow,
            status=rs.PAYMENT_FAILED,
            receipt=receipt,
            payment_type=payment_type,
            parent_payment_row=None,
            amount=100,
        )
        txn.latest_receipt = receipt
        txn.save()
        return receipt, payment_row

    @patch.object(Business, 'get_timezone')
    def test_get_day(self, get_timezone_mock):
        biz_tz = pytz.timezone('America/Los_Angeles')
        biz_tz._long_name = 'America/Los_Angeles'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        # 1 am in utc is 6 pm in Los Angeles day before
        # we want to test this because there was a bug
        # when providers in the US were getting wrong
        # transaction for a given day because of timezone offset
        date_from = datetime.datetime(2022, 10, 7, 1, 0, 0, tzinfo=datetime.timezone.utc)

        for day_index, payment_types in enumerate(self.scope_data):
            with freeze_time(date_from - datetime.timedelta(days=day_index)):
                [self.make_receipt(payment_type) for payment_type in payment_types]
                [self.make_failed_receipt(payment_type) for payment_type in payment_types]

        resp = self.fetch(self.url + '?scope=day', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert len(resp.json['tiles']) == 5
        assert all(tile['payment_types'] for tile in resp.json['tiles'])
        assert resp.json['scope'] == 'day'

        type_lengths = [len(tile['payment_types']) for tile in resp.json['tiles']]
        tile_totals = [tile['total'] for tile in resp.json['tiles']]
        spans = [tile['span'] for tile in resp.json['tiles']]
        froms = [tile['date_from'] for tile in resp.json['tiles']]
        tills = [tile['date_till'] for tile in resp.json['tiles']]
        assert type_lengths == [1, 1, 1, 1, 2]
        assert tile_totals == [100.0, 100.0, 200.0, 200.0, 200.0]
        assert spans == froms == tills

    @patch.object(Business, 'get_timezone')
    def test_get_month(self, get_timezone_mock):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        date_from = tznow()

        for month_index, payment_types in enumerate(self.scope_data):
            with freeze_time(date_from - relativedelta(months=+month_index)):
                [self.make_receipt(payment_type) for payment_type in payment_types]
                [self.make_failed_receipt(payment_type) for payment_type in payment_types]

        resp = self.fetch(self.url + '?scope=month', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert len(resp.json['tiles']) == 5
        assert all(tile['payment_types'] for tile in resp.json['tiles'])
        assert resp.json['scope'] == 'month'

        type_lengths = [len(tile['payment_types']) for tile in resp.json['tiles']]
        tile_totals = [tile['total'] for tile in resp.json['tiles']]
        assert type_lengths == [1, 1, 1, 1, 2]
        assert tile_totals == [100.0, 100.0, 200.0, 200.0, 200.0]

    @patch.object(Business, 'get_timezone')
    def test_get_custom(self, get_timezone_mock):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        custom_data = [
            [self.cash],
            [self.credit_card],
            [self.cash, self.cash],
            [self.credit_card, self.credit_card],
            [self.cash, self.credit_card],
            [self.cash],
            [self.cash],
            [self.cash],
            [self.cash],
            [self.cash],
        ]

        date_from = tznow()

        for day_index, payment_types in enumerate(custom_data):
            with freeze_time(date_from - datetime.timedelta(days=day_index)):
                [self.make_receipt(payment_type) for payment_type in payment_types]
                [self.make_failed_receipt(payment_type) for payment_type in payment_types]

        from_date = (date_from - datetime.timedelta(days=4)).strftime('%Y-%m-%d')
        till_date = date_from.strftime('%Y-%m-%d')

        resp = self.fetch(
            self.url + f'?scope=custom&date_from={from_date}&date_till={till_date}', method='GET'
        )
        assert resp.code == status.HTTP_200_OK
        assert len(resp.json['tiles']) == 1

        tile = resp.json['tiles'][0]
        assert tile['payment_types']
        assert tile['span'] == from_date + ' - ' + till_date
        assert tile['total'] == 800.0
        assert len(tile['payment_types']) == 2

        payment_codes = (payment_type['code'] for payment_type in tile['payment_types'])
        assert 'cash' in payment_codes
        assert 'credit_card' in payment_codes
        assert tile['date_from'] == from_date
        assert tile['date_till'] == till_date
        assert resp.json['scope'] == 'custom'

    @patch.object(Business, 'get_timezone')
    def test_summary_query_validation(self, get_timezone_mock):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz

        queries_results = (
            ("", None),
            ("  ", None),
            (None, None),
            ('nowy klient', 'nowy klient'),
            ('nowy klient ', 'nowy klient'),
            ('123456', '123456'),
        )
        for query, result in queries_results:
            data = {'scope': 'day', 'page': '1', 'per_page': '5', 'query': query}
            serializer = PaymentRowsSummarySerializer(data=data, context={'tz': biz_tz})
            assert serializer.is_valid()
            assert serializer.data['query'] == result

    @patch.object(Business, 'get_timezone')
    def test_get_should_not_include_deleted_transactions(self, get_timezone_mock):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        self.make_receipt(self.credit_card)
        self.make_receipt(self.cash, deleted=True)

        resp = self.fetch(self.url + '?scope=month', method='GET')

        assert resp.code == status.HTTP_200_OK
        assert len(resp.json['tiles']) == 1

        tile = resp.json['tiles'][0]
        payment_codes = {payment_type['code'] for payment_type in tile['payment_types']}
        assert 'cash' not in payment_codes  # related transaction is deleted
        assert 'credit_card' in payment_codes


@pytest.mark.django_db
class TipChoicesTestCase(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()

        pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)
        baker.make(Tip, pos=pos, default=True, rate=10)
        baker.make(Tip, pos=pos, rate=20)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=20)

        self.staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=baker.make(User),
        )

        self.url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'

    def test(self):
        body = {
            'dry_run': True,
            'transaction_type': 'P',
            'bookings': [
                {
                    'service_name': 'foo',
                    'item_price': 100,
                    'discount_rate': 50,
                    'commission_staffer_id': self.owner.id,
                },
                {
                    'service_name': 'bar',
                    'item_price': 100,
                    'commission_staffer_id': self.staffer.id,
                },
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']

        tip_choices = data['tip_choices']
        assert len(tip_choices) == 2
        assert tip_choices[0]['rate'] == '10.00'
        assert tip_choices[0]['amount_unformatted'] == '15.00'
        assert tip_choices[1]['rate'] == '20.00'
        assert tip_choices[1]['amount_unformatted'] == '30.00'

        staffer_tip_choices = data['staffer_tip_choices']
        assert len(staffer_tip_choices) == 2

        tip_choices_for_owner = staffer_tip_choices[str(self.owner.id)]
        assert len(tip_choices_for_owner) == 2
        assert tip_choices_for_owner[0]['rate'] == '10.00'
        assert tip_choices_for_owner[0]['amount_unformatted'] == '5.00'
        assert tip_choices_for_owner[1]['rate'] == '20.00'
        assert tip_choices_for_owner[1]['amount_unformatted'] == '10.00'

        tip_choices_for_staffer = staffer_tip_choices[str(self.staffer.id)]
        assert len(tip_choices_for_staffer) == 2
        assert tip_choices_for_staffer[0]['rate'] == '10.00'
        assert tip_choices_for_staffer[0]['amount_unformatted'] == '10.00'
        assert tip_choices_for_staffer[1]['rate'] == '20.00'
        assert tip_choices_for_staffer[1]['amount_unformatted'] == '20.00'

    def test_no_staffers_assigned(self):
        body = {
            'dry_run': True,
            'transaction_type': 'P',
            'bookings': [
                {
                    'service_name': 'foo',
                    'item_price': 100,
                    'discount_rate': 50,
                    'commission_staffer_id': UNASSIGNED_STAFFER_ID,
                },
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 201, resp.json

        data = resp.json['transaction']
        staffer_tip_choices = data['staffer_tip_choices']
        assert len(staffer_tip_choices) == 0


@pytest.mark.django_db
class BusinessTransactionsHandlerTapToPayTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=self.default_tax_rate,
            description="",
        )
        self.stock = baker.make(
            CommodityStockLevel,
            warehouse=self.warehouse,
            commodity=self.commodity,
            remaining_volume=10,
        )

        pos = baker.make(POS, business=self.business, active=True)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.TAP_TO_PAY, pos=pos)

        service_1 = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )

        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        self.booking_1 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings[0]

        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]

        self.body = {
            'payment_type_code': PaymentTypeEnum.TAP_TO_PAY,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                # crate Tr.Row for booking_1 and Tr.Row for Addon, which is in booking
                {
                    'booking_id': self.booking_1.id,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [],
            'addons': [],
        }

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_tap_to_pay_dry_run(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/dry_run/'

        resp = self.fetch(url, method='POST', body=self.body)

        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(len(resp.json['transaction']['rows']), 1)
        payment_type_choices = resp.json['transaction']['payment_type_choices']
        self.assertEqual(len(payment_type_choices), 1)
        self.assertEqual(payment_type_choices[0]['code'], PaymentTypeEnum.TAP_TO_PAY)


@pytest.mark.django_db
class BusinessTransactionsKeyedInPaymentEntryTestCase(BusinessTransactionsHandlerBaseTestClass):
    def setUp(self):
        super().setUp()
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        self.warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=self.default_tax_rate,
            description="",
        )
        self.stock = baker.make(
            CommodityStockLevel,
            warehouse=self.warehouse,
            commodity=self.commodity,
            remaining_volume=10,
        )

        pos = baker.make(
            POS,
            business=self.business,
            active=True,
        )
        self.pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        baker.make(
            StripeAccountHolder,
            account_holder_id=self.business_wallet.account_holder_id,
        )
        baker.make(StripeAccount, pos=self.business.pos)
        pos.pos_plans.add(self.pos_plan)

        baker.make(TaxRate, pos=pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.KEYED_IN_PAYMENT, pos=pos)

        service_1 = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )

        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        self.booking_1 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings[0]
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]

        self.body = {
            'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
            'payment_token': 'pm_1Q0PsIJvEtkwdCNYMSaVuRz6',
            'dry_run': False,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': self.booking_1.id,
                },
            ],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [],
            'addons': [],
        }

    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_keyed_in_payment_dry_run(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/dry_run/'

        resp = self.fetch(url, method='POST', body=self.body)

        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(len(resp.json['transaction']['rows']), 1)
        payment_type_choices = resp.json['transaction']['payment_type_choices']

        self.assertEqual(len(payment_type_choices), 1)
        self.assertEqual(payment_type_choices[0]['code'], PaymentTypeEnum.KEYED_IN_PAYMENT)

    @patch('stripe.PaymentIntent.retrieve')
    @patch('stripe.PaymentIntent.create')
    @patch.object(BusinessTransactionsHandler, 'business_with_advanced_staffer')
    def test_create_transaction_with_intent_keyed_in_payment(
        self, business_with_staffer_mock, mock_create, mock_retrieve
    ):
        payment_intent_mock = PaymentIntent(
            id='pi_123456789',
            status='requires_payment_method',
            amount=1000,
            currency='usd',
            client_secret=get_random_string(12),
            metadata={StripePaymentIntentMetadata.TERMINAL_PAYMENT: True},
        )
        mock_create.return_value = payment_intent_mock
        mock_retrieve.return_value = payment_intent_mock

        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        resp = self.fetch(url, method='POST', body=self.body)

        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(len(resp.json['transaction']['rows']), 1)
        self.assertEqual(
            resp.json['transaction']['payment_rows'][0]['payment_type_code'],
            PaymentTypeEnum.KEYED_IN_PAYMENT,
        )
        self.assertEqual(resp.json['transaction']['receipts'][0]['status_type'], 'pending')

        transaction = Transaction.objects.get(id=resp.json['transaction']['id'])
        url_payment_intent = (
            f'/business_api/me/businesses/{self.business.id}/stripe/payment_intent/?'
        )
        body = {
            'payment_row_id': transaction.payment_rows.first().id,
            'transaction_id': transaction.id,
        }
        resp_intent = self.fetch(url_payment_intent, method='POST', body=body)

        self.assertEqual(resp_intent.code, status.HTTP_200_OK)
        self.assertEqual(resp_intent.json['client_secret'], payment_intent_mock.client_secret)
