import datetime
import uuid
from unittest.mock import MagicMock

import mock
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.payments.enums import PayoutStatus, PayoutType
from lib.payment_providers.enums import PayoutMethodErrorCode
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.payments.dataclasses.financial_center import (
    ActionButton,
    FinancialCenterBusinessDetails,
    Balance,
    Payout,
    PayoutMethod,
    PayoutItem,
    Card,
    BankAccount,
)
from webapps.payments.enums import (
    KYCStatus,
    NewFinancialCenterActionButtons,
)
from webapps.pos.models import POS
from webapps.user.models import User


# pylint: disable=line-too-long


class FinancialCenterBaseTestCase(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.business = business_recipe.make()
        cls.pos = baker.make(
            POS,
            business=cls.business,
            _force_stripe_pba=True,
        )
        cls.resource = resource_recipe.make(
            business=cls.business,
            staff_user=cls.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )

    def _check_if_business_has_stripe_forced(self):
        self.pos._force_stripe_pba = False  # pylint: disable=protected-access
        self.pos.save()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'not_stripe_business',
                        'description': 'Financial center feature is available only for Stripe businesses.',
                    }
                ],
            },
        )


class FinancialCenterOverviewViewTestCase(FinancialCenterBaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.url = reverse(
            'payments__financial_center_overview',
            kwargs={'business_pk': cls.business.id},
        )

    @staticmethod
    def _get_payout_details(fast_payouts_active: bool) -> Payout:
        return Payout(
            next_payout_date=datetime.date(2025, 2, 13),
            payouts_active=True,
            fast_payouts_active=fast_payouts_active,
            payout_methods=[
                PayoutMethod(
                    card=None,
                    bank_account=BankAccount(
                        country='PL',
                        iban='PL **** 8210',
                        last_digits='8210',
                        routing_number='*********',
                    ),
                    is_default_for_regular_payouts=True,
                    is_default_for_fast_payouts=False,
                    error_code=None,
                ),
                PayoutMethod(
                    card=Card(
                        last_digits='5556',
                        expiry_year=2033,
                        expiry_month=3,
                        brand='Visa',
                    ),
                    bank_account=None,
                    is_default_for_regular_payouts=False,
                    is_default_for_fast_payouts=True,
                    error_code=PayoutMethodErrorCode.ERROR,
                ),
            ],
        )

    @mock.patch(
        'webapps.payments.views.financial_center.financial_center_overview.FinancialCenterService.get_overview_details'
    )
    def test_get__ok(self, mocked_overview_details):
        mocked_overview_details.return_value = FinancialCenterBusinessDetails(
            kyc_status=KYCStatus.ACTIVE,
            has_stripe_account=True,
            balance=Balance(
                total=1000000,
                available=800000,
                pending=200000,
                available_for_fast_payout=800000,
            ),
            payout=self._get_payout_details(True),
            action_buttons=[
                ActionButton(
                    type=NewFinancialCenterActionButtons.FAST_PAYOUT.value,
                    label=NewFinancialCenterActionButtons.FAST_PAYOUT.label,
                    enabled=True,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.value,
                    label=NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.label,
                    enabled=True,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.TAP_TO_PAY.value,
                    label=NewFinancialCenterActionButtons.TAP_TO_PAY.label,
                    enabled=True,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.value,
                    label=NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.label,
                    enabled=True,
                ),
            ],
        )
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.json(),
            {
                'kyc_status': 'active',
                'has_stripe_account': True,
                'balance': {
                    'total': '$10,000.00',
                    'available': '$8,000.00',
                    'pending': '$2,000.00',
                    'available_for_fast_payout': '$8,000.00',
                },
                'payout': {
                    'next_payout_date': 'February 13, 2025',
                    'payouts_active': True,
                    'payout_methods': [
                        {
                            'bank_account': {
                                'country': 'PL',
                                'iban': 'PL **** 8210',
                                'last_digits': '8210',
                                'routing_number': '*********',
                            },
                            'card': None,
                            'is_default_for_regular_payouts': True,
                            'is_default_for_fast_payouts': False,
                            'error_code': None,
                        },
                        {
                            'card': {
                                'last_digits': '5556',
                                'expiry_year': 2033,
                                'expiry_month': 3,
                                'brand': 'Visa',
                            },
                            'bank_account': None,
                            'is_default_for_regular_payouts': False,
                            'is_default_for_fast_payouts': True,
                            'error_code': 'error',
                        },
                    ],
                },
                'action_buttons': [
                    {
                        'type': NewFinancialCenterActionButtons.FAST_PAYOUT.value,
                        'label': NewFinancialCenterActionButtons.FAST_PAYOUT.label,
                        'enabled': True,
                    },
                    {
                        'type': NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.value,
                        'label': NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.label,
                        'enabled': True,
                    },
                    {
                        'type': NewFinancialCenterActionButtons.TAP_TO_PAY.value,
                        'label': NewFinancialCenterActionButtons.TAP_TO_PAY.label,
                        'enabled': True,
                    },
                    {
                        'type': NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.value,
                        'label': NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.label,
                        'enabled': True,
                    },
                ],
            },
        )

    @mock.patch(
        'webapps.payments.views.financial_center.financial_center_overview.FinancialCenterService.get_overview_details'
    )
    def test_get__ok_fast_payout_disabled(self, mocked_overview_details):
        mocked_overview_details.return_value = FinancialCenterBusinessDetails(
            kyc_status=KYCStatus.ACTIVE,
            has_stripe_account=True,
            balance=Balance(
                total=1000000,
                available=800000,
                pending=200000,
                available_for_fast_payout=None,
            ),
            payout=self._get_payout_details(False),
            action_buttons=[
                ActionButton(
                    type=NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.value,
                    label=NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.label,
                    enabled=True,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.TAP_TO_PAY.value,
                    label=NewFinancialCenterActionButtons.TAP_TO_PAY.label,
                    enabled=True,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.value,
                    label=NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.label,
                    enabled=True,
                ),
            ],
        )
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.json()['balance'],
            {
                'total': '$10,000.00',
                'available': '$8,000.00',
                'pending': '$2,000.00',
            },
        )
        self.assertEqual(
            response.json()['action_buttons'],
            [
                {
                    'type': NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.value,
                    'label': NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.label,
                    'enabled': True,
                },
                {
                    'type': NewFinancialCenterActionButtons.TAP_TO_PAY.value,
                    'label': NewFinancialCenterActionButtons.TAP_TO_PAY.label,
                    'enabled': True,
                },
                {
                    'type': NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.value,
                    'label': NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.label,
                    'enabled': True,
                },
            ],
        )

    @mock.patch(
        'webapps.payments.views.financial_center.financial_center_overview.FinancialCenterService.get_overview_details'  # pylint: disable=line-too-long
    )
    def test_get__ok_inactive_kyc(self, mocked_overview_details):
        mocked_overview_details.return_value = FinancialCenterBusinessDetails(
            kyc_status=KYCStatus.INACTIVE,
            has_stripe_account=True,
            balance=Balance(
                total=0,
                available=0,
                pending=0,
                available_for_fast_payout=None,
            ),
            payout=Payout(
                next_payout_date=None,
                payouts_active=False,
                fast_payouts_active=False,
                payout_methods=self._get_payout_details(False).payout_methods,
            ),
            action_buttons=[
                ActionButton(
                    type=NewFinancialCenterActionButtons.FAST_PAYOUT.value,
                    label=NewFinancialCenterActionButtons.FAST_PAYOUT.label,
                    enabled=False,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.value,
                    label=NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.label,
                    enabled=False,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.TAP_TO_PAY.value,
                    label=NewFinancialCenterActionButtons.TAP_TO_PAY.label,
                    enabled=False,
                ),
                ActionButton(
                    type=NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.value,
                    label=NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.label,
                    enabled=False,
                ),
            ],
        )
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.json(),
            {
                'kyc_status': 'inactive',
                'has_stripe_account': True,
                'balance': {'total': '$0.00', 'available': '$0.00', 'pending': '$0.00'},
                'payout': {
                    'next_payout_date': None,
                    'payouts_active': False,
                    'payout_methods': [
                        {
                            'card': None,
                            'bank_account': {
                                'country': 'PL',
                                'iban': 'PL **** 8210',
                                'last_digits': '8210',
                                'routing_number': '*********',
                            },
                            'is_default_for_regular_payouts': True,
                            'is_default_for_fast_payouts': False,
                            'error_code': None,
                        },
                        {
                            'card': {
                                'last_digits': '5556',
                                'expiry_year': 2033,
                                'expiry_month': 3,
                                'brand': 'Visa',
                            },
                            'bank_account': None,
                            'is_default_for_regular_payouts': False,
                            'is_default_for_fast_payouts': True,
                            'error_code': 'error',
                        },
                    ],
                },
                'action_buttons': [
                    {
                        'type': NewFinancialCenterActionButtons.FAST_PAYOUT.value,
                        'label': NewFinancialCenterActionButtons.FAST_PAYOUT.label,
                        'enabled': False,
                    },
                    {
                        'type': NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.value,
                        'label': NewFinancialCenterActionButtons.NO_SHOW_PROTECTION.label,
                        'enabled': False,
                    },
                    {
                        'type': NewFinancialCenterActionButtons.TAP_TO_PAY.value,
                        'label': NewFinancialCenterActionButtons.TAP_TO_PAY.label,
                        'enabled': False,
                    },
                    {
                        'type': NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.value,
                        'label': NewFinancialCenterActionButtons.BOOKSY_GIFT_CARDS.label,
                        'enabled': False,
                    },
                ],
            },
        )

    def test_get_400_no_stripe_forced(self):
        self._check_if_business_has_stripe_forced()


class FinancialCenterPayoutViewSetTestCase(FinancialCenterBaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.url = reverse(
            'payments__booksy_wallet_payouts',
            kwargs={'business_pk': cls.business.id},
        )

    @mock.patch(
        'webapps.payments.services.financial_center.FinancialCenterPayoutService.get_payouts_details'
    )
    @mock.patch(
        'webapps.payment_gateway.ports.PaymentGatewayPort.list_balance_transactions',
        return_value=MagicMock(),
    )
    @mock.patch(
        'webapps.payment_gateway.ports.PaymentGatewayPort.get_business_wallet',
        return_value=MagicMock(),
    )
    def test_list_payouts(
        self, mocked_get_wallet, mocked_list_balance_transactions, mocked_payouts_details
    ):
        mocked_payouts_details.side_effect = [
            [
                PayoutItem(
                    id=uuid.UUID('013907ca-9e71-4594-bbef-f7caf3a4aacf'),
                    amount=20000,
                    date='dummy_date',
                    status=PayoutStatus.IN_PAYMENT_PROCESSOR,
                    type=PayoutType.REGULAR,
                    group_by='mmm, yyyy',
                    expected_arrival_date='dummy_date',
                )
                for _ in range(3)
            ],
            [
                PayoutItem(
                    id=uuid.UUID('1860f5c2-4a06-42bd-8db2-bf3d3ad47d4f'),
                    amount=20000,
                    date='dummy_date',
                    status=PayoutStatus.IN_PAYMENT_PROCESSOR,
                    type=PayoutType.REGULAR,
                    group_by='mmm, yyyy',
                    expected_arrival_date='dummy_date',
                )
                for _ in range(2)
            ],
        ]
        response = self.client.get(self.url, data={'page': 1, 'per_page': 3})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 3)
        self.assertTrue(response.json()['has_next_page'])
        self.assertEqual(
            response.json()['results'][0],
            {
                'id': '013907ca-9e71-4594-bbef-f7caf3a4aacf',
                'amount': '$200.00',
                'date': 'dummy_date',
                'status': PayoutStatus.IN_PAYMENT_PROCESSOR,
                'type': PayoutType.REGULAR,
                'group_by': 'mmm, yyyy',
                'expected_arrival_date': 'dummy_date',
            },
        )

        response = self.client.get(self.url, data={'page': 2, 'per_page': 3})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 2)
        self.assertFalse(response.json()['has_next_page'])

    @mock.patch(
        'webapps.payments.services.financial_center.FinancialCenterPayoutService.get_payouts_details',
        return_value=[],
    )
    @mock.patch(
        'webapps.payment_gateway.ports.PaymentGatewayPort.list_balance_transactions',
        return_value=MagicMock(),
    )
    @mock.patch(
        'webapps.payment_gateway.ports.PaymentGatewayPort.get_business_wallet',
        return_value=MagicMock(),
    )
    def test_list_payouts_empty_list(
        self, mocked_get_wallet, mocked_list_balance_transactions, mocked_payouts_details
    ):
        response = self.client.get(self.url, data={'page': 1, 'per_page': 3})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['results'], [])
        self.assertFalse(response.json()['has_next_page'])

    def test_get_400_no_stripe_forced(self):
        self._check_if_business_has_stripe_forced()
