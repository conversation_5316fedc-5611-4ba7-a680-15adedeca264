from datetime import timed<PERSON><PERSON>
import mock
import pytest
from django.test import override_settings
from django.urls import reverse
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.feature_flag.feature.payment import (
    OTPCodeRequiredFlag,
    PayoutMethodChangeAllowedTimedeltaFlag,
)
from lib.payment_providers.entities import PortResponse
from lib.payment_providers.enums import ResponseEntityType
from lib.payments.enums import PaymentProviderCode, PayoutType
from lib.tests.utils import override_feature_flag, override_eppo_feature_flag
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.otp.settings import OTPStatus
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.tests.helpers import dummy_provider_account_details
from webapps.pos.models import POS
from webapps.stripe_integration.models import StripeAccount
from webapps.user.models import User

# pylint: disable=no-name-in-module
from webapps.session.booksy_auth.pb2.auth_pb2 import GenerateOTPResponse

# pylint: enable=no-name-in-module


class BaseProviderApiViewTestCase(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.business = business_recipe.make()
        cls.pos = baker.make(
            POS,
            business=cls.business,
            _force_stripe_pba=True,
        )
        cls.resource = resource_recipe.make(
            business=cls.business,
            staff_user=cls.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )


class CreateProviderAccountAPIViewTestCase(BaseProviderApiViewTestCase):
    @mock.patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_account')
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.get_provider_account_details'
    )
    def test_post(
        self,
        get_provider_account_details_mock,
        get_or_create_stripe_account_mock,
    ):
        get_or_create_stripe_account_mock.return_value = (StripeAccount(), True)
        get_provider_account_details_mock.return_value = PortResponse(
            entity=dummy_provider_account_details,
            entity_type=ResponseEntityType.ACCOUNT_HOLDER_DETAILS_ENTITY,
        )
        url = reverse(
            'payments__create_provider_account',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )
        PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )

        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        get_or_create_stripe_account_mock.assert_called_once()
        get_provider_account_details_mock.assert_called_once()
        self.assertEqual(response.json()['payments_enabled'], True)
        self.assertEqual(
            response.json()['payout_methods'][0]['bank_account']['last_digits'],
            "1234",
        )


class ProviderAccountDetailsAPIViewTestCase(BaseProviderApiViewTestCase):
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.get_provider_account_details'
    )
    def test_get(
        self,
        get_provider_account_details_mock,
    ):
        get_provider_account_details_mock.return_value = PortResponse(
            entity=dummy_provider_account_details,
            entity_type=ResponseEntityType.ACCOUNT_HOLDER_DETAILS_ENTITY,
        )
        url = reverse(
            'payments__provider_account_details',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )
        PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        get_provider_account_details_mock.assert_called_once()
        self.assertEqual(response.json()['payments_enabled'], True)
        self.assertEqual(
            response.json()['payout_methods'][0]['bank_account']['last_digits'],
            "1234",
        )


class CreatePayoutMethodAPIViewTestCase(BaseProviderApiViewTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=cls.business.id,
            statement_name='statement name',
        )

    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.add_provider_payout_method'
    )
    def test_post(
        self,
        add_provider_payout_method_mock,
    ):
        add_provider_payout_method_mock.return_value = PortResponse(
            entity=dummy_provider_account_details.payout_methods[0],
            entity_type=ResponseEntityType.PAYOUT_METHOD_ENTITY,
        )

        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        add_provider_payout_method_mock.assert_called_once()
        self.assertEqual(response.json()['bank_account']['last_digits'], "1234")

    @parameterized.expand(
        [
            (True, 1, True, True),  # global flag ON -> always allowed
            (False, 11, True, True),  # 1hr before allowed timedelta and business flag ON -> allowed
            (False, 11, False, True),  # 1hr before allowed timedelta and flag OFF -> allowed
            (False, 13, False, False),  # 1hr past allowed timedelta and flag OFF -> not allowed
            (False, 13, True, True),  # 1hr past allowed timedelta and business flag ON -> allowed
        ]
    )
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.add_provider_payout_method'
    )
    def test_post_is_payout_method_change_allowed(
        self,
        settings_value,
        timedelta_after_stripe_account_creation,
        payout_method_change_allowed,
        result,
        add_provider_payout_method_mock,
    ):
        stripe_account_holder = baker.make(
            'payment_providers.StripeAccountHolder',
            account_holder_id=self.wallet.account_holder_id,
        )
        add_provider_payout_method_mock.return_value = PortResponse(
            entity=dummy_provider_account_details.payout_methods[0],
            entity_type=ResponseEntityType.PAYOUT_METHOD_ENTITY,
        )
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}

        self.pos.payout_method_change_allowed = payout_method_change_allowed
        self.pos.save(update_fields=['payout_method_change_allowed'])

        with override_feature_flag({PayoutMethodChangeAllowedTimedeltaFlag.flag_name: 12}):
            with override_settings(BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED=settings_value):
                with freeze_time(
                    stripe_account_holder.created
                    + timedelta(hours=timedelta_after_stripe_account_creation)
                ):
                    response = self.client.post(url, data=data)
        if result:
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        else:
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.json()['errors'][0]['code'], 'not_allowed_exception')


class DeletePayoutMethodAPIViewTestCase(BaseProviderApiViewTestCase):
    @parameterized.expand([(True, True), (False, False)])
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.remove_provider_payout_method'
    )
    def test_delete(
        self,
        settings_value,
        result,
        remove_provider_payout_method_mock,
    ):
        remove_provider_payout_method_mock.return_value = None
        url = reverse(
            'payments__payout_method',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
                'payout_method_token': 'test_token',
            },
        )
        PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        with override_settings(BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED=settings_value):
            response = self.client.delete(url)
        if result:
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            remove_provider_payout_method_mock.assert_called_once()
        else:
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.json()['errors'][0]['code'], 'not_allowed_exception')
            remove_provider_payout_method_mock.assert_not_called()

    @pytest.mark.usefixtures('clean_otp_cache')
    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @override_eppo_feature_flag({OTPCodeRequiredFlag.flag_name: True})
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.remove_provider_payout_method'
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456'),
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.verify_otp_code',
        return_value=OTPStatus.VERIFIED,
    )
    def test_otp_delete(
        self,
        _verify_otp_code_mock,
        _get_otp_code_mock,
        remove_provider_payout_method_mock,
    ):
        remove_provider_payout_method_mock.return_value = None
        url = reverse(
            'payments__payout_method',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
                'payout_method_token': 'test_token',
            },
        )
        PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        with override_settings(BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED=True):
            response = self.client.delete(url, headers={'X-OTP-Code': '123456'})
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            remove_provider_payout_method_mock.assert_called_once()

    @pytest.mark.usefixtures('clean_otp_cache')
    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @override_eppo_feature_flag({OTPCodeRequiredFlag.flag_name: True})
    @override_settings(AUTOMATION_TESTS_ENV=True)
    def test_e2e_otp(
        self,
    ):
        url = reverse(
            'payments__payout_method',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
                'payout_method_token': 'test_token',
            },
        )
        PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        with override_settings(BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED=True):
            with self.assertRaises(RuntimeError):
                self.client.delete(url, headers={'X-OTP-Code': '123456'})
            response = self.client.delete(url, headers={'X-OTP-Code': '111111'})
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            response = self.client.delete(url, headers={'X-OTP-Code': '222222'})
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            resp = response.json()
            self.assertEqual(resp['errors'][0]['code'], 'otp_invalid')
            response = self.client.delete(url, headers={'X-OTP-Code': '333333'})
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            resp = response.json()
            self.assertEqual(resp['errors'][0]['code'], 'otp_banned')


class SetPayoutMethodAsDefaultAPIViewTestCase(BaseProviderApiViewTestCase):
    @parameterized.expand([(True, True), (False, False)])
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.set_provider_payout_method_as_default'
    )
    def test_post(
        self,
        settings_value,
        result,
        set_provider_payout_method_as_default_mock,
    ):
        set_provider_payout_method_as_default_mock.return_value = None
        url = reverse(
            'payments__payout_method_set_as_default',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
                'payout_method_token': 'test_token',
                'payout_type': PayoutType.FAST,
            },
        )
        PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        with override_settings(BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED=settings_value):
            response = self.client.post(url)
        if result:
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            set_provider_payout_method_as_default_mock.assert_called_once()
        else:
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.json()['errors'][0]['code'], 'not_allowed_exception')
            set_provider_payout_method_as_default_mock.assert_not_called()
