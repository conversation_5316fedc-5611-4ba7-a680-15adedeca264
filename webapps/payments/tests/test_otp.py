from unittest import TestCase
from unittest.mock import MagicMock

import mock
import pytest
from django.urls import reverse
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.feature_flag.feature.payment import OTPCodeRequiredFlag, OTPMinimumRequiredVersionFlag
from lib.payment_providers.entities import PortResponse
from lib.payment_providers.enums import ResponseEntityType
from lib.payments.enums import PaymentProviderCode
from lib.tests.utils import override_eppo_feature_flag
from lib.x_version_compatibility.compatibilities.otp import OTPChallengeSupportCompatibility
from lib.x_version_compatibility.typing import RequestType
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.consts import ANDROID, IPHONE
from webapps.otp.settings import OTPStatus
from webapps.payment_providers.tests.helpers import dummy_provider_account_details
from webapps.pos.models import POS
from webapps.user.models import User

# pylint: disable=no-name-in-module
from webapps.session.booksy_auth.pb2.auth_pb2 import GenerateOTPResponse

# pylint: enable=no-name-in-module


@override_eppo_feature_flag({OTPCodeRequiredFlag.flag_name: True})
class CreatePayoutMethodAPIViewTestCase(BaseBusinessApiTestCase):

    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.business = business_recipe.make()
        cls.pos = baker.make(
            POS,
            business=cls.business,
            _force_stripe_pba=True,
        )
        cls.resource = resource_recipe.make(
            business=cls.business,
            staff_user=cls.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )

    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @pytest.mark.usefixtures('clean_otp_cache')
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456'),
    )
    def test_empty_otp(self, get_otp_code_mock):
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'otp_required',
                        'type': 'validation',
                        'field': 'otp_code',
                        'description': 'Enter the 6-digit code sent to t*******@b*****.com',
                    },
                    {
                        "code": "otp_length",
                        "type": "digit",
                        "field": "otp_code",
                        "description": "6",
                    },
                    {
                        "code": "otp_description",
                        "type": "text",
                        "field": "otp_code",
                        "description": "Please enter your code within the next 10 minutes. "
                        "This helps us verify your identity and keep your account secure.",
                    },
                ]
            },
        )

    @pytest.mark.usefixtures('clean_otp_cache')
    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    def test_short_invalid_otp(self):
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data, headers={'X-OTP-Code': 12345})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'otp_invalid',
                        'type': 'validation',
                        'field': 'otp_code',
                        'description': 'Code incorrect, please check and try again or resend code.',
                    },
                    {
                        "code": "otp_length",
                        "type": "digit",
                        "field": "otp_code",
                        "description": "6",
                    },
                    {
                        "code": "otp_description",
                        "type": "text",
                        "field": "otp_code",
                        "description": "Please enter your code within the next 10 minutes. "
                        "This helps us verify your identity and keep your account secure.",
                    },
                    {
                        'code': 'otp_required',
                        'type': 'validation',
                        'field': 'otp_code',
                        'description': 'Enter the 6-digit code sent to t*******@b*****.com',
                    },
                ]
            },
        )

    @pytest.mark.usefixtures('clean_otp_cache')
    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    def test_string_invalid_otp(self):
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data, headers={'X-OTP-Code': 'invalid'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'otp_invalid',
                        'type': 'validation',
                        'field': 'otp_code',
                        'description': 'Code incorrect, please check and try again or resend code.',
                    },
                    {
                        "code": "otp_length",
                        "type": "digit",
                        "field": "otp_code",
                        "description": "6",
                    },
                    {
                        "code": "otp_description",
                        "type": "text",
                        "field": "otp_code",
                        "description": "Please enter your code within the next 10 minutes. "
                        "This helps us verify your identity and keep your account secure.",
                    },
                    {
                        'code': 'otp_required',
                        'type': 'validation',
                        'field': 'otp_code',
                        'description': 'Enter the 6-digit code sent to t*******@b*****.com',
                    },
                ]
            },
        )

    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @pytest.mark.usefixtures('clean_otp_cache')
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.add_provider_payout_method'
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456'),
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.verify_otp_code',
        return_value=OTPStatus.VERIFIED,
    )
    def test_string_valid_otp(
        self,
        _verify_otp_code_mock,
        _get_otp_code_mock,
        add_provider_payout_method_mock,
    ):
        add_provider_payout_method_mock.return_value = PortResponse(
            entity=dummy_provider_account_details.payout_methods[0],
            entity_type=ResponseEntityType.PAYOUT_METHOD_ENTITY,
        )
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data, headers={'X-OTP-Code': '123456'})
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @pytest.mark.usefixtures('clean_otp_cache')
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.add_provider_payout_method'
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456'),
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.verify_otp_code',
        return_value=OTPStatus.VERIFIED,
    )
    def test_otp_allow_window(
        self,
        _verify_otp_code_mock,
        _get_otp_code_mock,
        add_provider_payout_method_mock,
    ):
        add_provider_payout_method_mock.return_value = PortResponse(
            entity=dummy_provider_account_details.payout_methods[0],
            entity_type=ResponseEntityType.PAYOUT_METHOD_ENTITY,
        )
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data, headers={'X-OTP-Code': '123456'})
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # shouldn't ask otp_code
        response = self.client.post(url, data=data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @pytest.mark.usefixtures('clean_otp_cache')
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.add_provider_payout_method'
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.verify_otp_code',
        return_value=OTPStatus.VERIFIED,
    )
    @mock.patch('webapps.otp.views.OTPCodeMixin.send_otp_code_email')
    def test_no_email_sent_if_no_new_otp_code(
        self,
        mock_send_otp_code_email,
        _verify_otp_code_mock,
        _get_otp_code_mock,
        add_provider_payout_method_mock,
        #
    ):
        add_provider_payout_method_mock.return_value = PortResponse(
            entity=dummy_provider_account_details.payout_methods[0],
            entity_type=ResponseEntityType.PAYOUT_METHOD_ENTITY,
        )
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        _get_otp_code_mock.return_value = GenerateOTPResponse(otp_code='123456', created=False)

        response = self.client.post(url, data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'otp_required')
        mock_send_otp_code_email.assert_not_called()

    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @pytest.mark.usefixtures('clean_otp_cache')
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.add_provider_payout_method'
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456', status=OTPStatus.BLOCKED),
    )
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.verify_otp_code',
        return_value=OTPStatus.BLOCKED,
    )
    def test_blocked_user_cannot_generate_code(
        self,
        _verify_otp_code_mock,
        _get_otp_code_mock,
        add_provider_payout_method_mock,
    ):
        add_provider_payout_method_mock.return_value = PortResponse(
            entity=dummy_provider_account_details.payout_methods[0],
            entity_type=ResponseEntityType.PAYOUT_METHOD_ENTITY,
        )
        url = reverse(
            'payments__payout_methods',
            kwargs={
                'business_pk': self.business.id,
                'provider_code': PaymentProviderCode.STRIPE,
            },
        )

        data = {'token': 'test_token'}
        response = self.client.post(url, data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'otp_banned',
                        'type': 'validation',
                        'field': 'otp_code',
                        'description': 'For security reasons, due to three failed attempts to verify the account, further attempts will be blocked for one hour.',  # pylint: disable=line-too-long
                    },
                    {
                        'code': 'otp_length',
                        'type': 'digit',
                        'field': 'otp_code',
                        'description': '6',
                    },
                    {
                        'code': 'otp_description',
                        'type': 'text',
                        'field': 'otp_code',
                        'description': 'Please enter your code within the next 10 minutes. This helps us verify your identity and keep your account secure.',  # pylint: disable=line-too-long
                    },
                    {
                        'code': 'otp_required',
                        'description': 'Enter the 6-digit code sent to ' 't*******@b*****.com',
                        'field': 'otp_code',
                        'type': 'validation',
                    },
                ]
            },
        )


@override_eppo_feature_flag(
    {OTPMinimumRequiredVersionFlag.flag_name: {"ios": "3.50.1", "android": "3.50.1_410"}}
)
class TestOTPChallengeCompatibility(TestCase):
    @parameterized.expand(
        [
            (None, None, None, False),
            (BookingSources.CUSTOMER_APP, ANDROID, '1.2.3', False),
            (BookingSources.CUSTOMER_APP, IPHONE, '1.2.3', False),
            (BookingSources.BUSINESS_APP, ANDROID, '2.23.1_410', False),
            (BookingSources.BUSINESS_APP, ANDROID, '3.60.2', True),
            (BookingSources.BUSINESS_APP, IPHONE, '2.26.3', False),
            (BookingSources.BUSINESS_APP, IPHONE, '3.66.4', True),
        ]
    )
    def test_is_compatible(self, app_type, source_name, x_version, expected_result):
        booking_source = MagicMock()
        booking_source.configure_mock(app_type=app_type, name=source_name)
        request = MagicMock(
            spec=RequestType, headers={'X-Version': x_version}, booking_source=booking_source
        )

        self.assertEqual(OTPChallengeSupportCompatibility(request), expected_result)
