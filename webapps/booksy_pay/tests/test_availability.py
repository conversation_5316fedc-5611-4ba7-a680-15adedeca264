from datetime import datetime
from decimal import Decimal
from unittest.mock import patch, MagicMock

import pytest
from django.test import override_settings, TestCase
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized

from lib.tools import TZ_UTC
from webapps.booking.models import Appointment
from webapps.booking.my_booksy import MemberAppointment
from webapps.booking.tests.utils import create_appointment
from webapps.booksy_pay.availability import (
    BooksyPayAvailabilityChecker,
    get_appointments,
    PaymentRepository,
    PaymentRepositoryPOSv1,
)
from webapps.booksy_pay.types.availability import (
    BooksyPayAppointment,
    BooksyPayPOS,
)
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import PriceType
from webapps.business.models import (
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentRow,
    PaymentType,
    Receipt,
    Transaction,
)
from webapps.stripe_integration.baker_recipes import stripe_account_recipe
from webapps.stripe_integration.enums import StripeAccountStatus


class TestBooksyPayAvailabilityChecker(TestCase):
    def setUp(self):
        self.mock_repository = MagicMock(spec=PaymentRepository)
        self.appointment_ids = [1, 2, 3]
        self.checker = BooksyPayAvailabilityChecker(
            appointment_ids=self.appointment_ids,
            payment_repository=self.mock_repository,
        )

    def _check_all_ineligible(self, result):
        self.assertEqual(len(result), len(self.appointment_ids))
        for appointment_id in self.appointment_ids:
            self.assertIn(appointment_id, result)
            self.assertFalse(result[appointment_id].eligible)
            self.assertFalse(result[appointment_id].available)
            self.assertFalse(result[appointment_id].payment_window_open)

    @override_settings(POS__BOOKSY_PAY=False)
    def test_feature_toggle_disabled(self):
        result = self.checker.get_availability_info()
        self._check_all_ineligible(result)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.availability.get_appointments')
    def test_no_eligible_appointments(self, mock_get_appointments):
        mock_get_appointments.return_value = []
        result = self.checker.get_availability_info()

        self._check_all_ineligible(result)
        mock_get_appointments.asssert_called_once()

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.availability.get_appointments')
    def test_no_eligible_pos_settings(self, mock_get_appointments):
        mock_appointments = [
            BooksyPayAppointment(
                appointment_id=1, business_id=101, eligible=True, enters_payment_window=True
            ),
            BooksyPayAppointment(
                appointment_id=2, business_id=102, eligible=True, enters_payment_window=True
            ),
        ]
        mock_get_appointments.return_value = mock_appointments
        self.mock_repository.get_business_pos_map.return_value = {}

        result = self.checker.get_availability_info()
        self._check_all_ineligible(result)
        self.mock_repository.get_business_pos_map.assert_called_once()

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.availability.get_appointments')
    def test_successful_availability_check(self, mock_get_appointments):
        mock_appointments = [
            BooksyPayAppointment(
                appointment_id=1, business_id=101, eligible=True, enters_payment_window=True
            ),
            BooksyPayAppointment(
                appointment_id=2, business_id=102, eligible=True, enters_payment_window=False
            ),
        ]
        mock_get_appointments.return_value = mock_appointments
        mock_pos_settings = {
            101: BooksyPayPOS(pos_id=201, business_id=101, eligible=True, available=True),
            102: BooksyPayPOS(pos_id=202, business_id=102, eligible=True, available=True),
        }
        self.mock_repository.get_business_pos_map.return_value = mock_pos_settings

        # Mock no conflicting transactions
        self.mock_repository.get_appointment_ids_with_conflicting_txn.return_value = set()

        result = self.checker.get_availability_info()

        self.mock_repository.get_business_pos_map.assert_called_once()
        self.mock_repository.get_appointment_ids_with_conflicting_txn.assert_called_once()

        # Verify appointment 1 is eligible and in payment window
        self.assertTrue(result[1].eligible)
        self.assertTrue(result[1].available)
        self.assertTrue(result[1].payment_window_open)

        # Verify appointment 2 is eligible but not in payment window
        self.assertTrue(result[2].eligible)
        self.assertTrue(result[2].available)
        self.assertFalse(result[2].payment_window_open)

        # Verify appointment 3 is ineligible (not in mock_appointments)
        self.assertFalse(result[3].eligible)
        self.assertFalse(result[3].available)
        self.assertFalse(result[3].payment_window_open)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.availability.get_appointments')
    def test_conflicting_transactions(self, mock_get_appointments):
        mock_appointments = [
            BooksyPayAppointment(
                appointment_id=1, business_id=101, eligible=True, enters_payment_window=True
            ),
            BooksyPayAppointment(
                appointment_id=2, business_id=102, eligible=True, enters_payment_window=True
            ),
        ]
        mock_get_appointments.return_value = mock_appointments

        mock_pos_settings = {
            101: BooksyPayPOS(pos_id=201, business_id=101, eligible=True, available=True),
            102: BooksyPayPOS(pos_id=202, business_id=102, eligible=True, available=True),
        }
        self.mock_repository.get_business_pos_map.return_value = mock_pos_settings

        # Mock conflicting transaction for appointment 1
        self.mock_repository.get_appointment_ids_with_conflicting_txn.return_value = {1}

        result = self.checker.get_availability_info()
        self.mock_repository.get_business_pos_map.assert_called_once()
        self.mock_repository.get_appointment_ids_with_conflicting_txn.assert_called_once()

        # Verify appointment 1 is ineligible due to conflicting transaction
        self.assertFalse(result[1].eligible)
        self.assertFalse(result[1].available)
        self.assertFalse(result[1].payment_window_open)

        # Verify appointment 2 is eligible
        self.assertTrue(result[2].eligible)
        self.assertTrue(result[2].available)
        self.assertTrue(result[2].payment_window_open)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.availability.get_appointments')
    def test_mixed_eligibility(self, mock_get_appointments):
        mock_appointments = [
            BooksyPayAppointment(
                appointment_id=1, business_id=101, eligible=True, enters_payment_window=True
            ),
            # Appointment 2 is not returned by get_appointments (ineligible)
        ]
        mock_get_appointments.return_value = mock_appointments

        # Mock POS settings
        mock_pos_settings = {
            101: BooksyPayPOS(pos_id=201, business_id=101, eligible=True, available=True),
        }
        self.mock_repository.get_business_pos_map.return_value = mock_pos_settings

        # Mock no conflicting transactions
        self.mock_repository.get_appointment_ids_with_conflicting_txn.return_value = set()

        result = self.checker.get_availability_info()
        self.mock_repository.get_business_pos_map.assert_called_once()
        self.mock_repository.get_appointment_ids_with_conflicting_txn.assert_called_once()

        # Verify appointment 1 is eligible
        self.assertTrue(result[1].eligible)
        self.assertTrue(result[1].available)
        self.assertTrue(result[1].payment_window_open)

        # Verify appointments 2 and 3 are ineligible
        self.assertFalse(result[2].eligible)
        self.assertFalse(result[2].available)
        self.assertFalse(result[2].payment_window_open)

        self.assertFalse(result[3].eligible)
        self.assertFalse(result[3].available)
        self.assertFalse(result[3].payment_window_open)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.availability.get_appointments')
    def test_pos_available_but_payment_window_closed(self, mock_get_appointments):
        mock_appointments = [
            BooksyPayAppointment(
                appointment_id=1,
                business_id=101,
                eligible=True,
                enters_payment_window=False,  # Payment window closed
            ),
        ]
        mock_get_appointments.return_value = mock_appointments

        # Mock POS settings (available)
        mock_pos_settings = {
            101: BooksyPayPOS(pos_id=201, business_id=101, eligible=True, available=True),
        }
        self.mock_repository.get_business_pos_map.return_value = mock_pos_settings

        # Mock no conflicting transactions
        self.mock_repository.get_appointment_ids_with_conflicting_txn.return_value = set()

        result = self.checker.get_availability_info()
        self.mock_repository.get_business_pos_map.assert_called_once()
        self.mock_repository.get_appointment_ids_with_conflicting_txn.assert_called_once()

        # Verify appointment is eligible and available but payment window is closed
        self.assertTrue(result[1].eligible)
        self.assertTrue(result[1].available)
        self.assertFalse(result[1].payment_window_open)


@pytest.mark.django_db
@pytest.mark.parametrize(
    'total_value, appt_status, total_type, is_booksy_gift_card_appointment, expected_result',
    [
        (Decimal('25.00'), Appointment.STATUS.ACCEPTED, PriceType.FIXED, False, True),
        (Decimal('25.00'), Appointment.STATUS.FINISHED, PriceType.FIXED, False, True),
        (Decimal('0.20'), Appointment.STATUS.ACCEPTED, PriceType.FIXED, False, False),
        (Decimal('25.00'), Appointment.STATUS.CANCELED, PriceType.FIXED, False, False),
        (Decimal('25.00'), Appointment.STATUS.ACCEPTED, PriceType.STARTS_AT, False, False),
        (Decimal('25.00'), Appointment.STATUS.ACCEPTED, PriceType.FIXED, True, False),
    ],
)
def test_get_appointments_eligible(
    total_value, appt_status, total_type, is_booksy_gift_card_appointment, expected_result
):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        total_value=total_value,
        type=Appointment.TYPE.CUSTOMER,
        status=appt_status,
        total_type=total_type,
        is_booksy_gift_card_appointment=is_booksy_gift_card_appointment,
    )

    result = get_appointments([appointment.id], filter_only_eligible=False)
    assert len(result) == 1
    assert result[0].eligible == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    'nsp, expected_result',
    [
        (True, False),
        (False, True),
    ],
)
def test_get_appointments_eligible_nsp(nsp, expected_result):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    if nsp:
        baker.make(ServiceVariantPayment, service_variant=service_variant)

    appointment_params = dict(
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
    )

    appointment = create_appointment(
        [subbooking_params],
        **appointment_params,
    )
    result = get_appointments([appointment.id], filter_only_eligible=False)
    assert len(result) == 1
    assert result[0].eligible == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    'nsp_trusted, expected_result',
    [
        (True, True),
        (False, False),
    ],
)
def test_get_appointments_eligible_nsp_trusted(nsp_trusted, expected_result):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    baker.make(ServiceVariantPayment, service_variant=service_variant)

    appointment_params = dict(
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
        booked_for__trusted=nsp_trusted,
    )

    appointment = create_appointment(
        [subbooking_params],
        **appointment_params,
    )
    result = get_appointments([appointment.id], filter_only_eligible=False)
    assert len(result) == 1
    assert result[0].eligible == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    'nsp_trusted, expected_result',
    [
        (True, True),
        (False, False),
    ],
)
def test_get_appointments_eligible_nsp_trusted_for_family_and_friends(nsp_trusted, expected_result):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    baker.make(ServiceVariantPayment, service_variant=service_variant)

    appointment_params = dict(
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
        booked_for__trusted=nsp_trusted,
    )

    appointment = create_appointment(
        [subbooking_params],
        **appointment_params,
    )

    baker.make(
        MemberAppointment, booked_for=appointment.booked_for, booked_by=appointment.booked_for
    )
    result = get_appointments([appointment.id], filter_only_eligible=False)
    assert len(result) == 1
    assert result[0].eligible == expected_result


@pytest.mark.django_db
@freeze_time(datetime(2025, 3, 1))
def test_get_appointments_enters_payment_window():
    appointment1 = create_appointment(
        [
            dict(
                booked_from=datetime(2025, 3, 5, 10, 30, tzinfo=TZ_UTC),
                booked_till=datetime(2025, 3, 5, 10, 45, tzinfo=TZ_UTC),
            )
        ],
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
    )
    appointment2 = create_appointment(
        [
            dict(
                booked_from=datetime(2025, 3, 11, 10, 30, tzinfo=TZ_UTC),
                booked_till=datetime(2025, 3, 11, 10, 45, tzinfo=TZ_UTC),
            )
        ],
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.CANCELED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
    )
    result = get_appointments([appointment1.id, appointment2.id], filter_only_eligible=False)
    assert len(result) == 2
    result_map = {a.appointment_id: a for a in result}
    assert result_map[appointment1.id].enters_payment_window is True
    assert result_map[appointment2.id].enters_payment_window is False


@pytest.mark.django_db
@pytest.mark.parametrize(
    'filter_only_eligible, result_count',
    [
        (True, 1),
        (False, 2),
    ],
)
def test_get_appointments_filter_only_eligible(filter_only_eligible, result_count):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=25,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment1 = create_appointment(
        [subbooking_params],
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
    )
    appointment2 = create_appointment(
        [subbooking_params],
        total_value=Decimal('25.00'),
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.CANCELED,
        total_type=PriceType.FIXED,
        is_booksy_gift_card_appointment=False,
    )
    result = get_appointments(
        [appointment1.id, appointment2.id], filter_only_eligible=filter_only_eligible
    )
    assert len(result) == result_count


class TestPaymentRepositoryPOSv1(TestCase):
    """Tests for the PaymentRepositoryPOSv1 class without mocking database calls."""

    def setUp(self):
        self.business_1 = business_recipe.make()
        self.business_2 = business_recipe.make()

    @parameterized.expand(
        [
            (True, 1),
            (False, 2),
        ],
    )
    def test_get_business_pos_map_filter_only_eligible(self, filter_only_eligible, result_count):
        pos_1 = pos_recipe.make(
            business=self.business_1,
            active=True,
            booksy_pay_enabled=True,
        )
        pos_recipe.make(
            business=self.business_2,
            active=True,
            booksy_pay_enabled=False,
        )
        stripe_account_recipe.make(pos=pos_1, status=StripeAccountStatus.VERIFIED)
        result = PaymentRepositoryPOSv1.get_business_pos_map(
            business_ids=[self.business_1.id, self.business_2.id],
            filter_only_eligible=filter_only_eligible,
        )
        self.assertEqual(len(result), result_count)

    @parameterized.expand(
        [
            (True, True, True, True, True, True),
            (True, True, False, True, True, False),
            (False, True, True, True, False, False),
            (True, False, True, True, False, False),
            (True, True, True, False, False, False),
        ],
    )
    def test_get_business_pos_map(
        self,
        active,
        pos_refactor_enabled,
        booksy_pay_enabled,
        stripe_account,
        expected_eligible,
        expected_available,
    ):
        pos = pos_recipe.make(
            business=self.business_1,
            active=active,
            pos_refactor_stage2_enabled=pos_refactor_enabled,
            booksy_pay_enabled=booksy_pay_enabled,
        )
        if stripe_account:
            stripe_account_recipe.make(pos=pos, status=StripeAccountStatus.VERIFIED)

        result = PaymentRepositoryPOSv1.get_business_pos_map(
            business_ids=[self.business_1.id],
            filter_only_eligible=False,
        )
        self.assertEqual(len(result), 1)
        self.assertEqual(result[self.business_1.id].eligible, expected_eligible)
        self.assertEqual(result[self.business_1.id].available, expected_available)

    def test_get_appointment_ids_with_conflicting_txn__no_transactions(self):
        pos_recipe.make(
            business=self.business_1,
            active=True,
            booksy_pay_enabled=True,
        )
        appointment = create_appointment(business=self.business_1)
        result = PaymentRepositoryPOSv1.get_appointment_ids_with_conflicting_txn(
            appointment_ids=[appointment.id]
        )
        self.assertEqual(len(result), 0)

    @parameterized.expand(
        [
            (PaymentTypeEnum.BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS),
            (PaymentTypeEnum.BOOKSY_PAY, receipt_status.CALL_FOR_BOOKSY_PAY),
            (PaymentTypeEnum.PREPAYMENT, receipt_status.PREPAYMENT_SUCCESS),
            (PaymentTypeEnum.PREPAYMENT, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS),
            (PaymentTypeEnum.PAY_BY_APP, receipt_status.CHARGEBACK),
            (PaymentTypeEnum.PAY_BY_APP, receipt_status.PAYMENT_SUCCESS),
            (PaymentTypeEnum.PREPAYMENT, receipt_status.CALL_FOR_PREPAYMENT),
        ]
    )
    def test_get_appointment_ids_with_conflicting_txn__with_transactions(
        self,
        payment_type_code,
        receipt_status_code,
    ):
        pos = pos_recipe.make(
            business=self.business_1,
            active=True,
            booksy_pay_enabled=True,
        )
        payment_type = baker.make(
            PaymentType,
            code=payment_type_code,
            pos=pos,
        )
        appointment = create_appointment(business=self.business_1)
        txn = baker.make(
            Transaction,
            appointment=appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            pos=pos,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status_code,
            payment_type=payment_type,
        )
        self.payment_row = baker.make(
            PaymentRow,
            payment_type=payment_type,
            receipt=receipt,
            status=receipt_status_code,
            tip_amount=Decimal('0.00'),
        )
        txn.latest_receipt = receipt
        txn.save()
        result = PaymentRepositoryPOSv1.get_appointment_ids_with_conflicting_txn(
            appointment_ids=[appointment.id]
        )
        self.assertEqual(result, {appointment.id})
