import typing as t
from dataclasses import dataclass

from django.conf import settings
from django.http import Http404
from django.shortcuts import render

from bo_obs.datadog.enums import BooksyTeams

from lib.serializers import safe_get
from webapps.admin_extra.custom_permissions_classes import View
from webapps.booking.enums import AppointmentStatusChoices
from webapps.booking.models import Appointment, SubBooking
from webapps.business.enums import PriceType
from webapps.pos.enums import receipt_status
from webapps.pos.models import POS
from webapps.pos.tools import get_minimal_pba_amount
from webapps.pos.utils import is_call_for_status, is_no_show_protection_active


@dataclass(slots=True)
class Condition:
    title: str
    value: t.Any
    passed: bool = False

    @property
    def color(self) -> str:
        return 'green' if self.passed else 'red'


@dataclass(slots=True)
class BooleanValueCondition(Condition):
    negation: bool = False

    def _set_passed(self):
        value = bool(self.value)
        self.passed = not value if self.negation else value

    def __post_init__(self):
        self._set_passed()


class BooksyPayAppointmentView(View):
    booksy_teams = (BooksyTeams.PAYMENT_NEXUS,)
    permission_required = ()
    template = 'admin/custom_views/booksy_pay.html'

    def get(self, request, appointment_id: int, *args, **kwargs):
        # Here we query SubBooking model in order to call prefetch_payment_and_deposit
        subbooking = (
            SubBooking.objects.select_related(
                'appointment__business',
            )
            .prefetch_payment_and_deposit()
            .filter(appointment_id=appointment_id)
            .first()
        )
        if not subbooking:
            raise Http404(f'Appointment with id {appointment_id} does not exists')

        appointment: Appointment = subbooking.appointment
        pos: POS = appointment.business.pos

        # probably will be changed https://booksy.atlassian.net/browse/CE-554
        is_no_show = is_no_show_protection_active(
            business_id=appointment.business_id,
            service_variants_ids=[
                subbooking.service_variant_id for subbooking in appointment.subbookings
            ],
        )

        paid = subbooking.paid  # cached
        payment, _ = subbooking.get_payment_and_deposit()  # cached
        chargeback_occurred = (
            payment and payment.latest_receipt.status_code in receipt_status.CHARGEBACK_STATUSES
        ) or False
        return render(
            request,
            self.template,
            context={
                'appointment_id': appointment.id,
                'appointment': (
                    BooleanValueCondition(
                        title='Is Booksy Pay available',
                        value=appointment.is_booksy_pay_available,
                    ),
                    BooleanValueCondition(
                        title='Is Booksy Pay payment window open',
                        value=appointment.is_booksy_pay_payment_window_open,
                    ),
                    Condition(
                        title='status',
                        value=(
                            AppointmentStatusChoices(appointment.status).label
                            if appointment.status
                            else None
                        ),
                        passed=(
                            appointment.status
                            in (Appointment.STATUS.ACCEPTED, Appointment.STATUS.FINISHED)
                        ),
                    ),
                    Condition(
                        title='total_type',
                        value=(
                            PriceType(appointment.total_type).label
                            if appointment.total_type
                            else None
                        ),
                        passed=appointment.total_type == PriceType.FIXED,
                    ),
                    Condition(
                        title='total_value',
                        value=appointment.total_value,
                        passed=appointment.total_value
                        and appointment.total_value > get_minimal_pba_amount(),
                    ),
                    BooleanValueCondition(
                        title='paid',
                        value=paid,
                        negation=True,
                    ),
                    BooleanValueCondition(
                        title='paid_by_bp',
                        value=appointment.is_paid_by_booksy_pay,
                        negation=True,
                    ),
                    BooleanValueCondition(
                        title='is_call_for_status',
                        value=is_call_for_status(appointment.id),
                        negation=True,
                    ),
                    BooleanValueCondition(
                        title='is_no_show',
                        value=is_no_show,
                        negation=True,
                    ),
                    BooleanValueCondition(
                        title='chargeback_occurred',
                        value=chargeback_occurred,
                        negation=True,
                    ),
                    BooleanValueCondition(
                        title='is_prepaid',
                        value=appointment.is_prepaid,
                        negation=True,
                    ),
                    BooleanValueCondition(
                        title='is_booksy_gift_card_appointment',
                        value=appointment.is_booksy_gift_card_appointment,
                        negation=True,
                    ),
                ),
                'business_id': appointment.business.id,
                'business': (
                    BooleanValueCondition(
                        title='POS__BOOKSY_PAY',
                        value=settings.POS__BOOKSY_PAY,
                    ),
                    BooleanValueCondition(
                        title='Is Booksy Pay available',
                        value=appointment.business.booksy_pay_available,
                    ),
                    BooleanValueCondition(
                        title='pos_enabled',
                        value=appointment.business.pos_enabled,
                    ),
                    BooleanValueCondition(
                        title='booksy_pay_enabled',
                        value=safe_get(pos, ['booksy_pay_enabled']),
                    ),
                    BooleanValueCondition(
                        title='stripe_kyc_completed',
                        value=safe_get(pos, ['stripe_kyc_completed']),
                    ),
                ),
            },
        )
