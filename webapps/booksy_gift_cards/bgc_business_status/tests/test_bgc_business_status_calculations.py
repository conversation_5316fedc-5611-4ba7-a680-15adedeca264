from unittest import mock
from unittest.mock import MagicMock

import pytest
from django.test import TestCase
from django.test import override_settings
from django_socio_grpc.tests.grpc_test_utils.fake_grpc import FakeRpcError
from grpc import StatusCode
from model_bakery import baker
from parameterized import parameterized

from lib.payment_providers.enums import ProviderAccountHolderStatus
from webapps.booksy_gift_cards.bgc_business_status.enums import BGCBusinessStatus
from webapps.booksy_gift_cards.bgc_business_status.protobufs.bgc_business_status_pb2 import (  # pylint: disable=no-name-in-module
    BGCBusinessStatusRequest,
)
from webapps.booksy_gift_cards.bgc_business_status.protobufs.bgc_business_status_pb2_grpc import (
    BGCBusinessStatusStub,
)
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.enums import StaffAccessLevels
from webapps.business.models import Business
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.pos.baker_recipes import pos_recipe
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.stripe_integration.models import StripeAccount
from webapps.user.baker_recipes import user_recipe


# pylint: disable=line-too-long
@pytest.mark.grpc_api
@pytest.mark.django_db
@override_settings(API_GRPC=True)
class BGCBusinessStatusCalculationsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()
        cls.biz_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=cls.business.id,
            statement_name=cls.business.name,
        )
        cls.pos = pos_recipe.make(
            business=cls.business,
            _force_stripe_pba=True,
        )
        cls.other_business_worker = resource_recipe.make(
            business=business_recipe.make(),
            staff_user=user_recipe.make(),
            staff_access_level=StaffAccessLevels.RECEPTION,
        )
        cls.owner = resource_recipe.make(
            business=cls.business,
            staff_user=cls.business.owner,
            staff_access_level=StaffAccessLevels.OWNER,
        )
        cls.business.booksy_gift_cards_settings.accept_booksy_gift_cards = True
        cls.business.booksy_gift_cards_settings.accept_booksy_gift_cards_manually_disabled = False
        cls.business.booksy_gift_cards_settings.save()
        super().setUpTestData()

    @pytest.fixture(autouse=True)
    def _get_booksy_payment_stub(self, test_channel):
        self.channel = test_channel
        self.stub = BGCBusinessStatusStub(self.channel)

    def test_user_has_no_permission(self):
        request = BGCBusinessStatusRequest(
            business_id=self.business.id, user_id=self.other_business_worker.staff_user.id
        )

        with pytest.raises(FakeRpcError) as err:
            self.stub.GetBGCBusinessStatus(request)
        self.assertEqual(err.value.args[0], StatusCode.PERMISSION_DENIED)
        self.assertEqual(
            err.value.args[1],
            f'User with ID {self.other_business_worker.staff_user.id} is not a business ID {self.business.id} worker.',
        )

    def test_business_has_no_stripe_forced(self):
        self.business.status = Business.Status.PAID
        self.business.save()

        self.pos._force_stripe_pba = False  # pylint: disable=protected-access
        self.pos.pos_refactor_stage2_enabled = False
        self.pos.save()

        request = BGCBusinessStatusRequest(
            business_id=self.business.id, user_id=self.business.owner.id
        )

        response = self.stub.GetBGCBusinessStatus(request)

        self.assertEqual(response.bgc_business_status, BGCBusinessStatus.DISABLED)

    def test_business_has_no_subscription(self):
        self.business.status = Business.Status.TRIAL
        self.business.save()

        request = BGCBusinessStatusRequest(
            business_id=self.business.id, user_id=self.business.owner.id
        )

        response = self.stub.GetBGCBusinessStatus(request)

        self.assertEqual(response.bgc_business_status, BGCBusinessStatus.NO_SUBSCRIPTION)

    def test_business_has_no_stripe_kyc(self):
        self.business.status = Business.Status.PAID
        self.business.save()
        request = BGCBusinessStatusRequest(
            business_id=self.business.id, user_id=self.business.owner.id
        )

        response = self.stub.GetBGCBusinessStatus(request)
        self.assertEqual(response.bgc_business_status, BGCBusinessStatus.NOT_KYC)

    @parameterized.expand(
        [
            (ProviderAccountHolderStatus.VERIFIED.value, BGCBusinessStatus.ACTIVE.value),
            (ProviderAccountHolderStatus.TURNED_OFF.value, BGCBusinessStatus.DISABLED.value),
            (ProviderAccountHolderStatus.NOT_VERIFIED.value, BGCBusinessStatus.NOT_KYC.value),
            (
                ProviderAccountHolderStatus.VERIFICATION_PENDING.value,
                BGCBusinessStatus.PENDING_KYC.value,
            ),
        ]
    )
    @mock.patch(
        'webapps.booksy_gift_cards.bgc_business_status.servicer.bgc_business_status.get_stripe_account_details'
    )
    def test_business_has_different_kyc_status(
        self,
        stripe_status,
        bgc_status,
        mocked_stripe_account_details,
    ):
        self.business.status = Business.Status.PAID
        self.business.save()

        baker.make(StripeAccount, pos=self.pos, status=StripeAccountStatus.VERIFIED)

        if stripe_status == ProviderAccountHolderStatus.VERIFIED.value:
            self.business.booksy_gift_cards_settings.accept_booksy_gift_cards = True
            self.business.booksy_gift_cards_settings.accept_booksy_gift_cards_manually_disabled = (
                False
            )
            self.business.booksy_gift_cards_settings.save()

        mocked_stripe_account_details.return_value = MagicMock(status=stripe_status)

        request = BGCBusinessStatusRequest(
            business_id=self.business.id, user_id=self.business.owner.id
        )

        response = self.stub.GetBGCBusinessStatus(request)
        self.assertEqual(response.bgc_business_status, bgc_status)

    @mock.patch(
        'webapps.booksy_gift_cards.bgc_business_status.servicer.bgc_business_status.get_stripe_account_details'
    )
    def test_business_has_kyc_but_bgc_manually_disabled(
        self,
        mocked_stripe_account_details,
    ):
        self.business.status = Business.Status.PAID
        self.business.save()

        mocked_stripe_account_details.return_value = MagicMock(
            status=ProviderAccountHolderStatus.VERIFIED.value
        )
        baker.make(StripeAccount, pos=self.pos, status=StripeAccountStatus.VERIFIED)

        self.business.booksy_gift_cards_settings.accept_booksy_gift_cards_manually_disabled = True
        self.business.booksy_gift_cards_settings.save()

        request = BGCBusinessStatusRequest(
            business_id=self.business.id, user_id=self.business.owner.id
        )

        response = self.stub.GetBGCBusinessStatus(request)
        self.assertEqual(response.bgc_business_status, BGCBusinessStatus.DISABLED.value)
