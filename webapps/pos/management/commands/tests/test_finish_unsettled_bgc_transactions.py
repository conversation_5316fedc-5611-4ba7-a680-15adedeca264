import uuid
from datetime import datetime
from unittest import TestCase

import pytest
from mock import patch

from lib.feature_flag.feature.payment import UpdateBGCPaymentRowIfPBASucceededFlag
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.entities import BasketPaymentEntity
from lib.point_of_sale.enums import (
    PaymentMethodType,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketPaymentSource,
)
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.baker_recipes import appointment_recipe
from webapps.pos.baker_recipes import (
    payment_row_recipe,
    payment_type_recipe,
    receipt_recipe,
    posplan_recipe,
    transaction_recipe,
    pos_recipe,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status, POSPlanPaymentTypeEnum
from webapps.pos.management.commands.finish_unsettled_bgc_transactions import Command
from webapps.pos.models import Transaction


@pytest.mark.django_db
@override_eppo_feature_flag({UpdateBGCPaymentRowIfPBASucceededFlag.flag_name: True})
class TestFinishUnsettledBGCTransactionCommand(TestCase):

    def setUp(self):
        super().setUp()
        pos_plan = posplan_recipe.make(
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.pos = pos_recipe.make()
        self.pos.pos_plans.add(pos_plan)
        self.pba = payment_type_recipe.make()
        self.bgc = payment_type_recipe.make(code=PaymentTypeEnum.BOOKSY_GIFT_CARD, pos=self.pos)
        self.split = payment_type_recipe.make(code=PaymentTypeEnum.SPLIT, pos=self.pos)
        self.blik = payment_type_recipe.make(code=PaymentTypeEnum.BLIK, pos=self.pos)
        self.cash = payment_type_recipe.make(code=PaymentTypeEnum.CASH, pos=self.pos)
        self.appt = appointment_recipe.make(
            is_booksy_gift_card_appointment=True,
        )

    @patch('webapps.point_of_sale.ports.BasketPaymentPort.get_basket_payment_entity')
    def test_finish_unsettled_bgc_transactions_success(self, mocked_get_basket_payment_entity):
        mocked_get_basket_payment_entity.return_value = self._get_basket_payment_mock(
            BasketPaymentStatus.SUCCESS
        )
        bgc_txn = transaction_recipe.make(
            pos=self.pos,
            appointment=self.appt,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            transaction=bgc_txn,
            payment_type=self.split,
            status_code=receipt_status.CALL_FOR_PAYMENT,
        )
        bgc_txn.latest_receipt = receipt
        bgc_txn.save()
        payment_row_recipe.make(
            payment_type=self.bgc,
            status=receipt_status.GIFT_CARD_DEPOSIT,
            receipt=receipt,
        )
        payment_row_recipe.make(
            payment_type=self.pba,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt,
            basket_payment_id=mocked_get_basket_payment_entity.return_value.id,
        )

        Command().handle()

        bgc_txn.refresh_from_db()
        self.assertEqual(bgc_txn.latest_receipt.status_code, receipt_status.PAYMENT_SUCCESS)
        for payment_row in bgc_txn.latest_receipt.payment_rows.all():
            self.assertEqual(payment_row.status, receipt_status.PAYMENT_SUCCESS)

    @patch('webapps.point_of_sale.ports.BasketPaymentPort.get_basket_payment_entity')
    def test_finish_unsettled_bgc_transactions_success_not_bgc_appt(
        self, mocked_get_basket_payment_entity
    ):
        mocked_get_basket_payment_entity.return_value = self._get_basket_payment_mock(
            BasketPaymentStatus.SUCCESS
        )
        self.appt.is_booksy_gift_card_appointment = False
        self.appt.save()
        bgc_txn = transaction_recipe.make(
            pos=self.pos,
            appointment=self.appt,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            transaction=bgc_txn,
            payment_type=self.split,
            status_code=receipt_status.CALL_FOR_PAYMENT,
        )
        bgc_txn.latest_receipt = receipt
        bgc_txn.save()
        payment_row_recipe.make(
            payment_type=self.bgc,
            status=receipt_status.GIFT_CARD_DEPOSIT,
            receipt=receipt,
        )
        payment_row_recipe.make(
            payment_type=self.pba,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt,
            basket_payment_id=uuid.UUID('16d9f8bd-f6e9-4dc3-8540-713073077f41'),
        )

        Command().handle()

        bgc_txn.refresh_from_db()
        self.assertEqual(bgc_txn.latest_receipt.status_code, receipt_status.PAYMENT_SUCCESS)
        for payment_row in bgc_txn.latest_receipt.payment_rows.all():
            self.assertEqual(payment_row.status, receipt_status.PAYMENT_SUCCESS)

    @patch('webapps.point_of_sale.ports.BasketPaymentPort.get_basket_payment_entity')
    def test_finish_unsettled_bgc_transactions_failed(self, mocked_get_basket_payment_entity):
        mocked_get_basket_payment_entity.return_value = self._get_basket_payment_mock(
            BasketPaymentStatus.FAILED
        )
        bgc_txn = transaction_recipe.make(
            pos=self.pos,
            appointment=self.appt,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            transaction=bgc_txn,
            payment_type=self.split,
            status_code=receipt_status.CALL_FOR_PAYMENT,
        )
        bgc_txn.latest_receipt = receipt
        bgc_txn.save()
        payment_row_recipe.make(
            payment_type=self.bgc,
            status=receipt_status.GIFT_CARD_DEPOSIT,
            receipt=receipt,
        )
        payment_row_recipe.make(
            payment_type=self.pba,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt,
            basket_payment_id=uuid.UUID('16d9f8bd-f6e9-4dc3-8540-713073077f41'),
        )

        Command().handle()

        bgc_txn.refresh_from_db()
        self.assertEqual(bgc_txn.latest_receipt.status_code, receipt_status.CALL_FOR_PAYMENT)

    @patch(
        'webapps.pos.management.commands.finish_unsettled_bgc_transactions.Command.finish_unsettled_bgc_transactions_batch_task'  # pylint: disable=line-too-long
    )
    def test_finish_unsettled_bgc_transactions_not_called(self, mocked_batch_task):
        txn = transaction_recipe.make(
            pos=self.pos,
            appointment=self.appt,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            transaction=txn,
            payment_type=self.split,
            status_code=receipt_status.CALL_FOR_PAYMENT,
        )
        txn.latest_receipt = receipt
        txn.save()
        payment_row_recipe.make(
            payment_type=self.cash,
            status=receipt_status.PAYMENT_SUCCESS,
            receipt=receipt,
        )
        payment_row_recipe.make(
            payment_type=self.pba,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt,
            basket_payment_id=uuid.UUID('16d9f8bd-f6e9-4dc3-8540-713073077f41'),
        )

        Command().handle()

        txn.refresh_from_db()
        self.assertEqual(txn.latest_receipt.status_code, receipt_status.CALL_FOR_PAYMENT)

        mocked_batch_task.assert_not_called()

    @patch(
        'webapps.pos.management.commands.finish_unsettled_bgc_transactions.Command.finish_unsettled_bgc_transactions_batch_task'  # pylint: disable=line-too-long
    )
    def test_finish_unsettled_bgc_transactions_not_called_none_appt(self, mocked_batch_task):
        txn = transaction_recipe.make(
            pos=self.pos,
            appointment=None,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            transaction=txn,
            payment_type=self.split,
            status_code=receipt_status.CALL_FOR_PAYMENT,
        )
        txn.latest_receipt = receipt
        txn.save()
        payment_row_recipe.make(
            payment_type=self.cash,
            status=receipt_status.PAYMENT_SUCCESS,
            receipt=receipt,
        )
        payment_row_recipe.make(
            payment_type=self.pba,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt,
            basket_payment_id=uuid.UUID('16d9f8bd-f6e9-4dc3-8540-713073077f41'),
        )

        Command().handle()

        txn.refresh_from_db()
        self.assertEqual(txn.latest_receipt.status_code, receipt_status.CALL_FOR_PAYMENT)

        mocked_batch_task.assert_not_called()

    def _get_basket_payment_mock(self, status):
        return BasketPaymentEntity(
            id=uuid.UUID('16d9f8bd-f6e9-4dc3-8540-713073077f41'),
            basket_id=uuid.UUID('e33b58dd-f185-4ff0-8b37-219f09912b12'),
            amount=12345,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.STRIPE,
            balance_transaction_id=uuid.UUID('9e9943b6-565c-4097-9da9-9b71aad9a4f0'),
            status=status,
            user_id=123,
            type=BasketPaymentType.PAYMENT,
            parent_basket_payment_id=None,
            error_code=None,
            action_required_details=None,
            metadata=None,
            auto_capture=True,
            source=BasketPaymentSource.PAYMENT,
            created=datetime(2024, 11, 29),
            operator_id=13,
            label='label',
        )
