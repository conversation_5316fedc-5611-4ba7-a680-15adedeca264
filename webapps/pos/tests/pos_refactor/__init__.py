import uuid
from random import randint
from typing import (
    Optional,
    Union,
)

import pytest
from django.core.cache import cache
from django.db.models import Q
from django.test import (
    TestCase,
    override_settings,
)
from model_bakery import baker

from lib.baker_utils import get_or_create_booking_source
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    DisputeType,
    FeeType,
    PaymentMethodType as WalletPaymentMethodType,
    PaymentStatus,
)
from lib.payment_providers.entities import DeviceDataEntity
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
)
from lib.point_of_sale.enums import (
    BasketItemTaxType,
    BasketItemType,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketType,
    CancellationFeeAuthStatus,
    PaymentMethodType as PointOfSalePaymentMethodType,
)
from lib.tools import minor_unit, tznow
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_gateway.scripts import create_default_wallets
from webapps.payment_providers.models import (
    AccountHolder,
    Payment,
    StripeAccountHolder,
)
from webapps.point_of_sale.models import (
    Basket,
    BasketItem,
    BasketPayment,
)
from webapps.pos.enums import POSPlanPaymentTypeEnum, PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    POS,
    POSPlan,
    PaymentRow,
    PaymentType,
    TaxRate,
    Tip,
    Transaction,
    TransactionRow,
)
from webapps.pos.serializers import TransactionSerializer
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeTestEventBody
from webapps.user.models import User


# pylint: disable=too-many-arguments
# pylint: disable=too-many-positional-arguments
# pylint: disable=too-many-instance-attributes
@pytest.mark.django_db
@override_settings(POS__REFUNDS=True)
class TestTransactionSerializerBase(TestCase):
    # pylint: disable=too-many-instance-attributes
    def setUp(self):
        """Set up business ready for appointments."""

        # @session stuff
        self.booking_source = get_or_create_booking_source()
        self.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='*********',
        )
        self.access_level = Resource.STAFF_ACCESS_LEVEL_OWNER

        # business
        self.business = baker.make(
            Business,
            owner=self.user,
        )
        self.staffer = baker.make(
            Resource,
            staff_user=self.user,
            business=self.business,
            active=True,
            type=Resource.STAFF,
        )

        # pos
        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
            registers_enabled=False,
        )
        self.account_holder = baker.make(
            AccountHolder,
        )

        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=0)
        baker.make(Tip, pos=self.pos, rate=0, default=True)

        # cash payment type must exist
        self.cash = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.CASH,
        )

        self.blik = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.BLIK,
        )

        self.compatibilities = {
            'new_checkout': True,
            'prepayment': True,
        }

        self.bci = baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=self.business,
        )

        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        self.customer_wallet = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )[0]

        create_default_wallets()
        self.stripe_account_holder = baker.make(
            StripeAccountHolder,
            account_holder_id=PaymentGatewayPort.get_booksy_wallet().account_holder_id,
        )

        self.stripe_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )

        self.pos.recalculate_pos_plans()

        self.basket_payment_source = BasketPaymentSource.PAYMENT

        self.analytics_filters = (
            Q(device_data__device_fingerprint='233'),
            Q(device_data__phone_number='*********'),
            Q(device_data__user_agent='asdds'),
            Q(device_data__ip='123'),
        )

        self.device_data_entity = DeviceDataEntity(
            device_fingerprint='233',
            phone_number='*********',
            user_agent='asdds',
            ip='123',
        )

        self.device_data_context = {
            'extra_data': {
                'user': self.bci.user,
                'cardholder_ip': '123',
            },
            'device_fingerprint': '233',
            'cell_phone': '*********',
            'user_agent': 'asdds',
        }

        self.device_data_dict = {
            'device_data': DeviceDataDict(
                fingerprint='233',
                phone_number='*********',
                user_agent='asdds',
            ),
            'extra_data': {'cardholder_ip': '123'},
        }

    def tearDown(self) -> None:
        super().tearDown()
        cache.clear()

    def create_appointment(self):
        service_variant = baker.make(
            ServiceVariant,
            service=baker.make(
                Service,
                business=self.business,
                tax_rate=0,
            ),
            type=PriceType.FIXED,
            price=123.45,
        )
        return create_appointment(
            [
                {
                    'service_variant_id': service_variant.id,
                },
            ],
            business=self.business,
        )

    def create_transaction(self, payment_type_code: PaymentTypeEnum = PaymentTypeEnum.CASH):
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 123.45,
                }
                for booking in self.create_appointment().subbookings
            ],
            'payment_rows': [{'payment_type_code': payment_type_code, 'amount': 123.45}],
            'customer_card_id': self.bci.id,
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'device_data': self.device_data_entity,
            },
        )

        assert serializer.is_valid(), serializer.errors
        return serializer.save()

    def edit_transaction(
        self,
        old_txn: Transaction,
        edit: bool,
        payment_typ_code: PaymentTypeEnum,
        tip: dict = None,
        amount=543.21,
    ):
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 543.21,
                }
                for booking in self.create_appointment().subbookings
            ],
            'payment_rows': [{'payment_type_code': payment_typ_code, 'amount': amount}],
            'customer_card_id': self.bci.id,
            'parent_txn': old_txn.id,
        }

        if tip:
            data['tip'] = tip

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'old_txn': old_txn,
                'edit': edit,
                'device_data': self.device_data_entity,
            },
        )

        assert serializer.is_valid(), serializer.errors

        if edit is True:
            old_txn.update_payment_rows(
                status=receipt_status.ARCHIVED,
            )

        return serializer.save()

    def create_prepayment_transaction(self, appointment):
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                }
                for booking in appointment.subbookings
            ],
            'customer_card_id': self.bci.id,
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 432.12,
                }
            ],
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': None,
                'prepayment': 432.12,
                'compatibilities': self.compatibilities,
            },
        )
        assert serializer.is_valid(), serializer.errors
        return serializer.save()

    def edit_prepayment_transaction(self, old_txn, payment_rows_data, item_price=987.56, tip=None):
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': item_price,
                }
                for booking in old_txn.appointment.subbookings
            ],
            'payment_rows': payment_rows_data,
            'customer_card_id': self.bci.id,
            'parent_txn': old_txn.id,
        }

        if tip:
            data['tip'] = tip

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'old_txn': old_txn,
                'edit': False,
                'device_data': self.device_data_entity,
            },
        )

        assert serializer.is_valid(), serializer.errors
        return serializer.save()

    def create_split_transaction(self, payment_rows_data):
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                }
                for booking in self.create_appointment().subbookings
            ],
            'payment_rows': payment_rows_data,
            'customer_card_id': self.bci.id,
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'device_data': self.device_data_entity,
            },
        )

        assert serializer.is_valid(), serializer.errors
        txn = serializer.save()
        BasketPayment.objects.filter(
            payment_provider_code=PaymentProviderCode.BOOKSY_GIFT_CARDS
        ).update(operator_id=None)
        return txn

    def create_cancellation_fee(self, appointment):
        data = {
            'transaction_type': Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            'deposits': [
                {
                    'booking_id': booking.id,
                }
                for booking in appointment.subbookings
            ],
            'dry_run': False,
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 100.00,
                }
            ],
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': None,
                'deposit_mode': True,
                'compatibilities': self.compatibilities,
                'device_data': self.device_data_entity,
            },
        )

        assert serializer.is_valid(), serializer.errors
        return serializer.save()

    def get_and_check_basket_item(self, txn, basket_type: BasketItemType = BasketItemType.SERVICE):
        rows = txn.rows.all()
        self.assertEqual(len(rows), 1)
        basket_item = self.check_basket_item(rows[0], basket_type)
        return basket_item

    def check_basket_item(self, row: TransactionRow, basket_type: BasketItemType):
        basket_item = BasketItem.all_objects.get(id=row.basket_item_id)
        self.assertEqual(basket_item.type, basket_type)
        self.assertEqual(basket_item.order, row.order)
        self.assertEqual(basket_item.name_line_1, row.name_line_1)
        self.assertEqual(basket_item.name_line_2, row.name_line_2)
        self.assertEqual(basket_item.quantity, row.quantity)
        self.assertEqual(basket_item.item_price, minor_unit(row.item_price))
        self.assertEqual(basket_item.discount_rate, row.discount_rate)
        self.assertEqual(basket_item.discounted_item_price, minor_unit(row.discounted_item_price))
        self.assertEqual(basket_item.tax_amount, minor_unit(row.tax_amount))
        self.assertEqual(
            basket_item.tax_rate, minor_unit(row.tax_rate) if row.tax_rate else row.tax_rate
        )
        self.assertEqual(basket_item.tax_type, BasketItemTaxType.INCLUDED)
        self.assertEqual(basket_item.total, minor_unit(row.total))
        self.assertEqual(basket_item.net_total, minor_unit(row.net_total))
        self.assertEqual(basket_item.gross_total, minor_unit(row.gross_total))
        self.assertEqual(basket_item.discounted_total, minor_unit(row.discounted_total))
        self.assertEqual(basket_item.net_total_wo_discount, minor_unit(row.net_total_wo_discount))
        self.assertEqual(basket_item.order, row.order)

        return basket_item

    def compare_basket_item(
        self, basket_item: BasketItem, basket_item2: BasketItem, same_price=True
    ):
        self.assertNotEqual(basket_item, basket_item2)

        self.assertEqual(basket_item.type, basket_item2.type)
        self.assertEqual(basket_item.order, basket_item2.order)
        self.assertEqual(basket_item.name_line_1, basket_item2.name_line_1)
        self.assertEqual(basket_item.name_line_2, basket_item2.name_line_2)
        self.assertEqual(basket_item.quantity, basket_item2.quantity)
        self.assertEqual(basket_item.tax_rate, basket_item2.tax_rate)
        self.assertEqual(basket_item.tax_type, basket_item2.tax_type)

        if not same_price:
            return basket_item

        self.assertEqual(basket_item.item_price, basket_item2.item_price)
        self.assertEqual(basket_item.discount_rate, basket_item2.discount_rate)
        self.assertEqual(basket_item.discounted_item_price, basket_item2.discounted_item_price)
        self.assertEqual(basket_item.tax_amount, basket_item2.tax_amount)
        self.assertEqual(basket_item.total, basket_item2.total)
        self.assertEqual(basket_item.net_total, basket_item2.net_total)
        self.assertEqual(basket_item.gross_total, basket_item2.gross_total)
        self.assertEqual(basket_item.discounted_total, basket_item2.discounted_total)
        self.assertEqual(basket_item.net_total_wo_discount, basket_item2.net_total_wo_discount)

        return basket_item

    @staticmethod
    def map_basket_payment_status_into_balance_transaction_status(
        bp_status: BasketPaymentStatus,
    ) -> BalanceTransactionStatus:
        return {
            BasketPaymentStatus.PENDING: BalanceTransactionStatus.PROCESSING,
            BasketPaymentStatus.ACTION_REQUIRED: BalanceTransactionStatus.PROCESSING,
            BasketPaymentStatus.SUCCESS: BalanceTransactionStatus.SUCCESS,
            BasketPaymentStatus.FAILED: BalanceTransactionStatus.FAILED,
            BasketPaymentStatus.CANCELED: BalanceTransactionStatus.CANCELED,
        }[bp_status]

    @staticmethod
    def map_basket_payment_type_into_balance_transaction_type(
        bp_type: BasketPaymentType,
    ) -> BalanceTransactionType:
        return {
            BasketPaymentType.PAYMENT: BalanceTransactionType.PAYMENT,
            BasketPaymentType.REFUND: BalanceTransactionType.REFUND,
            BasketPaymentType.CHARGEBACK: BalanceTransactionType.DISPUTE,
            BasketPaymentType.CHARGEBACK_REVERSED: BalanceTransactionType.DISPUTE,
            BasketPaymentType.SECOND_CHARGEBACK: BalanceTransactionType.DISPUTE,
        }[bp_type]

    @staticmethod
    def map_basket_payment_method_into_wallet_payment_method(
        payment_method: PointOfSalePaymentMethodType,
    ):
        return {
            PointOfSalePaymentMethodType.TERMINAL: WalletPaymentMethodType.TERMINAL,
            PointOfSalePaymentMethodType.CARD: WalletPaymentMethodType.CARD,
            PointOfSalePaymentMethodType.GOOGLE_PAY: WalletPaymentMethodType.GOOGLE_PAY,
            PointOfSalePaymentMethodType.APPLE_PAY: WalletPaymentMethodType.APPLE_PAY,
            PointOfSalePaymentMethodType.TAP_TO_PAY: WalletPaymentMethodType.TAP_TO_PAY,
            PointOfSalePaymentMethodType.BOOKSY_GIFT_CARD: WalletPaymentMethodType.BOOKSY_GIFT_CARDS,  # pylint: disable=line-too-long
            PointOfSalePaymentMethodType.BLIK: WalletPaymentMethodType.BLIK,
            PointOfSalePaymentMethodType.KEYED_IN_PAYMENT: WalletPaymentMethodType.KEYED_IN_PAYMENT,
        }[payment_method]

    @staticmethod
    def map_basket_payment_type_into_balance_transaction_dispute(basket_type: BasketPaymentType):
        return {
            BasketPaymentType.CHARGEBACK: DisputeType.CHARGEBACK,
            BasketPaymentType.CHARGEBACK_REVERSED: DisputeType.REVERSED_CHARGEBACK,
            BasketPaymentType.SECOND_CHARGEBACK: DisputeType.SECOND_CHARGEBACK,
        }[basket_type]

    def check_basket_payment(
        self,
        basket: Basket,
        payment_row: PaymentRow,
        payment_method: PointOfSalePaymentMethodType,
        provider_code: Optional[PaymentProviderCode],
        basket_payment_status: BasketPaymentStatus,
        basket_payment_type: BasketPaymentType,
        balance_transaction_related_obj_status: Union[PaymentStatus] = None,
        error_code: PaymentError = None,
        action_required_details: dict = None,
        fee_amount: int = 0,
        balance_transaction_existing: bool = False,
        source: BasketPaymentSource = None,
    ):
        payment_row.refresh_from_db()
        basket_payment = BasketPayment.objects.get(basket=basket, id=payment_row.basket_payment_id)
        self.assertEqual(basket_payment.amount, minor_unit(payment_row.amount))
        self.assertEqual(basket_payment.payment_method, payment_method)
        self.assertEqual(basket_payment.payment_provider_code, provider_code)
        self.assertEqual(basket_payment.user_id, self.bci.user.id)
        self.assertEqual(basket_payment.type, basket_payment_type)
        self.assertEqual(basket_payment.status, basket_payment_status)
        self.assertEqual(basket_payment.error_code, error_code)
        self.assertEqual(basket_payment.action_required_details, action_required_details)
        self.assertEqual(basket_payment.source, source if source else self.basket_payment_source)

        if (
            basket_payment.type == BasketPaymentType.PAYMENT
            and payment_row.payment_type.code
            not in [
                PaymentTypeEnum.PREPAYMENT,
                PaymentTypeEnum.BOOKSY_GIFT_CARD,
            ]
        ):
            operator_resource = payment_row.receipt.transaction.operator.staffers.filter(
                business=self.business,
            ).first()
            self.assertEqual(basket_payment.operator_id, operator_resource.id)
        else:
            self.assertEqual(basket_payment.operator_id, None)

        if balance_transaction_existing or balance_transaction_related_obj_status:
            self.assertNotEqual(basket_payment.balance_transaction_id, None)

            payment_method = self.map_basket_payment_method_into_wallet_payment_method(
                payment_method
            )

            if payment_method not in [
                PointOfSalePaymentMethodType.APPLE_PAY,
                PointOfSalePaymentMethodType.GOOGLE_PAY,
                PointOfSalePaymentMethodType.TERMINAL,
                PointOfSalePaymentMethodType.TAP_TO_PAY,
                PointOfSalePaymentMethodType.BLIK,
                PointOfSalePaymentMethodType.BOOKSY_GIFT_CARD,
                PointOfSalePaymentMethodType.KEYED_IN_PAYMENT,
            ] and basket_payment.status in [
                BasketPaymentStatus.SUCCESS,
                BasketPaymentStatus.FAILED,
            ]:
                self.assertEqual(payment_row.card_last_digits, '1234')

            self.check_balance_transaction(
                basket_payment=basket_payment,
                payment_method=payment_method,
                provider_code=provider_code,
                balance_transaction_status=(
                    self.map_basket_payment_status_into_balance_transaction_status(
                        basket_payment.status
                    )
                ),
                balance_transaction_related_obj_status=balance_transaction_related_obj_status,
                fee_amount=fee_amount,
                balance_transaction_type=self.map_basket_payment_type_into_balance_transaction_type(
                    basket_payment.type
                ),
                parent_balance_transaction_id=basket_payment.parent_basket_payment
                and basket_payment.parent_basket_payment.balance_transaction_id,
                error_code=error_code,
                action_required_details=action_required_details,
            )

        else:
            self.assertEqual(basket_payment.balance_transaction_id, None)

        return basket_payment

    def check_balance_transaction(
        self,
        basket_payment: BasketPayment,
        payment_method: PointOfSalePaymentMethodType,
        provider_code: Optional[PaymentProviderCode],
        balance_transaction_status: BalanceTransactionStatus,
        balance_transaction_related_obj_status: Union[PaymentStatus],
        fee_amount: int = 0,
        balance_transaction_type: BalanceTransactionType = None,
        parent_balance_transaction_id: uuid.UUID = None,
        error_code: PaymentError = None,
        action_required_details: dict = None,
    ):
        bt = BalanceTransaction.objects.get(id=basket_payment.balance_transaction_id)

        self.assertEqual(bt.amount, basket_payment.amount)
        self.assertEqual(bt.status, balance_transaction_status)
        self.assertEqual(bt.payment_method, payment_method)
        self.assertEqual(bt.payment_provider_code, provider_code)
        self.assertEqual(bt.transaction_type, balance_transaction_type)
        self.assertNotEqual(bt.external_id, None)
        self.assertEqual(bt.parent_balance_transaction_id, parent_balance_transaction_id)

        if balance_transaction_type == BalanceTransactionType.PAYMENT:
            self.assertEqual(bt.receiver.business_id, self.business.id)
            self.assertEqual(bt.sender.user_id, self.bci.user_id)
            self.assertEqual(bt.payment.status, balance_transaction_related_obj_status)
            self.assertEqual(bt.payment.error_code, error_code)
            self.assertEqual(bt.payment.action_required_details, action_required_details)
            self.assertEqual(bt.fee_amount, fee_amount)
            self.assertEqual(bt.transaction_type, BalanceTransactionType.PAYMENT)
        elif balance_transaction_type == BalanceTransactionType.REFUND:
            self.assertEqual(bt.receiver.user_id, self.bci.user_id)
            self.assertEqual(bt.sender.business_id, self.business.id)
            self.assertNotEqual(bt.refund, None)
            self.assertEqual(bt.fee_amount, 0)
            self.assertEqual(bt.transaction_type, BalanceTransactionType.REFUND)

            # We cannot create FeeBalanceTransaction yet. It will be done after stage2.
            # if fee_amount:
            #     fee_bt = BalanceTransaction.objects.get(
            #         parent_balance_transaction_id=bt.id,
            #     )
            #
            #     self.assertEqual(fee_bt.receiver.owner_type, WalletOwnerType.BOOKSY)
            #     self.assertEqual(fee_bt.sender.business_id, self.business.id)
            #     self.assertEqual(fee_bt.amount, fee_amount)
            #     self.assertEqual(fee_bt.fee_amount, 0)
            #     self.assertEqual(fee_bt.transaction_type, BalanceTransactionType.FEE)
            #     self.assertEqual(fee_bt.status, balance_transaction_status)

        elif balance_transaction_type == BalanceTransactionType.DISPUTE:
            self.assertEqual(bt.transaction_type, BalanceTransactionType.DISPUTE)
            self.assertNotEqual(bt.dispute, None)
            self.assertEqual(
                bt.dispute.type,
                self.map_basket_payment_type_into_balance_transaction_dispute(basket_payment.type),
            )
            self.assertEqual(bt.fee_amount, 0)

            if bt.dispute in [DisputeType.CHARGEBACK, DisputeType.SECOND_CHARGEBACK]:
                # "normal" chargeback - funds go from the merchant to the customer
                self.assertEqual(bt.receiver.user_id, self.bci.user_id)
                self.assertEqual(bt.sender.business_id, self.business.id)
            elif bt.dispute in [DisputeType.REVERSED_CHARGEBACK]:
                # chargeback won by the merchant - funds go from the customer to the merchant
                self.assertEqual(bt.sender.user_id, self.bci.user_id)
                self.assertEqual(bt.receiver.business_id, self.business.id)

        return bt

    def _check_payment_providers_payment(
        self,
        balance_transaction_related_obj_status: Union[PaymentStatus],
        payment_providers_payment_id: str,
        error_code: PaymentError = None,
        action_required_details: dict = None,
    ):
        payment = Payment.objects.get(
            id=payment_providers_payment_id,
        )
        self.assertEqual(
            payment.status,
            balance_transaction_related_obj_status,
        )
        self.assertEqual(payment.error_code, error_code)
        self.assertEqual(payment.action_required_details, action_required_details)

    @staticmethod
    def map_cf_auth_status_into_payment_status(cf_auth_status: CancellationFeeAuthStatus):
        return {
            CancellationFeeAuthStatus.PENDING: BalanceTransactionStatus.PROCESSING,
            CancellationFeeAuthStatus.SUCCESS: BalanceTransactionStatus.PROCESSING,
            CancellationFeeAuthStatus.FAILED: BalanceTransactionStatus.FAILED,
            CancellationFeeAuthStatus.CANCELED: BalanceTransactionStatus.CANCELED,
        }[cf_auth_status]

    def check_basic_basket(self, basket: Basket, archived=False, customer_id=-1):
        self.assertEqual(basket.type, BasketType.PAYMENT)
        self.assertEqual(basket.business_id, self.business.id)

        if customer_id == -1:
            self.assertEqual(basket.customer_card_id, self.bci.id)
        else:
            self.assertEqual(basket.customer_card_id, None)

        if archived:
            self.assertEqual(basket.archived.date(), tznow().date())
        else:
            self.assertEqual(basket.archived, None)

    def check_basket_and_payment(self, txn):
        self.assertEqual(txn.receipts.all().count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketPayment
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        return basket

    @staticmethod
    def generate_id(lenght: int = 16):
        return ''.join(str(randint(0, 9)) for i in range(lenght))

    def _check_refund_balance_transactions(self):
        if self.provider_code == PaymentProviderCode.STRIPE:
            plan = self.stripe_plan
        else:
            plan = self.plan
        expect_refund_fee = plan.refund_provision > 0 or plan.refund_txn_fee > 0

        # there should be only 1 payment bt thus .get()
        payment_bt = BalanceTransaction.objects.get(transaction_type=BalanceTransactionType.PAYMENT)
        self.assertEqual(payment_bt.child_balance_transactions.count(), 1)
        refund_bt = payment_bt.child_balance_transactions.get(
            transaction_type=BalanceTransactionType.REFUND
        )
        expected_bt_count = 2  # payment + refund
        if expect_refund_fee:
            # there should be fees
            children_qs = refund_bt.child_balance_transactions.all()
            self.assertEqual(
                set(children_qs.values_list("transaction_type", flat=True)),
                {BalanceTransactionType.FEE},
            )
            self.assertEqual(children_qs.filter(fee__fee_type=FeeType.REFUND).count(), 1)
            expected_bt_count += 1

        self.assertEqual(BalanceTransaction.objects.count(), expected_bt_count)

    def _check_chargeback_balance_transactions(
        self,
        chargeback_reversed: bool = False,
        second_chargeback: bool = False,
    ):
        expect_payment_reversal_fee = self.provider_code == PaymentProviderCode.STRIPE

        if self.provider_code == PaymentProviderCode.STRIPE:
            plan = self.stripe_plan
        else:
            plan = self.plan
        expect_chargeback_fee = plan.chargeback_provision > 0 or plan.chargeback_txn_fee > 0

        # there should be only 1 payment bt thus .get()
        payment_bt = BalanceTransaction.objects.get(transaction_type=BalanceTransactionType.PAYMENT)
        payment_children_expected_count = 1
        if chargeback_reversed:
            payment_children_expected_count += 1
        if second_chargeback:
            payment_children_expected_count += 1
        self.assertEqual(
            payment_bt.child_balance_transactions.count(), payment_children_expected_count
        )
        dispute_bt = payment_bt.child_balance_transactions.get(
            transaction_type=BalanceTransactionType.DISPUTE,
            dispute__type=DisputeType.CHARGEBACK,
        )
        expected_bt_count = 1 + payment_children_expected_count  # payment + children

        # there should be fees in stage2
        children_qs = dispute_bt.child_balance_transactions.all()
        if expect_payment_reversal_fee or expect_chargeback_fee:
            self.assertEqual(
                set(children_qs.values_list("transaction_type", flat=True)),
                {BalanceTransactionType.FEE},
            )
        if expect_payment_reversal_fee:
            self.assertEqual(children_qs.filter(fee__fee_type=FeeType.PAYMENT_REVERSAL).count(), 1)
            expected_bt_count += 1
        if expect_chargeback_fee:
            self.assertEqual(children_qs.filter(fee__fee_type=FeeType.DISPUTE).count(), 1)
            expected_bt_count += 1
        if expect_payment_reversal_fee and chargeback_reversed:
            dispute_reversed_bt = payment_bt.child_balance_transactions.get(
                transaction_type=BalanceTransactionType.DISPUTE,
                dispute__type=DisputeType.REVERSED_CHARGEBACK,
            )
            dispute_reversed_children_qs = dispute_reversed_bt.child_balance_transactions.all()
            self.assertEqual(
                dispute_reversed_children_qs.filter(fee__fee_type=FeeType.FUNDS_REINSTATED).count(),
                1,
            )
            expected_bt_count += 1

        self.assertEqual(BalanceTransaction.objects.count(), expected_bt_count)
