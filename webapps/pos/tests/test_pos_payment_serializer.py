from pprint import pprint
from unittest.mock import patch

import pytest
from django.test.utils import override_settings
from django.utils.translation import gettext as _
from model_bakery import baker

from webapps.market_pay.models import AccountHolder
from webapps.pos.enums import (
    PAY_BY_APP_DISABLED,
    PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS,
    PAY_BY_APP_ENABLED,
    PAY_BY_APP_PENDING,
    PaymentTypeEnum,
)
from webapps.pos.models import POS, PaymentType
from webapps.pos.serializers import POSSerializer
from webapps.register.models import Register
from webapps.stripe_integration.models import StripeAccount

# region data
VALIDATION_SUCCESS = True
VALIDATION_FAILURE = False

EMPTY_ERRORS = {}
# region tips
TIP_0 = {
    'rate': 0,
    'default': False,
}
TIP_0_DEFAULT = {
    'rate': 0,
    'default': True,
}
TIP_1 = {
    'rate': 1,
    'default': False,
}
TIP_1_DEFAULT = {
    'rate': 1,
    'default': True,
}
TIP_1_5_DEFAULT = {
    'rate': 1.5,
    'default': True,
}
TIP_BELOW_ZERO = {
    'rate': -1,
    'default': False,
}
TIP_OVER_100 = {
    'rate': 101,
    'default': False,
}

TIPS_VALIDATION_SUCCESS = ({'tips': [TIP_0_DEFAULT, TIP_1]}, VALIDATION_SUCCESS, EMPTY_ERRORS)
TIPS_DUPLICATED_RATES = (
    {'tips': [TIP_0_DEFAULT, TIP_0]},
    VALIDATION_FAILURE,
    {'tips': [_('Rate values should be unique.')]},
)
TIPS_MULTIPLE_DEFAULT = (
    {'tips': [TIP_0_DEFAULT, TIP_1_DEFAULT]},
    VALIDATION_FAILURE,
    {'tips': [_('One and only one tip rate should be default.')]},
)
TIPS_NON_INTEGER = (
    {'tips': [TIP_0_DEFAULT, TIP_1_5_DEFAULT]},
    VALIDATION_FAILURE,
    {'tips': [{}, {'rate': [_('A valid integer is required.')]}]},
)
TIPS_BELOW_ZERO = (
    {'tips': [TIP_0_DEFAULT, TIP_BELOW_ZERO]},
    VALIDATION_FAILURE,
    {'tips': [{}, {'rate': [_('Ensure this value is greater than or equal to 0.')]}]},
)
TIPS_OVER_100 = (
    {'tips': [TIP_0_DEFAULT, TIP_OVER_100]},
    VALIDATION_FAILURE,
    {'tips': [{}, {'rate': [_('Ensure this value is less than or equal to 100.')]}]},
)
# endregion tips
# region tax rates
TAX_0 = {
    'rate': 0,
    'default_for_service': False,
    'default_for_product': False,
}
TAX_0_PRODUCT_DEFAULT = {
    'rate': 0,
    'default_for_service': True,
    'default_for_product': False,
}
TAX_0_SERVICE_DEFAULT = {
    'rate': 0,
    'default_for_service': False,
    'default_for_product': True,
}
TAX_0_PRODUCT_SERVICE_DEFAULT = {
    'rate': 0,
    'default_for_service': True,
    'default_for_product': True,
}
TAX_1 = {
    'rate': 1,
    'default_for_service': False,
    'default_for_product': False,
}
TAX_1_PRODUCT_DEFAULT = {
    'rate': 1,
    'default_for_product': True,
    'default_for_service': False,
}
TAX_1_SERVICE_DEFAULT = {
    'rate': 1,
    'default_for_product': False,
    'default_for_service': True,
}
TAX_1_333 = {
    'rate': 1.333,
    'default_for_service': False,
    'default_for_product': False,
}
TAX_0_5 = {
    'rate': 0.5,
    'default_for_service': False,
    'default_for_product': False,
}
TAX_NULL = {
    'rate': None,
    'default_for_service': False,
    'default_for_product': False,
}

TAX_RATES_VALIDATION_SUCCESS = (
    {'tax_rates': [TAX_0_PRODUCT_SERVICE_DEFAULT, TAX_NULL]},
    VALIDATION_SUCCESS,
    EMPTY_ERRORS,
)
TAX_RATES_MISSING_DEFAULTS = (
    {'tax_rates': [TAX_0]},
    VALIDATION_FAILURE,
    {
        'tax_rates': [
            _('One and only one tax rate should be default for service.'),
            _('One and only one tax rate should be default for product.'),
        ]
    },
)
TAX_RATES_OVER_PRECISION = (
    {'tax_rates': [TAX_0_PRODUCT_SERVICE_DEFAULT, TAX_1_333]},
    VALIDATION_FAILURE,
    {'tax_rates': [{}, {'rate': [_('Ensure that there are no more than 2 decimal places.')]}]},
)
TAX_RATES_MULIPLE_PRODUCT_DEFAULTS = (
    {'tax_rates': [TAX_0_PRODUCT_SERVICE_DEFAULT, TAX_1_PRODUCT_DEFAULT]},
    VALIDATION_FAILURE,
    {
        'tax_rates': [
            _('One and only one tax rate should be default for product.'),
        ]
    },
)
TAX_RATES_MULIPLE_SERVICE_DEFAULTS = (
    {'tax_rates': [TAX_0_PRODUCT_SERVICE_DEFAULT, TAX_1_SERVICE_DEFAULT]},
    VALIDATION_FAILURE,
    {
        'tax_rates': [
            _('One and only one tax rate should be default for service.'),
        ]
    },
)
TAX_RATES_DUPLICATED_RATES = (
    {'tax_rates': [TAX_0_PRODUCT_SERVICE_DEFAULT, TAX_0]},
    VALIDATION_FAILURE,
    {
        'tax_rates': [
            _('Rate values should be unique.'),
        ]
    },
)
TAX_RATES_SPECIAL_CASE = (
    {'tax_rates': [TAX_0_PRODUCT_SERVICE_DEFAULT, TAX_0_5]},
    VALIDATION_FAILURE,
    {
        'tax_rates': [
            _('Invalid tax rate.'),
        ]
    },
)
# endregion payment types
# region pay_by_app_status
SETTING_PAY_BY_APP_ACTIVE = True
SETTING_PAY_BY_APP_DISABLED = False
POS_PAY_BY_APP_STATUS_ENABLED = PAY_BY_APP_ENABLED
POS_PAY_BY_APP_STATUS_PENDING = PAY_BY_APP_PENDING
POS_PAY_BY_APP_STATUS_DISABLED = PAY_BY_APP_DISABLED

PAY_BY_APP_ENABLED_RESEND = (
    {'pay_by_app_status': PAY_BY_APP_ENABLED},
    POS_PAY_BY_APP_STATUS_ENABLED,
    SETTING_PAY_BY_APP_ACTIVE,
    VALIDATION_SUCCESS,
    EMPTY_ERRORS,
)
PAY_BY_APP_ENABLED_ACTIVATION_BY_MERCHANT = (
    {'pay_by_app_status': PAY_BY_APP_ENABLED},
    POS_PAY_BY_APP_STATUS_PENDING,
    SETTING_PAY_BY_APP_ACTIVE,
    VALIDATION_FAILURE,
    {'pay_by_app_status': [_('Status is not allowed. Pending and disabled statuses are allowed.')]},
)
PAY_BY_APP_NOT_ENABLED_ACTIVATION_BY_MERCHANT = (
    {'pay_by_app_status': PAY_BY_APP_ENABLED},
    POS_PAY_BY_APP_STATUS_DISABLED,
    SETTING_PAY_BY_APP_DISABLED,
    VALIDATION_FAILURE,
    {'pay_by_app_status': [_('Mobile payments are disabled in your country.')]},
)
PAY_BY_APP_DISABLED_DUE_TO_ERRORS_NOT_UPDATED = (
    {'pay_by_app_status': PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS},
    PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS,
    SETTING_PAY_BY_APP_DISABLED,
    VALIDATION_SUCCESS,
    EMPTY_ERRORS,
)
# endregion pay_by_app_status
# endregion data


@pytest.fixture
def pos():
    return baker.make(POS)


@pytest.fixture
def pos_with_payment_types():
    pos_instance = baker.make(POS)
    cash = baker.make(
        PaymentType,
        code=PaymentTypeEnum.CASH,
        pos=pos_instance,
        default=True,
    )
    bank_transfer = baker.make(
        PaymentType,
        code=PaymentTypeEnum.BANK_TRANSFER,
        pos=pos_instance,
    )
    return pos_instance, cash, bank_transfer


@pytest.mark.django_db
def test_account_holder_related_fields_when_account_holder_exist(
    pos,
):  # pylint: disable=redefined-outer-name
    baker.make(AccountHolder, pos=pos, payout_allowed=True)
    serializer = POSSerializer(
        instance=pos,
    )
    assert serializer.data['has_marketpay_account'] is True
    assert serializer.data['marketpay_payouts_enabled'] is True


@pytest.mark.django_db
def test_force_stripe_pba_field(pos):  # pylint: disable=redefined-outer-name
    pos._force_stripe_pba = True  # pylint: disable=protected-access
    pos.save(
        update_fields=[
            '_force_stripe_pba',
        ]
    )
    baker.make(AccountHolder, pos=pos)
    serializer = POSSerializer(
        instance=pos,
    )
    assert serializer.data['force_stripe_pba'] is True


@pytest.mark.django_db
def test_has_stripe_account_field(pos):  # pylint: disable=redefined-outer-name
    serializer = POSSerializer(instance=pos)
    assert serializer.data['has_stripe_account'] is False
    baker.make(StripeAccount, pos=pos)
    serializer = POSSerializer(instance=pos)
    assert serializer.data['has_stripe_account'] is True


@pytest.mark.django_db
def test_account_holder_related_fields_when_account_holder_dont_exist(
    pos,
):  # pylint: disable=redefined-outer-name
    serializer = POSSerializer(
        instance=pos,
    )
    assert serializer.data['has_marketpay_account'] is False
    assert serializer.data['marketpay_payouts_enabled'] is False


@pytest.mark.django_db
def test_transaction_simple(pos):  # pylint: disable=redefined-outer-name
    with override_settings(POS__PAY_BY_APP=True):
        serializer = POSSerializer(
            data={'pay_by_app_status': POS.PAY_BY_APP_PENDING},
            instance=pos,
        )
        serializer.is_valid()
        print("ERRORS:", serializer.errors)
        assert serializer.is_valid()
        print("VALIDATED DATA:")
        pprint(dict(serializer.validated_data), indent=4)

        # save the serializer
        pos = serializer.save()
        assert pos.pay_by_app_request_date is not None

        # if POS will be updated with same status date won't change
        previous_date = pos.pay_by_app_request_date
        serializer = POSSerializer(
            data={'pay_by_app_status': POS.PAY_BY_APP_PENDING},
            instance=pos,
        )
        serializer.is_valid()
        pos = serializer.save()

        assert pos.pay_by_app_request_date == previous_date


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('data', 'expected_validity', 'expected_errors'),
    [
        TIPS_VALIDATION_SUCCESS,
        TIPS_DUPLICATED_RATES,
        TIPS_MULTIPLE_DEFAULT,
        TIPS_NON_INTEGER,
        TIPS_BELOW_ZERO,
        TIPS_OVER_100,
        TAX_RATES_VALIDATION_SUCCESS,
        TAX_RATES_MISSING_DEFAULTS,
        TAX_RATES_OVER_PRECISION,
        TAX_RATES_MULIPLE_PRODUCT_DEFAULTS,
        TAX_RATES_MULIPLE_SERVICE_DEFAULTS,
        TAX_RATES_DUPLICATED_RATES,
        TAX_RATES_SPECIAL_CASE,
    ],
)
def test_pos_update(
    pos, data, expected_validity, expected_errors
):  # pylint: disable=redefined-outer-name
    serializer = POSSerializer(data=data, instance=pos)
    assert serializer.is_valid() == expected_validity
    assert serializer.errors == expected_errors


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('data', 'instance_status', 'settings_value', 'expected_validity', 'expected_errors'),
    [
        PAY_BY_APP_ENABLED_RESEND,
        PAY_BY_APP_ENABLED_ACTIVATION_BY_MERCHANT,
        PAY_BY_APP_NOT_ENABLED_ACTIVATION_BY_MERCHANT,
        PAY_BY_APP_DISABLED_DUE_TO_ERRORS_NOT_UPDATED,
    ],
)
def test_pos_pay_by_app_setting(
    pos, data, instance_status, settings_value, expected_validity, expected_errors
):  # pylint: disable=redefined-outer-name
    with override_settings(POS__PAY_BY_APP=settings_value):
        pos.pay_by_app_status = instance_status
        serializer = POSSerializer(data=data, instance=pos)
        assert serializer.is_valid() == expected_validity
        assert serializer.errors == expected_errors


REGISTERS_CHANGES_DATA_TESTS = [
    ({'registers_enabled': True},),
    ({'registers_max_opened': 4},),
    ({'registers_reopening': False},),
    ({'registers_shared_enabled': True},),
]

REGISTERS_DEFAULTS_DATA_TESTS = [
    ({'registers_enabled': False},),
    ({'registers_max_opened': 1},),
    ({'registers_reopening': True},),
    ({'registers_shared_enabled': False},),
]

REGISTERS_DATA_TESTS = REGISTERS_CHANGES_DATA_TESTS + REGISTERS_DEFAULTS_DATA_TESTS

POS_DATA_TESTS = [
    ({'payment_auto_accept': True},),
    ({'commissions_enabled': True},),
    ({'tips_enabled': True},),
    ({'tax_in_receipt_visible': True},),
]


@pytest.mark.django_db
@pytest.mark.parametrize(('data',), REGISTERS_CHANGES_DATA_TESTS)
def test_register_change_in_pos_with_opened_registers(
    pos, data
):  # pylint: disable=redefined-outer-name
    """When there is at least one register opened, changes in registers section are forbidden."""
    baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=pos)
    baker.make(Register, is_open=True, pos=pos)

    serializer = POSSerializer(data=data, instance=pos)
    assert serializer.is_valid() is False
    assert len(serializer.errors) == 1
    print(serializer.errors)
    assert serializer.errors['non_field_errors'][0].code == 'open_registers'


@pytest.mark.django_db
@pytest.mark.parametrize(('data',), REGISTERS_DEFAULTS_DATA_TESTS)
def test_register_no_change_in_pos_with_opened_registers(
    pos, data
):  # pylint: disable=redefined-outer-name
    """Passing in already active settings should be allowed (no changes to registers occur)."""
    baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=pos)
    baker.make(Register, is_open=True, pos=pos)

    serializer = POSSerializer(data=data, instance=pos)
    assert serializer.is_valid() is True


@pytest.mark.django_db
@pytest.mark.parametrize(('data',), REGISTERS_DATA_TESTS)
def test_register_change_in_pos_without_opened_registers(
    pos, data
):  # pylint: disable=redefined-outer-name
    baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=pos)
    baker.make(Register, is_open=False, pos=pos)
    serializer = POSSerializer(data=data, instance=pos)
    assert serializer.is_valid() is True


@pytest.mark.django_db
@pytest.mark.parametrize(('data',), POS_DATA_TESTS)
def test_pos_change_in_pos_with_opened_registers(pos, data):  # pylint: disable=redefined-outer-name
    baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=pos)
    baker.make(Register, is_open=True, pos=pos)
    serializer = POSSerializer(data=data, instance=pos)
    assert serializer.is_valid() is True


@pytest.mark.django_db
def test_payment_type_get(pos_with_payment_types):  # pylint: disable=redefined-outer-name
    pos_instance, _cash, _bank_transfer = pos_with_payment_types
    serializer = POSSerializer(instance=pos_instance)
    assert serializer.data['payment_types'][0]['editable'] is False
    assert serializer.data['payment_types'][1]['editable'] is True


@pytest.mark.django_db
def test_payment_type_cash_disabled(pos_with_payment_types):  # pylint: disable=redefined-outer-name
    pos_instance, cash, bank_transfer = pos_with_payment_types
    data = {
        'payment_types': [
            {'id': cash.id, 'code': cash.code, 'enabled': False},
            {'id': bank_transfer.id, 'code': bank_transfer.code, 'enabled': True},
        ]
    }
    serializer = POSSerializer(data=data, instance=pos_instance)
    assert serializer.is_valid() is False
    assert serializer.errors['payment_types'][0]['non_field_errors'][0].code == 'cannot_be_disabled'


@pytest.mark.django_db
def test_payment_type_success_update(
    pos_with_payment_types,
):  # pylint: disable=redefined-outer-name
    pos_instance, cash, bank_transfer = pos_with_payment_types
    data = {
        'payment_types': [
            {
                'id': cash.id,
                'code': cash.code,
                'enabled': True,
            },
            {
                'id': bank_transfer.id,
                'code': bank_transfer.code,
                'enabled': True,
            },
        ]
    }

    serializer = POSSerializer(data=data, instance=pos_instance)
    assert serializer.is_valid() is True, serializer.errors

    assert (
        PaymentType.objects.filter(
            enabled=True, code=PaymentTypeEnum.BANK_TRANSFER, pos=pos_instance
        ).exists()
        is True
    )


@pytest.mark.django_db
def test_get_show_new_financial_center_default_pos_settings(
    pos,
):  # pylint: disable=redefined-outer-name
    with override_settings(POS__PAY_BY_APP=True):
        with patch('webapps.pos.serializers.ShowNewFinancialCenterFlag', side_effect=ValueError):
            serializer = POSSerializer(
                data={'pay_by_app_status': POS.PAY_BY_APP_PENDING},
                instance=pos,
            )
            serializer.is_valid()
            serializer.save()
            assert serializer.data['show_new_financial_center'] is False
