# pylint: disable=consider-using-f-string
from typing import Optional
import re

from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin.models import CHANGE, LogEntry
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db.models import Prefetch
from django.db.transaction import atomic
from django.forms.models import BaseInlineFormSet
from django.shortcuts import redirect
from django.templatetags.static import static
from django.urls import (
    re_path as url,
    reverse,
)
from django.utils.encoding import force_bytes
from django.utils.html import escape, format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from lib.datadog.decorators import keep_dd_trace_for_django_admin

from lib.admin_helpers import (
    BaseModelAdmin,
    ChangeLogAdminMixin,
    DeleteSelectedPermissionMixin,
    <PERSON><PERSON><PERSON><PERSON>el<PERSON><PERSON>in,
    ReadOnlyFieldsMixin,
    admin_link,
    get_link_html,
    NoRowsInListViewMixin,
)
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.payment import QuickCardEntryPaymentMethodFlag
from lib.rivers import River, bump_document
from lib.tools import format_currency, get_object_or_404, SimplePaginator
from webapps.admin_extra.custom_permissions_classes import (
    BaseIsInGroupMixin,
    USIsInGroupPermissionMixin,
)
from webapps.admin_extra.tools import export_as_csv_action
from webapps.adyen.models import Auth, Capture, Refund
from webapps.booking.enums import AppointmentStatusChoices
from webapps.business.forms.fields import TableCreator
from webapps.business.models import Business
from webapps.pos.booksy_gift_cards.models import BooksyGiftCard
from webapps.pos.bsx.models import BsxLog, BsxSettings
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    BankAccount,
    CommissionChangeLog,
    HigherPrepaymentSplash,
    HigherPrepaymentSplashDecision,
    OperationFee,
    POS,
    POSChangeLog,
    POSPlan,
    PaymentMethod,
    PaymentRow,
    PaymentRowChange,
    PaymentType,
    Receipt,
    RefundRow,
    Splash,
    TaxRate,
    Tip,
    Transaction,
    TransactionRow,
    TransactionTaxSubtotal,
    POSPlanBatchUpdateLog,
    TippingAfterAppointmentAnswer,
)
from webapps.pos.refund import is_refund_possible
from webapps.pos.serializers import POSChangeLogSerializer
from webapps.pos.tasks import DisablePrepayments
from webapps.pos.tools import (
    check_pay_by_app_status,
    check_payment_types,
    check_tax_rates,
    check_tips,
)
from webapps.printer_api.admin import (
    get_print_receipt_btn,
    get_print_transaction_btn,
    make_print_transaction,
)
from webapps.user.groups import GroupName
from webapps.user.tools import get_user_from_django_request


class TaxRateFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        errors = check_tax_rates([form.cleaned_data for form in self.forms])

        if errors:
            raise forms.ValidationError(*errors)


class TaxRateInline(DeleteSelectedPermissionMixin, admin.TabularInline):
    model = TaxRate
    formset = TaxRateFormSet
    fields = [
        'pos',
        'rate',
        'label',
        'default_for_service',
        'default_for_product',
        'can_be_deleted',
    ]
    readonly_fields = ['label', 'can_be_deleted']

    def get_extra(self, request, obj=None, **kwargs):
        return 0 if obj and obj.tax_rates.count() > 0 else 3

    def can_be_deleted(self, obj):
        if obj.default_for_service or obj.default_for_product:
            return False
        return True


class TipFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        errors = check_tips([form.cleaned_data for form in self.forms])
        if errors:
            raise forms.ValidationError(errors)


class TipInline(admin.TabularInline):
    model = Tip
    formset = TipFormSet
    exclude = ['deleted']

    def get_extra(self, request, obj=None, **kwargs):
        return 0 if obj and obj.tips.count() > 0 else 3


class AddPaymentTypeInline(admin.TabularInline):
    """Inline for new Payment Types."""

    verbose_name = 'Add payment type'
    verbose_name_plural = 'Add payment types'
    model = PaymentType
    exclude = ['deleted']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj=obj, **kwargs)

        already_used_pt_codes = PaymentType.objects.filter(
            pos=obj,
        ).values_list('code', flat=True)

        forbidden_codes = (
            PaymentType.OLD_STYLE_VOUCHERS
            if settings.VOUCHERS_ENABLED
            else PaymentType.VOUCHER_TYPES
        )

        biz_ff_id = (UserData(subject_key=obj.business.id),) if obj.business else ()
        # Functionality renamed to "Keyed In Payment"
        if not QuickCardEntryPaymentMethodFlag(*biz_ff_id):
            forbidden_codes.append(PaymentTypeEnum.KEYED_IN_PAYMENT)

        formset.form.base_fields['code'].choices = [
            (code, '%s (%s)' % (label, code)) if code else (code, label)
            for code, label in formset.form.base_fields['code'].choices
            if not (
                code
                in [
                    PaymentTypeEnum.PAY_BY_APP,
                    PaymentTypeEnum.PREPAYMENT,
                    PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                ]
                or code in already_used_pt_codes
                or code in forbidden_codes
            )
        ]
        return formset

    def get_extra(self, request, obj=None, **kwargs):
        return 1 if obj and obj.payment_types.count() > 0 else 3

    def has_view_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class EditPaymentTypeFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        error = check_payment_types([form.cleaned_data for form in self.forms])
        if error:
            raise forms.ValidationError(error)

        cash_disabled = any(
            form
            for form in self.forms
            if form.cleaned_data['code'] == PaymentTypeEnum.CASH
            and (form.cleaned_data['enabled'] is False or form.cleaned_data['available'] is False)
        )

        if cash_disabled:
            raise forms.ValidationError('Cash cannot be disabled')


class EditPaymentTypeInline(DeleteSelectedPermissionMixin, admin.TabularInline):
    """Inline for existing Payment Types - code field is disabled."""

    model = PaymentType
    formset = EditPaymentTypeFormSet
    readonly_fields = ['can_be_deleted']
    exclude = ['deleted']
    extra = 0

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj=obj, **kwargs)
        formset.form.base_fields['code'].choices = [
            (code, '%s (%s)' % (label, code)) if code else (code, label)
            for code, label in formset.form.base_fields['code'].choices
        ]
        formset.form.base_fields['code'].disabled = True
        return formset

    def get_extra(self, request, obj=None, **kwargs):
        return 0  # add disallowed

    def has_add_permission(self, request, obj=None):
        return False

    def can_be_deleted(self, obj):
        return False


class POSChangeLogInline(ReadOnlyFieldsMixin, ChangeLogAdminMixin, admin.TabularInline):
    model = POSChangeLog
    fields = ['operator', 'created', 'diff_display']
    readonly_fields = ['operator', 'diff_display']

    def get_queryset(self, request):
        qs = super().get_queryset(request).select_related('operator', 'pos').order_by('created')

        return qs


class POSChangeLogAdmin(NoAddDelMixin, ChangeLogAdminMixin, BaseModelAdmin):
    list_display = ('pos', 'operator', 'created', 'diff_display')
    list_display_links = ('pos',)
    search_fields = ('=pos__business__id', 'pos__business__name')
    readonly_fields = ['operator', 'pos']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('operator', 'pos__business', 'pos')


admin.site.register(POSChangeLog, POSChangeLogAdmin)


class PayByAppStatus(admin.SimpleListFilter):
    # Human-readable title which will be displayed in the
    # right admin sidebar just above the filter options.
    title = 'pay_by_app status'

    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'status'

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return POS.PAY_BY_APP_CHOICES

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if not self.value():
            return queryset
        return queryset.filter(pay_by_app_status=self.value())


@atomic
def change_status(queryset, status, operator_id):
    active_pos = queryset.filter(active=True)

    serializer = POSChangeLogSerializer(active_pos, many=True)
    POSChangeLog.create_bulk_history(serializer.data, operator_id)

    active_pos.update(fraud_status=status)


def mark_fraud(modeladmin, request, queryset):  # pylint: disable=unused-argument
    if not POSAdmin.is_user_in_groups(request.user):
        messages.error(
            request,
            'You are not allowed to do it!',
        )
        return

    change_status(queryset, POS.FRAUD_STATUS_FRAUD, request.user.id)


mark_fraud.short_description = 'Mark with status Fraud'


def mark_whitelist(modeladmin, request, queryset):  # pylint: disable=unused-argument
    if not POSAdmin.is_user_in_groups(request.user):
        messages.error(
            request,
            'You are not allowed to do it!',
        )
        return

    change_status(queryset, POS.FRAUD_STATUS_WHITELIST, request.user.id)


mark_whitelist.short_description = 'Mark with status Whitelisted'


class POSForm(forms.ModelForm):
    class Meta:
        model = POS
        exclude = ()  # pylint: disable=modelform-uses-exclude
        labels = {
            'active': 'POS active',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = getattr(self, 'instance', None)

        if not instance:
            return

        if not (
            # Disable prepayment option field when disabled in country
            settings.POS__PREPAYMENTS
            and
            # POS doesn't have PBA
            instance.is_pay_by_app_active
        ):
            self.fields['prepayment_enabled'].widget.attrs['disabled'] = True

        if not (
            # Disable refund option field when disabled in country
            settings.POS__REFUNDS
            and
            # POS doesn't have PBA
            instance.is_pay_by_app_active
        ):
            self.fields['refund_enabled'].widget.attrs['disabled'] = True

    def clean(self):
        from webapps.pos.calculations import round_currency

        # Check if service fee are corresponding to currency decimal length
        service_fee = self.cleaned_data.get('service_fee')
        if round_currency(service_fee) != service_fee:
            raise forms.ValidationError(
                'Service fee value is not corresponding to tip rounding mode',
                params={
                    'code': 'invalid',
                    'field': 'service_fee',
                    'type': 'validation',
                },
            )

        if (
            self.initial.get('registers_shared_enabled') is True
            and self.cleaned_data.get('registers_shared_enabled') is False
        ):
            if self.instance.registers.filter(is_open=True).count() > 0:
                raise forms.ValidationError(
                    _('Before turning off shared registers close all of them'),
                    params={
                        'code': 'invalid',
                        'field': 'registers_shared_enabled',
                        'type': 'validation',
                    },
                )
        return self.cleaned_data

    def clean_pay_by_app_status(self):
        error = check_pay_by_app_status(
            instance=self.instance,
            value=self.cleaned_data['pay_by_app_status'],
        )
        if error:
            raise forms.ValidationError(error)

    def clean_pos_plans(self):
        pos_plans = self.cleaned_data['pos_plans']

        pos_plans_types = [pos_plan.plan_type for pos_plan in pos_plans]

        if len(set(pos_plans_types)) != len(pos_plans):
            raise forms.ValidationError(
                _('Only one POSPlan per type is allowed.'),
                params={
                    'code': 'invalid',
                    'field': 'pos_plans',
                    'type': 'validation',
                },
            )

        return pos_plans


class POSAdmin(USIsInGroupPermissionMixin, NoAddDelMixin, ChangeLogAdminMixin, BaseModelAdmin):
    inlines = [
        TaxRateInline,
        TipInline,
        EditPaymentTypeInline,
        AddPaymentTypeInline,
    ]

    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('business', 'business_active', 'active'),
                    ('service_tax_mode', 'product_tax_mode', 'voucher_tax_mode'),
                    ('tips_enabled', 'tip_calculation_mode'),
                    ('tax_in_receipt_visible',),
                    ('commissions_enabled', 'products_stock_enabled'),
                    ('payment_auto_accept', 'force_pba_for_cf'),
                    ('item_discount_enabled', 'global_discount_enabled'),
                    ('service_fee',),
                    ('receipt_footer_line_1',),
                )
                + (
                    (
                        'marketpay_enabled',
                        'restrict_payment_access',
                        'auto_charge_cancellation_fee',
                    )
                    if settings.MARKET_PAY_ENABLED
                    else ()
                )
                + (('stripe_terminal_enabled',) if settings.POS__STRIPE_TERMINAL else ())
                + (('tap_to_pay_enabled',) if settings.POS__TAP_TO_PAY else ())
                + (('tap_to_pay_status',) if settings.POS__TAP_TO_PAY else ())
                + ('_force_stripe_pba',)
                + ('force_stripe_kyc',)
                + ('pos_refactor_stage2_migration_date',)
                + ('pos_refactor_stage2_migration_group',)
                + ('payout_method_change_allowed',)
                + ('blik_in_prepayment_promo_enabled',)
                + (('booksy_pay_enabled',) if settings.POS__BOOKSY_PAY else ())
                + ('get_booksy_pay_late_cancellation_window',)
                + ('ba_deposit_enabled',)
                + ('promote_payment_method_availability_to_customers',)
            },
        ),
        (
            'Registers',
            {
                'fields': [
                    'registers_enabled',
                    'registers_max_opened',
                    'registers_reopening',
                    'registers_shared_enabled',
                ]
            },
        ),
        (
            'Prepayments & Refunds',
            {
                'fields': [
                    'prepayment_enabled',
                    'refund_enabled',
                ],
            },
        ),
        (
            (
                'Fast Payouts',
                {
                    'fields': [
                        'fast_payouts_visible',
                        'fast_payouts_admin_blocked',
                        'fast_payouts_merchant_enabled',
                    ],
                },
            )
            if settings.POS__FAST_PAYOUTS
            else ((), {'fields': []})
        ),
        (
            'POS Plan',
            {
                'fields': [
                    'deprecated_pos_plan',
                    'deprecated_pos_plan_locked',
                    'pos_plans',
                ],
            },
        ),
        ('POS Changes', {'classes': ('collapse',), 'fields': ('pos_change_log',)}),
    )
    list_display = (
        'verification',
        'fraud_status',
        'settings_name',
        'business_link',
        'change_log',
        'pay_by_app_status',
        'pay_by_app_request_date',
    )
    list_display_links = ('settings_name',)
    search_fields = ['=business__id']
    list_filter = [
        'active',
        PayByAppStatus,
        'tap_to_pay_status',
        'pay_by_app_request_date',
        'business__verification',
        'fraud_status',
        'booksy_pay_enabled',
        'ba_deposit_enabled',
    ]
    raw_id_fields = ['business', 'pos_plans']
    actions = [
        mark_fraud,
        mark_whitelist,
    ]
    readonly_fields = [
        'business',
        'business_active',
        'pos_change_log',
        'donations_enabled',
        'restrict_payment_access',
        'deprecated_pos_plan',
        'deprecated_pos_plan_locked',
        'tap_to_pay_status',
        'get_booksy_pay_late_cancellation_window',
    ]
    form = POSForm
    change_list_template = "admin/change_lists/change_list__pos.html"
    actions_on_top = True
    groups = [GroupName.POS_SPECIAL_ACTIONS]

    def __init__(self, *args, **kwargs):
        self.switch_pay_by_app = None
        self.pos__active = False
        self.restrict_booksy_card_reader = None
        super().__init__(*args, **kwargs)

    def get_queryset(self, request):  # pylint: disable=duplicate-code
        return (
            super()
            .get_queryset(request)
            .select_related(
                'business',
                'business__owner',
            )
            .prefetch_related(
                'tips',
                'tax_rates',
                'payment_types',
                'change_logs',
            )
        )

    def get_readonly_fields(self, request, obj=None):
        dynamic_fields = []
        user = request.user
        is_superuser = user.is_superuser
        if not is_superuser:
            dynamic_fields.append('active')

        payout_method_change_allowed = (
            is_superuser or user.groups.filter(name__in=[GroupName.PAYOUT_METHOD_CHANGE]).exists()
        )
        if not payout_method_change_allowed:
            dynamic_fields.append('payout_method_change_allowed')

        if not self.is_user_in_groups(request.user):
            # 61190 - allow enlightened users turn off KYC
            # or make any changes regarding marketpay
            if obj and obj.marketpay_enabled:
                dynamic_fields.append('marketpay_enabled')
                dynamic_fields.append('restrict_payment_access')
            if obj and obj.stripe_terminal_enabled:
                dynamic_fields.append('stripe_terminal_enabled')
            if obj and obj.tap_to_pay_enabled:
                dynamic_fields.append('tap_to_pay_enabled')
        return self.readonly_fields + dynamic_fields

    def pos_change_log(self, obj):
        max_num_change = 5
        pos_changes_qs = (
            POSChangeLog.objects.filter(pos_id=obj.id)
            .select_related('operator', 'pos')
            .order_by('created')
        )
        slice_end = pos_changes_qs.count()
        slice_start = slice_end - max_num_change
        if slice_start > 0:
            # get only last max_num_change
            pos_changes = pos_changes_qs[slice_start:slice_end]
        else:
            # nothing to slice less then max_num_change items
            pos_changes, _pos_changes_cache = pos_changes_qs, pos_changes_qs
        items = (
            '<b>ID:</b> {id} <br/>'
            '<b>OPERATOR:</b> {operator} <br/>'
            '<b>CREATED:</b> {created} <br/>'
            '<b>diff:</b> {diff} <br/>'.format(
                id=change.id,
                operator=change.operator.email if change.operator else "Booksy",
                created=str(change.created),
                diff=self.diff_display(change),
            )
            for change in pos_changes[::-1]
        )
        return mark_safe(''.join(items))  # nosemgrep: avoid-mark-safe

    @staticmethod
    def business_link(obj):
        biz = obj.business
        if biz is None:
            return '-'
        return biz.admin_id_link

    business_link.short_description = 'Business'
    business_link.admin_order_field = 'business_id'

    @staticmethod
    def verification(obj):
        # default settings has no business
        if not obj.business:
            return
        img = ''
        if obj.business.verification == Business.Verification.VERIFIED:
            img = '<img src="{}"> '.format(static('admin/img/icon-yes.svg'))
        elif obj.business.verification == Business.Verification.NEGATIVE:
            img = '<img src="{}"> '.format(static('admin/img/icon-no.svg'))
        return format_html(img + obj.business.get_verification_display())

    verification.short_description = 'Business Verification'

    def save_model(self, request, obj, form, change):
        registers_will_be_disabled = not form.cleaned_data['registers_enabled']
        current_registers_enabled = form.instance.get_dirty_fields().get('registers_enabled', False)
        if current_registers_enabled and registers_will_be_disabled:
            form.instance.clear_cash_registers(operator_id=request.user.id)

        if (
            form.initial['prepayment_enabled'] is True
            and form.cleaned_data['prepayment_enabled'] is False
            and obj.business
        ):
            DisablePrepayments.delay(business_id=obj.business.id)

        super().save_model(request, obj, form, change)
        # do not reindex default pos
        if obj.business:
            bump_document(River.BUSINESS, obj.business.id)

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)

        instance = form.instance
        serializer = POSChangeLogSerializer(instance=instance)
        instance.log_changes(request.user.id, serializer.data)

    @staticmethod
    def settings_name(obj):
        return str(obj)

    settings_name.short_description = 'POS Settings'

    @staticmethod
    def change_log(obj):
        last_log = obj.change_logs.first()

        return format_html(
            '<a href="{}?pos_id__exact={}">{}</a> {}',
            reverse('admin:pos_poschangelog_changelist'),
            obj.id,
            'Show Change Logs',
            'Last Change: {}'.format(last_log.created) if last_log else '',
        )

    change_log.short_description = 'Change Log'

    def get_switch_pay_by_app(self, request, obj):
        if not self.is_user_in_groups(request.user):
            return ''

        if obj.pay_by_app_status != POS.PAY_BY_APP_ENABLED:
            phrase = 'Enable'
            fun = 'enable_pay_by_app'
        else:
            phrase = 'Disable'
            fun = 'disable_pay_by_app'
        return (
            (
                """
            <a href="%s" class="btn ">
                {} pay by app
            </a>
        """.format(
                    phrase
                )
            )
            % reverse("admin:{}".format(fun), args=(obj.id,))
        )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'recalculate_plans',
                self.admin_site.admin_view(self.recalculate_plans),
                name='recalculate_plans',
            ),
            url(
                r'^(?P<pos_id>\d+)/enable_pay_by_app/$',
                self.admin_site.admin_view(self.enable_pay_by_app),
                name='enable_pay_by_app',
            ),
            url(
                r'^(?P<pos_id>\d+)/disable_pay_by_app/$',
                self.admin_site.admin_view(self.disable_pay_by_app),
                name='disable_pay_by_app',
            ),
            url(
                r'^(?P<pos_id>\d+)/restrict_pba/$',
                self.admin_site.admin_view(
                    self.restrict_pay_by_app,
                ),
                name='restrict_payment_access_from_view',
            ),
            url(
                r'^(?P<pos_id>\d+)/restrict_bcr/$',
                self.admin_site.admin_view(
                    self.restrict_stripe_terminal,
                ),
                name='restrict_stripe_terminal_from_view',
            ),
            url(
                r'^(?P<pos_id>\d+)/restore_bcr/$',
                self.admin_site.admin_view(
                    self.restore_stripe_terminal,
                ),
                name='restore_stripe_terminal_from_view',
            ),
        ]
        return additional_urls + urls

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.switch_pay_by_app = self.get_switch_pay_by_app(request, obj)
        self.pos__active = obj.active
        self.restrict_booksy_card_reader = self.get_restrict_booksy_card_reader(request, obj)
        form = super().get_form(request, obj, **kwargs)
        return form

    def get_restrict_booksy_card_reader(self, request, obj):
        if not (
            settings.POS__STRIPE_TERMINAL
            and obj.stripe_account
            and self.is_user_in_groups(request.user)
        ):
            return ""
        if obj.stripe_account.blocked:
            action = "restore"
        else:
            action = "restrict"

        action_url = reverse(
            f"admin:{action}_stripe_terminal_from_view",
            args=(obj.id,),
        )
        return f'<a class="btn" href="{action_url}">{_(f"{action} Booksy Card Reader")}</a>'

    @staticmethod
    def disable_pay_by_app(request, pos_id):
        pos = POS.objects.get(id=pos_id)

        if pos.disable_pay_by_app():
            messages.success(request, 'Pay By app is disabled')
        else:
            messages.error(request, 'Pay By app is already disabled')

        pos.refresh_from_db()
        serializer = POSChangeLogSerializer(instance=pos)
        pos.log_changes(request.user.id, serializer.data)

        return redirect(
            reverse(
                'admin:pos_pos_change',
                args=(pos_id,),
            )
        )

    @staticmethod
    def restrict_payment_access(obj: POS) -> str:
        action_url = reverse(
            "admin:restrict_payment_access_from_view",
            args=(obj.id,),
        )
        return format_html(
            '<a class="btn" href="{}">{}</a>', action_url, _("Restrict payment access")
        )

    @staticmethod
    def restrict_pay_by_app(request, pos_id: int) -> None:
        pos = POS.objects.get(id=pos_id)
        pos.restrict_pba()

        return redirect(
            reverse(
                'admin:pos_pos_change',
                args=(pos_id,),
            )
        )

    @staticmethod
    def restrict_stripe_terminal(request, pos_id: int) -> None:
        pos = POS.objects.get(id=pos_id)
        pos.restrict_stripe_terminal()

        return redirect(
            reverse(
                'admin:pos_pos_change',
                args=(pos_id,),
            )
        )

    @staticmethod
    def restore_stripe_terminal(request, pos_id: int) -> None:
        pos = POS.objects.get(id=pos_id)
        pos.restore_stripe_terminal()

        return redirect(
            reverse(
                'admin:pos_pos_change',
                args=(pos_id,),
            )
        )

    @staticmethod
    def enable_pay_by_app(request, pos_id):
        from webapps.notification.scenarios import start_scenario
        from webapps.notification.scenarios.scenarios_noshowprotection import (
            NoShowInstructionScenario,
        )

        pos = POS.objects.get(id=pos_id)

        if not settings.POS__PAY_BY_APP:
            messages.error(request, 'Pay by app can\'t be enabled for this country')
        elif pos.enable_pay_by_app():
            messages.success(request, 'Pay by app is enabled')
            start_scenario(
                NoShowInstructionScenario,
                business_id=pos.business.id,
            )
        else:
            messages.error(request, 'Pay by app is already enabled')

        pos.refresh_from_db()
        serializer = POSChangeLogSerializer(instance=pos)
        pos.log_changes(request.user.id, serializer.data)

        return redirect(
            reverse(
                'admin:pos_pos_change',
                args=(pos_id,),
            )
        )

    @staticmethod
    def recalculate_plans(request):
        from webapps.pos.tasks import recalculate_pos_plans

        recalculate_pos_plans.delay()
        messages.success(request, 'POS Plans will refresh in few seconds.')

        return redirect(request.META.get('HTTP_REFERER', '/'))

    @staticmethod
    def business_active(obj: POS) -> Optional[bool]:
        return obj.business and obj.business.active

    @admin.display(description="Booksy Pay Late Cancellation Window")
    def get_booksy_pay_late_cancellation_window(self, obj):
        # regex used to show "hours+=8" instead of "relativedelta(hours+=8)"
        return re.search(
            r"\((.*?)\)", str(obj.booksy_pay_late_cancellation_window_or_default)
        ).group(1)

    business_active.boolean = True


admin.site.register(POS, POSAdmin)


class POSPlanForm(forms.ModelForm):
    class Meta:
        model = POSPlan
        fields = (
            'plan_type',
            'min_txn_num',
            'provision',
            'txn_fee',
            'refund_provision',
            'refund_txn_fee',
            'chargeback_provision',
            'chargeback_txn_fee',
            'individual',
        )

    def clean_provision(self):
        provision = self.cleaned_data['provision']
        if provision < 0:
            raise forms.ValidationError('Provision should not be lower than 0.')

        return provision

    def clean(self):
        try:
            POSPlan.validate_non_individual_unique(self.instance, **self.cleaned_data)
        except DjangoValidationError as validation_error:
            raise forms.ValidationError(
                'Global plan with min_txn_num = {} already exists'.format(
                    self.cleaned_data['min_txn_num']
                )
            ) from validation_error

        return self.cleaned_data


class POSPlanAdmin(BaseModelAdmin):
    model = POSPlan
    fields = (
        'id',
        'plan_type',
        'min_txn_num',
        'provision',
        'txn_fee',
        'refund_provision',
        'refund_txn_fee',
        'chargeback_provision',
        'chargeback_txn_fee',
        'individual',
        'how_many_poses',
    )
    list_display = (
        'id',
        'plan_type',
        'min_txn_num',
        'provision',
        'txn_fee',
        'individual',
    )
    form = POSPlanForm
    readonly_fields = (
        'id',
        'how_many_poses',
    )

    @staticmethod
    def how_many_poses(obj):
        return obj.poses.count()


admin.site.register(POSPlan, POSPlanAdmin)


class POSPlanBatchUpdateLogAdmin(NoAddDelMixin, BaseModelAdmin):
    model = POSPlanBatchUpdateLog
    fields = (
        'id',
        'operator',
        'status',
        'data',
    )
    list_display = (
        'id',
        'operator',
        'status',
        'created',
    )
    readonly_fields = (
        'id',
        'operator',
        'status',
        'data',
    )
    search_fields = ('=created',)

    list_filter = ('status',)


admin.site.register(POSPlanBatchUpdateLog, POSPlanBatchUpdateLogAdmin)


class ReceiptInline(ReadOnlyFieldsMixin, NoAddDelMixin, admin.TabularInline):
    model = Receipt

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):

        field = super().formfield_for_foreignkey(db_field, request, **kwargs)

        if db_field.name == 'payment_type':
            if request._obj is not None:  # pylint: disable=protected-access
                field.queryset = field.queryset.filter(
                    pos=request._obj.pos  # pylint: disable=protected-access
                )
            else:
                field.queryset = field.queryset.none()

        return field


class TransactionRowInline(
    ChangeLogAdminMixin, ReadOnlyFieldsMixin, NoAddDelMixin, admin.StackedInline
):
    model = TransactionRow
    raw_id_fields = (
        'subbooking',
        'service_variant',
        'product',
    )

    readonly_fields = ('commission_change_log',)

    class Media:
        js = [
            'https://code.jquery.com/jquery-2.2.4.min.js',
            'js/collapsed_stacked_inlines.js',
        ]

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj=obj)
        fields[fields.index('service_variant')] = 'service_variant_link'
        return fields

    def get_readonly_fields(self, request, obj=None):
        fields = super().get_readonly_fields(request, obj=obj)
        fields[fields.index('service_variant')] = 'service_variant_link'
        if obj is not None:
            fields.insert(fields.index('commission_staffer'), 'commission')
        return fields

    @staticmethod
    def commission(obj):
        return obj.commission

    def commission_change_log(self, obj):
        max_num_change = 20
        commission_changes_qs = CommissionChangeLog.objects.filter(
            transaction_row_id=obj.id,
        ).order_by('created')

        slice_end = commission_changes_qs.count()
        slice_start = slice_end - max_num_change
        if slice_start > 0:
            # get only last max_num_change
            commission_changes = commission_changes_qs[slice_start:slice_end]
        else:
            # nothing to slice less then max_num_change items
            commission_changes = commission_changes_qs

        diff_table = TableCreator(('Created', 'Operator', 'Diff'))
        return format_html(
            diff_table.form_table(
                [
                    [
                        str(change.created),
                        change.operator.email if change.operator else "Booksy",
                        self.diff_display(change),
                    ]
                    for change in commission_changes[::-1]
                ]
            )
        )

    @admin.display(description='Service variant')
    def service_variant_link(self, obj):
        if obj.service_variant:
            return format_html(
                '<a href="{}?version={}">{}</a>',
                admin_link(obj.service_variant),
                obj.service_variant_version,
                obj.service_variant,
            )


class TransactionTaxSubtotalInline(ReadOnlyFieldsMixin, NoAddDelMixin, admin.TabularInline):
    model = TransactionTaxSubtotal
    readonly_fields = ('created', 'updated', 'deleted')


class TransactionSeriesMixin:
    @staticmethod
    def transaction_series(obj):
        series = obj.series().order_by('id')
        rows = []

        table = TableCreator(
            (
                'Transaction ID',
                'Receipt ID',
                'Receipt Created',
                'Total',
                'Receipt Status',
                'Payment Rows',
            ),
            border_size=2,
        )
        payment_row_table = TableCreator(
            (
                'ID',
                'Created',
                'Status',
                'Payment Type',
                'Amount',
                'Pnref',
                'Card Type',
                'Last digits',
                'Provider',
                'Settled',
                'Settle',
            )
        )

        for transaction in series:
            for receipt in transaction.receipts.all().order_by('id'):

                payment_rows = payment_row_table.form_table(
                    [
                        [
                            payment_row_table.get_id_link(row),
                            row.created,
                            row.get_status_display(),
                            row.payment_type.get_code_display(),
                            format_currency(row.amount),
                            BasePaymentRowAdmin.adyen_obj(row),
                            row.card_type,
                            row.card_last_digits,
                            row.provider,
                            row.settled,
                            (
                                BasePaymentRowAdmin.get_pr_button(row)
                                if BasePaymentRowAdmin.editable(row)
                                else ""
                            ),
                        ]
                        for row in receipt.payment_rows.all()
                    ]
                )

                row = [
                    table.get_id_link(transaction),
                    table.get_id_link(receipt),
                    receipt.created,
                    format_currency(transaction.total),
                    receipt.get_status_type_display(),
                    force_bytes(payment_rows),
                ]

                rows.append(row)

        return format_html(table.form_table(rows))


class AppointmentStatusFilter(admin.SimpleListFilter):
    title = 'Related Appointment status'
    parameter_name = 'appointment_status'

    def lookups(self, request, model_admin):
        return AppointmentStatusChoices.choices()

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        return queryset.filter(appointment__status=self.value())


class TransactionAdmin(
    ReadOnlyFieldsMixin,
    NoAddDelMixin,
    NoRowsInListViewMixin,
    BaseModelAdmin,
    TransactionSeriesMixin,
):
    paginator = SimplePaginator
    show_full_result_count = False
    list_max_show_all = 20
    list_per_page = 20
    inlines = [
        TransactionTaxSubtotalInline,
        TransactionRowInline,
    ]
    raw_id_fields = (
        'pos',
        'operator',
        'latest_receipt',
        'customer',
        'customer_card',
        'appointment',
        'deposit',
    )
    list_display = (
        'id',
        'charge_date',
        'created',
        'updated',
        'total',
        'pos',
        'transaction_type',
        'latest_receipt',
        'status',
        'status_type',
        'payment_type',
        'readable_appointment',
        'readable_customer',
        'readable_fullname',
    )
    list_select_related = ('appointment', 'customer')
    search_fields = (
        '=id',
        '=operator__id',
        '=customer__id',
        '=customer_card__id',
        '=pos__business__id',
        'business_name',
        'customer_data',
        'updated',
    )
    query_fields = [
        'id',
        'latest_receipt__receipt_number',
        'pos__business_id',
        'customer__id',
        'customer__email',
        'receipts__payment_rows__id',
        'appointment_id',
        'latest_receipt__payment_rows__intents__external_id',
    ]
    list_filter = (
        'transaction_type',
        'latest_receipt__status_code',
        'latest_receipt__payment_type__code',
        'updated',
        'receipts__payment_rows__payment_link',
        AppointmentStatusFilter,
    )
    readonly_fields = ('transaction_series',)
    exclude = ('settled', 'ready_for_settle')
    date_hierarchy = 'charge_date'
    # django-admin-lightweight-date-hierarchy
    date_hierarchy_drilldown = False

    actions = [
        export_as_csv_action(
            fields=list_display,
            header=True,
            force_fields=True,
        ),
    ]

    @staticmethod
    def has_archive_permission(request, obj=None):
        if (
            not request.user.is_superuser
            or obj is None
            or obj.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        ):
            return False

        if obj.latest_receipt:
            payment_rows = obj.latest_receipt.payment_rows.all()

            if obj.latest_receipt.status_code not in receipt_status.DELETABLE_STATUSES:
                return False

            # Note: We use Transaction.lock on frontend but in admin need to
            # be more lenient, eg to allow removal for cancelled bookings.
            transaction_lock = (
                obj.latest_receipt.status_code not in receipt_status.NOT_LOCKING_STATUSES
            ) or obj.children.exists()

            if transaction_lock or [x for x in payment_rows if x.locked]:
                return False

        return True

    @staticmethod
    def archive_transaction_view(request, transaction_id):
        txn = Transaction.objects.get(id=transaction_id)
        if txn.latest_receipt.status_code == receipt_status.PARK_SALE:
            # Park sale can be deleted. It is kind of draft.
            transaction_series = txn.series()
            for transaction in transaction_series:
                transaction.soft_delete()
        else:
            txn.update_payment_rows(
                receipt_status.ARCHIVED,
                receipt_number=txn.latest_receipt.receipt_number,
                log_action=PaymentRowChange.MULTI_ROW_UPDATE,
                log_note='delete transaction from admin',
            )

        return redirect(request.META.get('HTTP_REFERER', '/'))

    def get_queryset(self, request):
        qs = super().get_queryset(request=request)
        return qs.prefetch_related(
            'latest_receipt',
            'latest_receipt__payment_type',
            Prefetch(
                'pos',
                queryset=POS.objects.select_related('business').prefetch_related('payment_types'),
            ),
        ).select_related(
            'appointment',
            'customer',
            'customer_card',
        )

    def get_form(self, request, obj=None, change=False, **kwargs):
        # save obj reference for future processing in Inlines
        request._obj = obj  # pylint: disable=protected-access
        self.archive_transaction_btn = self.get_archive_transaction_btn(request, obj)
        self.make_print = get_print_transaction_btn(obj.id)
        return super().get_form(request, obj, **kwargs)

    def get_urls(self):
        urls = super().get_urls()

        additional_urls = [
            url(
                r'^(?P<transaction_id>\d+)/archive_transaction',
                self.admin_site.admin_view(self.archive_transaction_view),
                name='archive_transaction',
            ),
            url(
                r'^(?P<transaction_id>\d+)/print/$',
                self.admin_site.admin_view(make_print_transaction),
                name='make_print',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def status(obj):
        if obj.latest_receipt is None:
            return None
        return obj.latest_receipt.get_status_code_display()

    @staticmethod
    def status_type(obj):
        if obj.latest_receipt is None:
            return None
        return obj.latest_receipt.get_status_type_display()

    @staticmethod
    def payment_type(obj):
        if obj.latest_receipt is None or obj.latest_receipt.payment_type is None:
            return None
        return obj.latest_receipt.payment_type.label

    @classmethod
    def get_archive_transaction_btn(cls, request, obj):
        if obj is None or not cls.has_archive_permission(request, obj):
            return ''

        return '<a href={}" class="btn btn-danger">Archive transaction</a>'.format(
            reverse("admin:archive_transaction", args=(obj.id,))
        )


admin.site.register(Transaction, TransactionAdmin)


class PaymentMethodAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    list_filter = ['provider']
    list_display = [
        'id',
        'user_link',
        'provider',
        'method_type',
        'card_type',
        'card_last_digits',
        'default',
    ]
    search_fields = [
        '=id',
        '=user__id',
        'user__email',
    ]
    fields = [
        'id',
        'created',
        'updated',
        'deleted',
        'user_link',
        'provider',
        'provider_ref',
        'auth_link',
        'method_type',
        'card_type',
        'card_last_digits',
        'default',
        'tokenized_pm_id',
    ]
    readonly_fields = ['user_link', 'auth_link']

    @staticmethod
    def user_link(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.user), str(escape(obj.user)))

    @staticmethod
    def auth_link(obj):
        auth_obj = Auth.objects.filter(reference=obj.provider_ref).first()
        if auth_obj:
            return get_link_html(auth_obj)
        return ''

    user_link.short_description = 'User'
    user_link.admin_order_field = 'user_id'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


admin.site.register(PaymentMethod, PaymentMethodAdmin)


class BankAccountAdmin(BaseModelAdmin):
    list_display = ['id', 'routing_number', 'account_number', 'type', 'pos']
    list_display_links = list_display
    fields = ['routing_number', 'account_number', 'type', 'pos']
    raw_id_fields = ['pos']

    search_fields = [
        '=pos__business__id',
        '=pos__business__name',
    ]


admin.site.register(BankAccount, BankAccountAdmin)


class ReceiptAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, NoRowsInListViewMixin, BaseModelAdmin):
    paginator = SimplePaginator
    show_full_result_count = False
    list_display = ['created', '_transaction', 'status_code', '_payment_type']
    search_fields = ('=id', 'transaction__id', 'status_code')
    readonly_fields = ('transaction_id', 'payment_rows', 'assigned_number')

    fieldsets = (
        (None, {'fields': (('created', 'updated', 'deleted'),)}),
        (
            'Receipt Details',
            {
                'fields': (
                    'id',
                    'transaction_id',
                    'status_code',
                    'assigned_number',
                )
            },
        ),
        (
            'Payment Details',
            {
                'fields': (
                    'card_type',
                    'card_last_digits',
                    'pnref',
                    'provider',
                )
            },
        ),
        ('Payment Rows', {'fields': ('payment_rows',)}),
    )

    @staticmethod
    def _transaction(obj):
        # this is done on purpose to prevent orm from automatically performing select_related.
        # We want to use prefetch_related for performance reasons
        return obj.transaction

    _transaction.short_description = 'Transaction'

    @staticmethod
    def _payment_type(obj):
        # note as above (_transaction)
        return obj.payment_type

    _payment_type.short_description = 'Payment Type'

    def get_queryset(self, request):
        return (
            Receipt.objects.all()
            .prefetch_related(
                Prefetch(
                    "transaction",
                    queryset=Transaction.objects.only("id", "total", "transaction_type"),
                ),
                Prefetch("payment_type", queryset=PaymentType.objects.only("id", "code")),
            )
            .only(
                "id",
                "created",
                "status_code",
                "transaction",
                "payment_type",
            )
        )

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.make_print = get_print_receipt_btn(obj)
        form = super().get_form(request, obj, **kwargs)
        return form

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<transaction_id>\d+)/print/$',
                self.admin_site.admin_view(make_print_transaction),
                name='make_print',
            ),
        ]
        return additional_urls + urls

    def has_delete_permission(self, request, obj=None):
        return True

    @staticmethod
    def editable(obj):
        return obj.transaction.latest_receipt == obj

    @staticmethod
    def transaction_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.transaction), obj.transaction.id)

    def payment_rows(self, obj):
        payment_row_table = TableCreator(
            (
                'ID',
                'Status',
                'Payment Type',
                'Amount',
                'Pnref',
                'Card Type',
                'Last digits',
                'Provider',
                'Settled',
                'Settle',
            )
        )

        payment_rows = payment_row_table.form_table(
            [
                [
                    payment_row_table.get_id_link(row),
                    row.get_status_display(),
                    row.payment_type.get_code_display(),
                    format_currency(row.amount),
                    BasePaymentRowAdmin.adyen_obj(row),
                    row.card_type,
                    row.card_last_digits,
                    row.provider,
                    row.settled,
                    BasePaymentRowAdmin.get_pr_button(row) if self.editable(obj) else "",
                ]
                for row in obj.payment_rows.all()
            ]
        )

        return format_html(payment_rows)


admin.site.register(Receipt, ReceiptAdmin)


class PaymentTypeListFilter(admin.SimpleListFilter):
    """Simple filter listing all values of PaymentType.CODE_CHOICES.

    Fixing performance problem of not distinct PaymentType.code
    """

    title = 'PaymentType'
    parameter_name = 'payment_type__code'

    def lookups(self, request, model_admin):
        return PaymentTypeEnum.choices()

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(payment_type__code=self.value())


class OperationFeeInline(admin.TabularInline):
    class Media:
        css = {'all': (settings.STATIC_URL + 'css/noaddanother.css',)}

    model = OperationFee
    readonly_fields = ('operation_id', 'formatted_amount', 'parts', 'type', 'settled')
    fields = ('operation_id', 'formatted_amount', 'parts', 'type', 'settled')
    show_change_link = True
    can_delete = False
    extra = 0

    @staticmethod
    def operation_id(obj):
        return obj.id
        # return format_html(
        #     '<a href="{}">{}</a>', admin_link(obj), obj.id
        # )


class BasePaymentRowAdmin(
    BaseIsInGroupMixin,
    ReadOnlyFieldsMixin,
    NoAddDelMixin,
    BaseModelAdmin,
):
    list_max_show_all = 20000
    list_per_page = 100
    list_display = [
        'id',
        'created',
        'receipt',
        'status',
        'payment_type',
        'settled',
        'refund_requested',
        'business_id',
        'transaction_id',
        'appointment_id',
        'customer_id',
    ]

    readonly_fields = [
        'adyen_obj',
        'business_id',
        'transaction_id',
        'receipt_id',
        'mass_payment_id',
        'send_for_refund',
        'stripe_payment_intent',
    ]

    search_fields = (
        '=id',
        '=receipt__id',
        'status',
    )

    query_fields = (
        'id',
        # receipt__transaction__pos__business_id__icontains
        # will throw error FieldError: Related Field got invalid lookup
        'receipt__transaction__pos__business__id',
        'receipt__receipt_number',
    )

    list_filter = ('status', 'settled', PaymentTypeListFilter)
    inlines = (OperationFeeInline,)

    ordering = ('-id',)
    groups = [GroupName.PAYMENT_ROW_ISSUE_REFUND]

    def __init__(self, *args, **kwargs):
        self.switch_settled_btn = None
        self.is_allowed = False
        super().__init__(*args, **kwargs)

    def get_readonly_fields(self, request, obj=None):
        # check if user can send for refund
        self.is_allowed = self.is_user_in_groups(request.user)
        return super().get_readonly_fields(request, obj)

    def get_queryset(self, request):  # pylint: disable=duplicate-code
        return (
            super()
            .get_queryset(request)
            .select_related(
                'payment_type',
                'receipt__transaction__appointment',
                'receipt__transaction__customer',
                'receipt__transaction__pos__business',
            )
        )

    @staticmethod
    def editable(obj):
        return (
            not obj.receipt.transaction.children.all()
            and obj.receipt.transaction.latest_receipt == obj.receipt
        )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<payment_row_id>\d+)/switch_settled/$',
                self.admin_site.admin_view(self.pr_switch_settled),
                name='pr_switch_settled',
            ),
            url(
                r'^(?P<payment_row_id>\d+)/send_for_refund/$',
                self.admin_site.admin_view(self.send_for_refund_view),
                name='send_for_refund_view',
            ),
        ]
        return additional_urls + urls

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.switch_settled_btn = self.get_pr_button(obj)
        return super().get_form(request, obj, **kwargs)

    @staticmethod
    def business_id(obj):
        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.receipt.transaction.pos.business),
            obj.receipt.transaction.pos.business.id,
        )

    @staticmethod
    def transaction_id(obj):
        return format_html(
            '<a href="{}">{}</a>', admin_link(obj.receipt.transaction), obj.receipt.transaction.id
        )

    @staticmethod
    def receipt_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.receipt), obj.receipt.id)

    @staticmethod
    def appointment_id(obj):
        if not obj.receipt.transaction.appointment:
            return ""

        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.receipt.transaction.appointment),
            obj.receipt.transaction.appointment.id,
        )

    @staticmethod
    def customer_id(obj):
        if not obj.receipt.transaction.customer:
            return ""

        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.receipt.transaction.customer),
            obj.receipt.transaction.customer.id,
        )

    @staticmethod
    def mass_payment_id(obj):
        payout = obj.payouts.filter().first()
        if not payout:
            return ""

        return format_html(
            '<a href="{}">{}</a>', admin_link(payout.mass_payment), payout.mass_payment
        )

    @staticmethod
    def stripe_payment_intent(obj):
        intent = obj.intents.first()
        if not intent:
            return ''

        return format_html('<a href="{}">{}</a>', admin_link(intent), intent.id)

    @staticmethod
    def adyen_obj(obj):
        search_models = [
            Auth,
            Capture,
            Refund,
        ]

        for model in search_models:
            adyen_obj = model.objects.filter(reference=obj.pnref).first()
            if adyen_obj:
                return format_html('<a href="{}">{}</a>', admin_link(adyen_obj), obj.pnref)

        return obj.pnref or ''

    @staticmethod
    def get_pr_button(obj):
        if obj is None:
            return ''
        if obj.settled:
            phrase = _('Mark as not settled')
        else:
            phrase = _('Mark as settled')
        return '<a href="{}" class="btn ">{}</a>'.format(
            reverse("admin:pr_switch_settled", args=(obj.id,)),
            phrase,
        )

    @staticmethod
    def pr_switch_settled(request, payment_row_id):
        pr = get_object_or_404(PaymentRow, id=payment_row_id)

        pr.settled = not pr.settled
        pr.save()

        if pr.settled:
            messages.success(request, 'PaymentRow marked as settled')

            LogEntry.objects.log_action(
                user_id=request.user.id,
                content_type_id=ContentType.objects.get_for_model(pr).pk,
                object_id=pr.id,
                object_repr=str(pr),
                action_flag=CHANGE,
                change_message='paymentrow_marked_as_settled',
            )

            PaymentRowChange.add(
                operator=get_user_from_django_request(request),
                reason=PaymentRowChange.SWITCH_SETTLED_ON,
                payment_row=pr,
            )

        else:
            messages.success(request, 'PaymentRow marked as not settled')

            LogEntry.objects.log_action(
                user_id=request.user.id,
                content_type_id=ContentType.objects.get_for_model(pr).pk,
                object_id=pr.id,
                object_repr=str(pr),
                action_flag=CHANGE,
                change_message='paymentrow_marked_as_not_settled',
            )

            PaymentRowChange.add(
                operator=get_user_from_django_request(request),
                reason=PaymentRowChange.SWITCH_SETTLED_OFF,
                payment_row=pr,
            )

        return redirect(request.META.get('HTTP_REFERER', '/'))

    def send_for_refund(self, obj):
        phrase = _('Send for refund')

        possible, _error = is_refund_possible(obj, from_admin=True)
        if possible and self.is_allowed:
            return format_html(
                '<a href="{}" class="btn ">{}</a>',
                reverse("admin:send_for_refund_view", args=(obj.id,)),
                phrase,
            )

        return ''

    @staticmethod
    @keep_dd_trace_for_django_admin(status_codes=[400, 404, 500], trace_percentage=100)
    def send_for_refund_view(request, payment_row_id):
        pr = get_object_or_404(PaymentRow, id=payment_row_id)
        user = get_user_from_django_request(request)

        pr.send_for_refund(user, from_admin=True)

        return redirect(
            reverse(
                'admin:pos_transaction_change',
                args=(pr.receipt.transaction.id,),
            )
        )


# Main PaymentRow Admin
class PaymentRowAdmin(NoRowsInListViewMixin, BasePaymentRowAdmin):
    paginator = SimplePaginator
    show_full_result_count = False


admin.site.register(PaymentRow, PaymentRowAdmin)


# Payment Row admin showing only row waiiting for Sent for refund
class RefundRowAdmin(BasePaymentRowAdmin):
    # pylint: disable=duplicate-code
    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .filter(
                refund_requested__isnull=False,
                children__isnull=True,
                settled=False,
            )
            .select_related('payment_type')
        )


admin.site.register(RefundRow, RefundRowAdmin)


class PaymentRowChangeAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    paginator = SimplePaginator
    show_full_result_count = False
    list_max_show_all = 20000
    list_per_page = 100
    list_display = [
        'id',
        'payment_row_id',
        'receipt_id',
        'transaction_id',
        'created',
        'reason',
        'operator',
    ]

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=payment_row__id',
    )

    query_fields = ('id', 'payment_row_id', 'payment_row__receipt__transaction__id')

    list_filter = ('reason',)

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related('operator')
            .prefetch_related('payment_row__receipt__transaction')
        )

    @staticmethod
    def payment_row_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.payment_row), obj.payment_row.id)

    @staticmethod
    def transaction_id(obj):
        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.payment_row.receipt.transaction),
            obj.payment_row.receipt.transaction.id,
        )

    @staticmethod
    def receipt_id(obj):
        return format_html(
            '<a href="{}">{}</a>', admin_link(obj.payment_row.receipt), obj.payment_row.receipt.id
        )


admin.site.register(PaymentRowChange, PaymentRowChangeAdmin)


class OperationFeeAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    list_max_show_all = 20000
    list_per_page = 100
    list_display = [
        'id',
        'created',
        'formatted_amount',
    ]

    readonly_fields = [
        'id',
        'created',
        'formatted_amount',
    ]


admin.site.register(OperationFee, OperationFeeAdmin)


class BsxSettingsAdmin(BaseModelAdmin):
    list_display = [
        'pos',
        'receipt_printing_enabled',
        'open_drawer_after_sale',
        'print_operator_name',
        'visible_for_biz',
    ]

    raw_id_fields = ['pos']


admin.site.register(BsxSettings, BsxSettingsAdmin)


class BsxLogAdmin(NoAddDelMixin, BaseModelAdmin):

    list_display = (
        'id',
        'created',
        'business_id',
        'transaction_id',
        'log',
    )

    search_fields = (
        '=id',
        '=business_id',
        '=transaction_id',
        'log',
    )

    fields = (
        'created',
        'business_id',
        'transaction_id',
        'log',
    )

    readonly_fields = (
        'created',
        'business_id',
        'transaction_id',
        'log',
    )


admin.site.register(BsxLog, BsxLogAdmin)


class SplashAdmin(BaseModelAdmin):
    search_fields = ['=business__id', '=operator__id']
    readonly_fields = ['business', 'operator']
    list_filter = ['type']

    hide_keyword_field = True


admin.site.register(Splash, SplashAdmin)


class BooksyGiftCardAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    list_display = (
        'id',
        'payment_id',
        'obj_external_id',
    )
    readonly_fields = [
        'obj_external_id',
    ]
    search_fields = [
        'id',
        'payment_id',
        'external_id',
        'payment__receipt__transaction__appointment_id',
    ]
    exclude = [
        'external_id',
    ]

    @admin.display(
        description="external_id",
        ordering="external_id",
    )
    def obj_external_id(self, obj: BooksyGiftCard):
        bgc_admin_url = (
            f"https://{settings.API_COUNTRY}.{settings.BOOKSY_DOMAIN}/booksy-gift-cards-api/"
            f"v1/admin/gift_card/giftcard/?"
            f"q=external_id+%3D+%22{obj.external_id}%22"
        )
        return format_html('<a href="{}">{}</a>', bgc_admin_url, obj.external_id)


admin.site.register(BooksyGiftCard, BooksyGiftCardAdmin)


class HigherPrepaymentSplashAdmin(BaseModelAdmin):
    search_fields = ['=business_id', '=operator_id']
    list_display = (
        'id',
        'business',
        'operator',
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('operator', 'business')


admin.site.register(HigherPrepaymentSplash, HigherPrepaymentSplashAdmin)


class HigherPrepaymentSplashDecisionAdmin(BaseModelAdmin):
    search_fields = ['=pos_id']
    list_display = (
        'id',
        'pos',
        'decision',
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('pos')


admin.site.register(HigherPrepaymentSplashDecision, HigherPrepaymentSplashDecisionAdmin)


class TippingAfterAppointmentAnswerAdmin(BaseModelAdmin):
    search_fields = ['client_id']
    list_display = (
        'id',
        'client',
        'answer',
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('client')


admin.site.register(TippingAfterAppointmentAnswer, TippingAfterAppointmentAnswerAdmin)
