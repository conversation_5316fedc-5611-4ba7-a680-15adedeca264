# pylint: disable=too-many-lines,too-many-statements
import unittest
from collections import OrderedDict
from datetime import datetime
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.test import override_settings
from mock import patch
from model_bakery import baker
from parameterized import parameterized

from country_config import Country
from lib.test_utils import create_subbooking
from lib.tools import tznow
from service.invoicing.tests.common import FCEnabledTestMixin
from service.pos.tests.common import POSTestsMixin
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment, BookingSources
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import Service, ServiceVariant, Resource, Business
from webapps.french_certification.models import FiscalReceipt, TreasuryOperation
from webapps.french_certification.tests.common import get_fiscal_receipt_with_basket_data
from webapps.french_certification.utils import get_basket_items
from webapps.french_certification.validators import validate_transaction_rows_for_prepayment
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.enums.compatibilities import NEW_CHECKOUT
from webapps.pos.models import (
    POS,
    PaymentType,
    TaxRate,
    Transaction,
    PaymentRow,
)
from webapps.pos.serializers import (
    TransactionRowDetailsSerializer,
    TransactionSerializer,
)
from webapps.user.models import User
from webapps.voucher.models import Voucher, VoucherTemplate
from webapps.warehouse.models import (
    Commodity,
    Warehouse,
)


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class BusinessTransactionsHandlerTestCase(FCEnabledTestMixin, POSTestsMixin, BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        self.default_tax_rate = baker.make(TaxRate, rate=10.00)
        fc_seller_recipe.make(business=self.business)

        self.pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.CREDIT_CARD)
        self.create_cash_payment_type(self.pos)

    def test_no_tax_rate_raise_error(self):
        baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )

        commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=baker.make(TaxRate, rate=None),
            description="",
        )

        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'products': [{'discount_rate': 0, 'product_id': commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)

        self.assertEqual(resp.code, 400)
        self.assertEqual(
            resp.json['errors'][0]['description'], f'Service tax is missing for {commodity.name}'
        )
        self.assertEqual(resp.json['errors'][1]['description'], str(commodity.id))
        self.assertEqual(resp.json['errors'][2]['description'], 'product')

    def test_voucher_cannot_be_sold_together_with_services(self):
        egift_template = baker.make(
            VoucherTemplate,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=VoucherTemplate.DAYS_30,
            pos=self.pos,
        )

        service = baker.make(Service, business=self.business, tax_rate=20)
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
        )
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws={
                'type': Appointment.TYPE.BUSINESS,
                'status': Appointment.STATUS.ACCEPTED,
                'service_variant': variant,
            },
        )

        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [{"booking_id": booking.id}],
            'tip_rate': 0,
            'discount_rate': 0,
            'vouchers': [
                {
                    'voucher_template_id': egift_template.id,
                    'valid_from': (tznow() + relativedelta(days=6)).strftime('%Y-%m-%d'),
                    'item_price': 123,
                    'voucher_customer': None,
                }
            ],
        }

        resp = self.fetch(self.url, method='POST', body=body)

        self.assertEqual(resp.code, 400)
        self.assertEqual(
            resp.json['errors'][0]['description'],
            'Gift Card cannot be sold together with service or product',
        )

    def test_voucher_cannot_be_sold_together_with_products(self):
        baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )

        commodity = baker.make(
            Commodity,
            business=self.business,
            name='Test product',
            total_pack_capacity=1,
            tax_rate=baker.make(TaxRate, rate=20),
            description="",
        )

        egift_template = baker.make(
            VoucherTemplate,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=VoucherTemplate.DAYS_30,
            pos=self.pos,
        )

        body = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': True,
            'customer_data': '',
            'transaction_type': 'P',
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 0,
            'vouchers': [
                {
                    'voucher_template_id': egift_template.id,
                    'valid_from': (tznow() + relativedelta(days=6)).strftime('%Y-%m-%d'),
                    'item_price': 123,
                    'voucher_customer': None,
                }
            ],
            'products': [{'discount_rate': 0, 'product_id': commodity.id, 'quantity': 1}],
        }

        resp = self.fetch(self.url, method='POST', body=body)

        self.assertEqual(resp.code, 400)
        self.assertEqual(
            resp.json['errors'][0]['description'],
            'Gift Card cannot be sold together with service or product',
        )

    @patch(
        'service.pos.business_transactions.BusinessTransactionsHandler.business_with_advanced_staffer'  # pylint: disable=line-too-long
    )
    def test_transaction_with_vouchers_creates_tresury_operation(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business

        egift_template = baker.make(
            VoucherTemplate,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=VoucherTemplate.DAYS_30,
            pos=self.pos,
        )

        transaction_data = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 10,
            'addons': [],
            'vouchers': [
                {
                    'voucher_template_id': egift_template.id,
                    'valid_from': (tznow() + relativedelta(days=6)).strftime('%Y-%m-%d'),
                    'item_price': 123,
                    'voucher_customer': None,
                }
            ],
        }

        with (
            patch(
                'webapps.french_certification.actions.txn_refactor_stage2_enabled',
                return_value=True,
            ),
            patch('webapps.pos.models.txn_refactor_stage2_enabled', return_value=True),
            patch('webapps.pos.services.txn_refactor_stage2_enabled', return_value=True),
        ):
            url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'

            resp = self.fetch(url, method='POST', body=transaction_data)

        self.assertEqual(resp.code, 201)
        self.assertEqual(TreasuryOperation.objects.count(), 1)
        self.assertEqual(FiscalReceipt.objects.count(), 0)


@pytest.mark.django_db
class TestBlockingTransactionWithEditableFlag(
    unittest.TestCase
):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        self.booking_source = baker.make(BookingSources)
        self.user = baker.make(User)
        self.access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
        self.business = baker.make(Business, owner=self.user)
        self.pos = baker.make(
            POS, business=self.business, active=True
        )
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        self.compatibilities = {NEW_CHECKOUT: True}
        self.service = baker.make(Service, business=self.business, tax_rate=10)
        self.service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            type=PriceType.FIXED,
            price=10,
        )
        self.tz = self.business.get_timezone()
        self.appt = create_appointment(
            [
                {
                    'service_variant': self.service_variant,
                    'booked_from': datetime(2023, 11, 17, 12, tzinfo=self.tz),
                    'booked_till': datetime(2023, 11, 17, 13, tzinfo=self.tz),
                },
                {
                    'service_variant': self.service_variant,
                    'booked_from': datetime(2023, 11, 17, 13, tzinfo=self.tz),
                    'booked_till': datetime(2023, 11, 17, 14, tzinfo=self.tz),
                },
            ],
            business=self.business,
        )
        fc_seller_recipe.make(business=self.business)
        self.data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'mode': PaymentRow.PAYMENT_ROW_MODE__COMPLETE,
                    'amount': 90,
                }
            ],
            'bookings': [
                {
                    'booking_id': self.appt.bookings.all()[0].id,
                    'item_price': 60,
                },
                {
                    'service_variant_id': self.service_variant.id,
                    'item_price': 30,
                },
            ],
        }
        self.serializer = TransactionSerializer(
            data=self.data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'business': self.business,
            },
        )
        self.assertTrue(self.serializer.is_valid(), self.serializer.errors)
        self.txn = self.serializer.save()
        get_fiscal_receipt_with_basket_data(
            business_id=self.business.id,
            is_prepayment=True,
            basket_id=self.txn.basket_id,
            basket_items=get_basket_items(self.txn.basket_id),
        )

    @parameterized.expand([c for c in Country if c not in Country.FR])
    def test_editable_flag_without_feature_flag(self, country: Country):
        with override_settings(API_COUNTRY=country):
            self.assertTrue(self.serializer.data['rows'][0]['editable'])
            self.assertTrue(self.serializer.data['rows'][1]['editable'])

    @french_certification_enabled(certification_enabled=True)
    def test_editable_flag_with_feature_flag(self):
        self.assertFalse(self.serializer.data['rows'][0]['editable'])
        self.assertTrue(self.serializer.data['rows'][1]['editable'])

    @parameterized.expand(['item_price', 'discount_rate'])
    @french_certification_enabled(certification_enabled=True)
    def test_editing_prepaid_booking_occurs_error(self, field):
        self.data['bookings'][0][field] = 50

        serializer = TransactionSerializer(
            data=self.data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'old_txn': self.txn,
                'edit': True,
                'business': self.business,
            },
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            str(serializer.errors['non_field_errors'][0]),
            'You cannot edit service paid by prepayment',
        )

    @french_certification_enabled(certification_enabled=True)
    def test_deleting_prepaid_booking_occurs_error(self):
        self.data['bookings'] = [
            {
                'service_variant_id': self.service_variant.id,
                'item_price': 30,
            },
        ]

        serializer = TransactionSerializer(
            data=self.data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'old_txn': self.txn,
                'edit': True,
                'business': self.business,
            },
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            str(serializer.errors['non_field_errors'][0]),
            'You cannot delete service paid by prepayment',
        )

    @french_certification_enabled(certification_enabled=True)
    def test_no_error_when_there_is_no_subbooking(self):
        business = baker.make(Business)
        pos = baker.make(POS, business=business)

        transaction_row = TransactionRowDetailsSerializer(
            OrderedDict(
                [
                    ('discount_rate', 0),
                    ('item_price', Decimal('10.00')),
                    ('commission_staffer_id', 8997),
                    ('type', 'T'),
                    ('tax_rate', Decimal('20.00')),
                    ('quantity', 1),
                    ('name_line_1', 'Opłata za dojazd'),
                    ('name_line_2', ''),
                    ('tax_type', 'I'),
                    ('discounted_item_price', Decimal('10.00')),
                    ('discounted_total', Decimal('10.00')),
                    ('tax_amount', Decimal('1.67')),
                    ('tax_included', Decimal('1.67')),
                    ('tax_excluded', Decimal('0')),
                    ('net_total', Decimal('8.33')),
                    ('gross_total', Decimal('10.00')),
                    ('net_total_wo_discount', Decimal('8.33')),
                    ('real_discount_amount', Decimal('0.00')),
                    ('total', Decimal('10.00')),
                    ('total_wo_discount', Decimal('10.00')),
                ]
            ),
            context={
                'pos': pos,
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
            },
        ).data

        self.assertTrue(transaction_row['editable'])

    @patch('webapps.french_certification.validators.get_prepayment_assigned_to_given_basket_id')
    @french_certification_enabled(certification_enabled=True)
    def test_validate_transaction_rows_for_prepayment(
        self, get_prepayment_assigned_to_given_basket_id_mock
    ):
        validate_transaction_rows_for_prepayment(
            rows=[
                OrderedDict(
                    [
                        ('id', 3),
                        ('booking_id', None),
                        ('product_id', None),
                        ('service_variant_id', None),
                        ('service_variant_series_id', None),
                        ('service_variant_combo_parent_id', None),
                        ('service_variant_label', None),
                        ('service_variant_combo_parent_name', ''),
                        ('service_color', None),
                        ('quantity', 1),
                        ('item_price', 10.0),
                        ('formatted_item_price', '$10.00'),
                        ('tax_amount', '$0.91'),
                        ('tax_rate', '10%'),
                        ('total', 10.0),
                        ('formatted_total', '$10.00'),
                        ('formatted_total_wo_discount', '$10.00'),
                        ('commission_staffer_id', 8997),
                        ('commission_amount', None),
                        ('commissions_last_edit', '2024-01-22T13:38'),
                        ('commissions_last_editor_name', ''),
                        ('discount_rate', 0),
                        ('name_line_2', ''),
                        ('voucher_id', None),
                        ('voucher_template_id', None),
                        ('valid_from', None),
                        ('voucher_customer', None),
                        ('warehouse_name', None),
                        ('addon', None),
                        ('row_hash_uuid', 'e6598c34099d496fa018361451153fa7'),
                        ('editable', True),
                        ('type', 'T'),
                        ('order', 2),
                        ('name_line_1', 'Travel Fee'),
                        ('discounted_item_price', '10.00'),
                        ('tax_type', 'I'),
                        ('tax_excluded', '0.00'),
                        ('tax_included', '0.91'),
                        ('discounted_total', '10.00'),
                        ('net_total_wo_discount', '9.09'),
                        ('real_discount_amount', '0.00'),
                        ('net_total', '9.09'),
                        ('gross_total', '10.00'),
                        ('basket_item_id', '5793230e-b79f-4a01-b80a-f07c15623b4c'),
                        ('addon_use', None),
                        ('warehouse', None),
                        ('service_name', 'Travel Fee'),
                    ]
                )
            ],
            txn=self.txn,
        )
