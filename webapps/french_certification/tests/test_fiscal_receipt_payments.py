from dataclasses import asdict
from decimal import Decimal
from unittest.mock import Mock, patch

from django.test import TestCase
from model_bakery import baker

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    RefundSplitsEntity,
    PaymentSplitsEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    PaymentMethodType,
)
from lib.point_of_sale.events import basket_payment_details_updated_event
from lib.tools import minor_unit
from service.tests import BaseAsyncHTTPTest
from webapps.business.models import Business
from webapps.french_certification.models import FiscalReceipt
from webapps.french_certification.tests.entities import TestBasketPaymentEntity
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.point_of_sale.models import Basket, BasketItem, BasketPayment
from webapps.point_of_sale.services.basket_payment import BasketPaymentService
from webapps.pos.baker_recipes import (
    pos_recipe,
    payment_type_recipe,
)
from webapps.pos.enums import receipt_status, PaymentTypeEnum
from webapps.pos.models import PaymentRow, Transaction, Receipt, PaymentType
from webapps.warehouse.models import Commodity, Warehouse


class FiscalReceiptPaymentsTestCase(TestCase, BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.business = baker.make(Business)
        self.pos = pos_recipe.make(business=self.business)
        fc_seller_recipe.make(business=self.business)
        baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=self.pos)
        self.payment_type = payment_type_recipe.make(pos=self.pos)

    @french_certification_enabled(certification_enabled=True)
    @patch(
        'webapps.french_certification.actions.LoyaltyCardService.get_loyalty_card_transaction_data',
        Mock(return_value={'total_stamps': 5}),
    )
    @patch('webapps.french_certification.actions.txn_refactor_stage2_enabled', return_value=True)
    def test_initialize_basket_payment_offline_created_fiscal_receipt(self, *_):
        basket = baker.make(Basket, business_id=self.business.id)
        transaction = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            basket_id=basket.id,
        )
        receipt = baker.make(
            Receipt,
            transaction=transaction,
            status_code=receipt_status.PENDING,
        )
        transaction.latest_receipt = receipt
        transaction.save()
        baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.PENDING,
            payment_type=self.payment_type,
        )
        basket_payment = BasketPaymentService.initialize_basket_payment(
            basket=basket,
            basket_payment_type=BasketPaymentType.PAYMENT,
            amount=minor_unit(123),
            payment_method_type=PaymentMethodType.CASH,
            payment_initialization=True,
            status=BasketPaymentStatus.SUCCESS,
            source=BasketPaymentSource.PAYMENT,
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            refund_splits=RefundSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            auto_capture=False,
        )
        self.assertNotEqual(basket_payment.id, None)
        self.assertEqual(FiscalReceipt.objects.count(), 1)

    @french_certification_enabled(certification_enabled=True)
    @patch(
        'webapps.french_certification.actions.LoyaltyCardService.get_loyalty_card_transaction_data',
        Mock(return_value={'total_stamps': 5}),
    )
    @patch('webapps.french_certification.actions.txn_refactor_stage2_enabled', return_value=True)
    @patch('webapps.pos.models.txn_refactor_stage2_enabled', return_value=True)
    def test_fiscal_receipt_created_after_basket_payment_update_old(self, *_):
        basket = baker.make(Basket, business_id=self.business.id)
        transaction = baker.make(
            Transaction,
            pos=self.pos,
            basket_id=basket.id,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            transaction=transaction,
            status_code=receipt_status.PENDING,
        )
        transaction.latest_receipt = receipt
        transaction.save()
        baker.make(BasketItem, basket=basket, gross_total=3500, net_total=3000, tax_amount=500)
        basket_payment = baker.make(
            BasketPayment,
            basket_id=basket.id,
            amount=3500,
            status=BasketPaymentStatus.PENDING,
            type=BasketPaymentType.PAYMENT,
            payment_provider_code=PaymentProviderCode.ADYEN,
        )
        baker.make(
            PaymentRow,
            receipt=receipt,
            basket_payment_id=basket_payment.id,
            status=receipt_status.PENDING,
            payment_type=self.payment_type,
        )

        returned_bp = BasketPaymentService.update_basket_payment_details(
            basket_payment=basket_payment,
            new_status=BasketPaymentStatus.SUCCESS,
        )

        self.assertEqual(basket_payment, returned_bp)
        basket_payment.refresh_from_db()

        self.assertEqual(basket_payment.status, BasketPaymentStatus.SUCCESS)

    @french_certification_enabled(certification_enabled=True)
    @patch(
        'webapps.french_certification.actions.LoyaltyCardService.get_loyalty_card_transaction_data',
        Mock(return_value={'total_stamps': 5}),
    )
    @patch('webapps.french_certification.actions.txn_refactor_stage2_enabled', return_value=True)
    @patch('webapps.pos.models.txn_refactor_stage2_enabled', return_value=True)
    def test_fiscal_receipt_created_after_basket_payment_updated(self, *_):
        basket = baker.make(Basket, business_id=self.business.id)

        transaction = baker.make(
            Transaction,
            pos=self.pos,
            basket_id=basket.id,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            transaction=transaction,
            status_code=receipt_status.PENDING,
        )
        transaction.latest_receipt = receipt
        transaction.save()
        baker.make(BasketItem, basket=basket, gross_total=3500, net_total=3000, tax_amount=500)
        basket_payment = TestBasketPaymentEntity(
            basket_id=basket.id,
            amount=3500,
            status=BasketPaymentStatus.SUCCESS,
            payment_provider_code=PaymentProviderCode.ADYEN,
        )

        with patch(
            'webapps.french_certification.actions.basket_fully_paid', Mock(return_value=True)
        ):
            basket_payment_details_updated_event.send(asdict(basket_payment))

        self.assertEqual(FiscalReceipt.objects.count(), 1)

    @patch(
        'service.pos.business_transactions.BusinessTransactionsHandler.business_with_advanced_staffer'  # pylint: disable=line-too-long
    )
    def test_create_transaction_creates_fiscal_receipt(self, business_with_staffer_mock):
        business_with_staffer_mock.return_value = self.business
        baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        product = baker.make(
            Commodity,
            business=self.business,
        )

        transaction_data = {
            'payment_type_code': PaymentTypeEnum.CASH,
            'dry_run': False,
            'customer_data': '',
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [],
            'tip_rate': 0,
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': product.id, 'quantity': 1}],
            'addons': [],
        }

        with (
            patch(
                'webapps.french_certification.actions.french_certification_enabled',
                return_value=True,
            ),
            patch(
                (
                    'webapps.french_certification.actions.'
                    'LoyaltyCardService.get_loyalty_card_transaction_data'
                ),
                Mock(return_value={'total_stamps': 5}),
            ),
            patch(
                'webapps.french_certification.actions.txn_refactor_stage2_enabled',
                return_value=True,
            ),
            patch('webapps.pos.models.txn_refactor_stage2_enabled', return_value=True),
            patch('webapps.pos.services.txn_refactor_stage2_enabled', return_value=True),
        ):
            url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'

            resp = self.fetch(url, method='POST', body=transaction_data)

        self.assertEqual(resp.code, 201)
        self.assertEqual(FiscalReceipt.objects.count(), 1)
