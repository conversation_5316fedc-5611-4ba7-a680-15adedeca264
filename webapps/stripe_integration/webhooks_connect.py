import copy
import datetime
import importlib
import traceback

from dacite import from_dict
from django.conf import settings

from country_config import Country
from lib.feature_flag.feature.payment import (
    ReorganizePaymentTypeTilesEnabled,
    RestrictedSoonConsentFlag,
)
from lib.feature_flag.feature.payment import SendConnectedAccountUpdatedEventFlag
from lib.payment_gateway.entities import WalletEntity
from lib.payment_providers.entities import AccountHolderSettingsData
from lib.payments.enums import PaymentProviderCode
from lib.segment_analytics.enums import EventType
from lib.serializers import safe_get
from webapps.business.models import Business
from webapps.business_consents.enums import ConsentCode, ConsentAction
from webapps.business_consents.ports import BusinessConsentsPort
from webapps.marketing.models import DelayedGTMEventAuthData
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort
from webapps.pos.enums import TapTo<PERSON><PERSON><PERSON>tatus
from webapps.pos.ports import set_tap_to_pay_status
from webapps.segment.tasks import (
    analytics_bcr_stripe_kyc_not_verified_account_gtm_task,
    analytics_bcr_stripe_kyc_not_verified_account_segment_task,
    analytics_bcr_stripe_kyc_pending_account_gtm_task,
    analytics_bcr_stripe_kyc_pending_account_segment_task,
    analytics_bcr_stripe_kyc_verified_account_gtm_task,
    analytics_bcr_stripe_kyc_verified_account_segment_task,
)
from webapps.stripe_integration.enums import (
    StripeAccountStatus,
)
from webapps.stripe_integration.enums import StripeWebhookEvents as event
from webapps.stripe_integration.events import (
    stripe_first_time_kyc_passed_event,
    stripe_kyc_failed_event,
    stripe_kyc_requires_update_event,
)
from webapps.stripe_integration.kafka_events.connected_account_updated import (
    ConnectedAccountUpdatedEvent,
    ConnectedAccountUpdatedEventKey,
)
from webapps.stripe_integration.models import (
    StripeAccount,
    StripeConnectNotification,
)
from webapps.stripe_integration.provider import StripeProvider


def _turn_on_stripe_if_consent_given(pos):
    if settings.API_COUNTRY == Country.PL:
        consents = (
            ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2,
            ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG_V2,
            ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS_V2,
        )
        for code in consents:
            consent = BusinessConsentsPort.get_consent(
                consent_code=code,
                business_id=pos.business_id,
                decision=ConsentAction.AGREE,
            )
            if consent:
                pos._force_stripe_pba = True  # pylint: disable=protected-access
                pos.save(update_fields=['_force_stripe_pba'])
                break


def account_updated_handler(
    notification: StripeConnectNotification,
):  # pylint: disable=too-many-branches, disable=too-many-statements
    stripe_account = notification.account
    if not stripe_account:
        return False
    pos = stripe_account.pos

    data = notification.data
    requirements = data['data']['object']['requirements']
    verification_errors = requirements['errors']
    is_pending_verification = requirements['currently_due'] or requirements['pending_verification']
    charges_enabled = data['data']['object']['charges_enabled']
    payouts_enabled = data['data']['object']['payouts_enabled']
    tos_acceptance_date_timestamp = data['data']['object']['tos_acceptance']['date']
    tos_acceptance_date = (
        datetime.datetime.fromtimestamp(
            tos_acceptance_date_timestamp,
            tz=pos.business.get_timezone(),
        )
        if tos_acceptance_date_timestamp
        else None
    )
    previous_status = stripe_account.status

    stripe_account.charges_enabled = charges_enabled
    stripe_account.payouts_enabled = payouts_enabled
    stripe_account.tos_acceptance_date = tos_acceptance_date

    is_account_verified = charges_enabled
    if is_account_verified:  # TODO sprawdzic faktyczna zmiane na dirty_fields
        stripe_account.status = StripeAccountStatus.VERIFIED
        if not stripe_account.kyc_verified_at_least_once:
            stripe_account.kyc_verified_at_least_once = True
            stripe_first_time_kyc_passed_event.send(stripe_account)
    elif verification_errors:
        stripe_account.status = StripeAccountStatus.NOT_VERIFIED
        if not stripe_account.kyc_verified_at_least_once:
            stripe_kyc_failed_event.send(stripe_account)
        else:
            stripe_kyc_requires_update_event.send(stripe_account)

    elif is_pending_verification:
        stripe_account.status = StripeAccountStatus.VERIFICATION_PENDING

    elif previous_status and previous_status != StripeAccountStatus.TURNED_OFF:
        stripe_account.status = StripeAccountStatus.NOT_VERIFIED

    else:
        stripe_account.status = StripeAccountStatus.TURNED_OFF

    stripe_account.save()

    if stripe_account.status == StripeAccountStatus.VERIFIED:
        # if Px agreed and is verified in Stripe - switch payments processing to Stripe
        _turn_on_stripe_if_consent_given(pos)
        if pos.force_stripe_pba:
            pos.enable_pay_by_app()
        if settings.POS__BLIK:
            pos.enable_blik()

        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=pos.business_id,
        )
        account_holder_settings_entity: AccountHolderSettingsData = (
            PaymentProvidersAccountHolderPort.get_account_holder_settings(
                account_holder_id=wallet_entity.account_holder_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
            ).entity
        )
        if ReorganizePaymentTypeTilesEnabled():
            bcr_enabled = bool(
                safe_get(
                    account_holder_settings_entity,
                    [PaymentProviderCode.STRIPE, 'bcr_fees_accepted'],
                )
            )
            if bcr_enabled:
                pos.enable_stripe_terminal_payments()
        else:
            pos.enable_stripe_terminal_payments()
        if settings.POS__TAP_TO_PAY:
            ttp_enabled = bool(
                safe_get(
                    account_holder_settings_entity,
                    [PaymentProviderCode.STRIPE, 'tap_to_pay_fees_accepted'],
                )
            )
            if ttp_enabled:
                set_tap_to_pay_status(pos.business_id, TapToPayStatus.ENABLED)

        StripeProvider.get_or_create_location(stripe_account)
        StripeProvider.set_tip_settings(stripe_account)
    else:
        if pos.force_stripe_pba:
            pos.disable_pay_by_app_due_to_kyc_errors()
        if settings.POS__BLIK:
            pos.disable_blik()
        if settings.POS__KEYED_IN_PAYMENT:
            pos.disable_keyed_in_payment()

        pos.disable_ba_deposit_payments()
        pos.disable_stripe_terminal_payments()
        set_tap_to_pay_status(pos.business_id, TapToPayStatus.DISABLED)

    if RestrictedSoonConsentFlag():
        is_restricted_soon = bool(requirements['current_deadline'])
        if is_restricted_soon:
            BusinessConsentsPort.get_or_create_consent(
                consent_code=ConsentCode.STRIPE_ACCOUNT_RESTRICTED_SOON,
                business_id=pos.business_id,
            )
        else:
            # if it's not restricted soon, check if there exists a consent, and if so, disable it
            # (as it means the merchant filled kyc correctly and shouldn't be reminded anymore)
            consent = BusinessConsentsPort.get_consent(
                consent_code=ConsentCode.STRIPE_ACCOUNT_RESTRICTED_SOON,
                business_id=pos.business_id,
            )
            if consent and consent.decision != ConsentAction.AGREE:
                BusinessConsentsPort.set_consent_decision(
                    consent_code=ConsentCode.STRIPE_ACCOUNT_RESTRICTED_SOON,
                    business_id=pos.business_id,
                    action=ConsentAction.AGREE,
                )

    _queue_martech_analytic_task_if_necessary(
        stripe_account,
        previous_status,
    )

    if SendConnectedAccountUpdatedEventFlag():
        publish_connected_account_updated_event(notification)

    return True


def _queue_martech_analytic_task_if_necessary(
    stripe_account: StripeAccount,
    previous_status: StripeAccountStatus,
):
    if stripe_account.status == previous_status:
        return

    match stripe_account.status:
        case StripeAccountStatus.VERIFICATION_PENDING:
            _queue_task_if_auth_data_exist(
                business=stripe_account.pos.business,
                event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_PENDING_ACCOUNT,
                gtm_task=analytics_bcr_stripe_kyc_pending_account_gtm_task,
                segment_task=analytics_bcr_stripe_kyc_pending_account_segment_task,
            )
        case StripeAccountStatus.VERIFIED:
            _queue_task_if_auth_data_exist(
                business=stripe_account.pos.business,
                event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_VERIFIED_ACCOUNT,
                gtm_task=analytics_bcr_stripe_kyc_verified_account_gtm_task,
                segment_task=analytics_bcr_stripe_kyc_verified_account_segment_task,
            )
        case StripeAccountStatus.NOT_VERIFIED:
            _queue_task_if_auth_data_exist(
                business=stripe_account.pos.business,
                event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT,
                gtm_task=analytics_bcr_stripe_kyc_not_verified_account_gtm_task,
                segment_task=analytics_bcr_stripe_kyc_not_verified_account_segment_task,
            )


def _queue_task_if_auth_data_exist(
    event_type: DelayedGTMEventAuthData.EventType,
    business: Business,
    gtm_task,
    segment_task=None,
):
    event_auth_data = DelayedGTMEventAuthData.objects.filter(
        business=business,
        event_type=event_type,
        event_sent=False,
    ).first()
    if segment_task:
        segment_task.delay(
            business_id=business.id,
            context={
                'source_id': getattr(event_auth_data, 'booking_source_id', None),
                'event_type': EventType.BUSINESS,
                'business_id': business.id,
            },
        )

    if not event_auth_data:
        return

    gtm_task.delay(
        business_id=business.id,
        auth_data=event_auth_data.auth_data,
        context={
            'source_id': event_auth_data.booking_source_id,
            'event_type': EventType.BUSINESS,
            'business_id': business.id,
        },
    )
    event_auth_data.mark_as_sent()


STRIPE_WEBHOOK_EVENTS_HANDLERS = {
    event.ACCOUNT_UPDATED: account_updated_handler,
}


# pylint:disable=duplicate-code
def handle_connect_stripe_event(
    notification: StripeConnectNotification,
):
    # TODO for now synchronization will be done in post_save
    #  because connect events needs to be rewritten in next stage
    # NotificationServices.handle_notification(payment_providers_notification)
    handler = STRIPE_WEBHOOK_EVENTS_HANDLERS.get(notification.type)
    handled_successfully = False
    if handler:
        try:
            handled_successfully = handler(notification)
        except Exception as e:
            notification.handled_successfully = False
            notification.handling_errors = traceback.format_exc()
            notification.save()
            raise e
    notification.handled_successfully = handled_successfully
    notification.save()


# pylint:enable=duplicate-code


def publish_connected_account_updated_event(notification: StripeConnectNotification):
    booksy_event_publishers = _import_booksy_event_publishers()

    business_id = notification.account.pos.business_id
    event_data = copy.deepcopy(notification.data['data']['object'])
    current_deadline = event_data['requirements']['current_deadline']
    event_data['requirements']['current_deadline'] = (
        datetime.datetime.fromtimestamp(current_deadline) if current_deadline else None
    )
    event_data['capabilities'] = dict(event_data['capabilities'])

    message = from_dict(
        data_class=ConnectedAccountUpdatedEvent,
        data=event_data
        | {
            'business_id': business_id,
            'created': datetime.datetime.fromtimestamp(notification.data['created']),
            'payment_provider_code': PaymentProviderCode.STRIPE,
        },
    )

    key = ConnectedAccountUpdatedEventKey(business_id=business_id)
    booksy_event_publishers.connected_account_updated_event_publisher.produce(
        key=key, message=message
    )


def _import_booksy_event_publishers():
    from lib.kafka_events import booksy_event_publishers

    importlib.reload(booksy_event_publishers)
    return booksy_event_publishers
