from datetime import datetime, timedelta

import mock
import pytest
from django.conf import settings
from django.test import override_settings
from freezegun import freeze_time
from mock.mock import PropertyMock
from model_bakery import baker
from pytz import UTC
from rest_framework import status
from segment.analytics import Client

from country_config import Country
from lib.locks import StripeAccountCreationLock
from lib.payment_providers.enums import StripeAccountType
from lib.tools import id_to_external_api
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.models import BookingSources
from webapps.business.models import Business
from webapps.consts import FRONTDESK
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.marketing.models import DelayedGTMEventAuthData
from webapps.pos.models import POS
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import BooksyAppVersions, DeviceTypeName, AnalyticEventEnums
from webapps.stripe_integration.enums import (
    StripeAccountStatus,
    StripeExternalAccountType,
    FastPayoutStatus,
    StripeAccountOnboardingStatus,
    StripePayoutMethodType,
    StripePayoutStatus,
)
from webapps.stripe_integration.models import StripeAccount, StripePayout
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_account_create,
    mock_stripe_account_delete,
    mock_stripe_account_link_create,
    mock_stripe_account_retrieve,
)


def _martech_extra_headers():
    frontdesk_source, _ = BookingSources.objects.get_or_create(
        app_type=BookingSources.BUSINESS_APP,
        name=FRONTDESK,
        defaults={
            'api_key': 'frontdesk-test-api-key',
        },
    )

    return {'X-User-Pseudo-ID': 'TEST.1234', 'X-API-KEY': frontdesk_source.api_key}


@pytest.mark.django_db
class StripeAccountHandlerTests(BaseAsyncHTTPTest):
    url = "/business_api/me/businesses/{business_id}/stripe/account/"

    def setUp(self):
        super().setUp()

        self.pos = baker.make(
            POS,
            business=self.business,
            fast_payouts_admin_blocked=False,
            fast_payouts_visible=True,
        )
        self.stripe_account = baker.make(
            StripeAccount,
            pos=self.pos,
            status=StripeAccountStatus.TURNED_OFF,
            charges_enabled=False,
            payouts_enabled=True,
        )
        self.url = self.url.format(business_id=self.business.id)

    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get(self, stripe_account_retrieve_mock):
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['status'] == self.stripe_account.status
        assert resp.json['charges_enabled'] == self.stripe_account.charges_enabled
        assert resp.json['payouts_enabled'] == self.stripe_account.payouts_enabled
        assert resp.json['location'] is None

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_hidden_payouts_visible_false(self, account_retrieve_mock):
        self.pos.fast_payouts_visible = False
        self.pos.save()
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.HIDDEN

    @override_settings(POS__FAST_PAYOUTS=False)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_hidden_settings_fast_payouts_off(self, account_retrieve_mock):
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.HIDDEN

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_blocked(self, account_retrieve_mock):
        self.pos.fast_payouts_admin_blocked = True
        self.pos.save()
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.BLOCKED

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_missing_kyc(self, account_retrieve_mock):
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.MISSING_KYC

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_complete_first_transaction(self, account_retrieve_mock):
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.bcr_onboarding_status',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = StripeAccountOnboardingStatus.TERMINAL_SHIPPED
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.COMPLETE_FIRST_TRANSACTION

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_merchant_disabled(self, account_retrieve_mock):
        self.pos.fast_payouts_merchant_enabled = False
        self.pos.save()
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.has_successful_transaction',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = True
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.MERCHANT_DISABLED

    @override_settings(POS__FAST_PAYOUTS=True)
    @override_settings(API_COUNTRY=Country.US)
    @mock_stripe_account_retrieve(StripeExternalAccountType.BANK_ACCOUNT, None)
    def test_get_status_wrong_bank_account(self, account_retrieve_mock):
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.has_successful_transaction',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = True
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.WRONG_BANK_ACCOUNT

    @override_settings(POS__FAST_PAYOUTS=True)
    @override_settings(API_COUNTRY=Country.US)
    @mock_stripe_account_retrieve(None)
    def test_get_status_missing_payout_method(self, _account_retrieve_mock):
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.has_successful_transaction',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = True
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.MISSING_PAYOUT_METHOD

    @override_settings(POS__FAST_PAYOUTS=True)
    @override_settings(API_COUNTRY=Country.US)
    @freeze_time(datetime(2021, 2, 15, 9, 0, 0, tzinfo=UTC))
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_available_tomorrow(self, account_retrieve_mock):
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        business_timezone = self.business.get_timezone()
        business_midnight = datetime(2021, 2, 15, tzinfo=business_timezone)

        baker.make(
            StripePayout,
            account=self.stripe_account,
            method=StripePayoutMethodType.INSTANT,
            payout_created=business_midnight.astimezone(UTC) + timedelta(hours=1),
            status=StripePayoutStatus.PAID,
        )

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.has_successful_transaction',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = True
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.AVAILABLE_TOMORROW

    @override_settings(POS__FAST_PAYOUTS=True)
    @override_settings(API_COUNTRY=Country.US)
    @freeze_time(datetime(2021, 2, 15, 9, 0, 0, tzinfo=UTC))
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_available_today_payout_before_midnight(self, account_retrieve_mock):
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        business_timezone = self.business.get_timezone()
        business_midnight = datetime(2021, 2, 15, tzinfo=business_timezone)

        baker.make(
            StripePayout,
            account=self.stripe_account,
            method=StripePayoutMethodType.INSTANT,
            payout_created=business_midnight.astimezone(UTC) - timedelta(hours=1),
        )

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.has_successful_transaction',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = True
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.AVAILABLE

    @override_settings(POS__FAST_PAYOUTS=True)
    @override_settings(API_COUNTRY=Country.US)
    @freeze_time(datetime(2021, 2, 15, tzinfo=UTC))
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_status_available(self, account_retrieve_mock):
        self.stripe_account.status = StripeAccountStatus.VERIFIED
        self.stripe_account.save()

        with mock.patch(
            'webapps.stripe_integration.models.StripeAccount.has_successful_transaction',
            new_callable=PropertyMock,
        ) as mck:
            mck.return_value = True
            resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.AVAILABLE

    @override_settings(POS__FAST_PAYOUTS=True)
    def test_get_without_stripe_account_and_payouts_visible(self):
        self.stripe_account.delete()
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['status'] == StripeAccountStatus.TURNED_OFF
        assert resp.json['blocked'] is False
        assert resp.json['charges_enabled'] is False
        assert resp.json['payouts_enabled'] is False
        assert resp.json['location'] is None
        assert resp.json['onboarding_status'] is None
        assert resp.json['bcr_onboarding_status'] is None
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.MISSING_KYC

    def test_get_without_stripe_account_and_payouts_visible_fast_payouts_global_off(self):
        self.stripe_account.delete()
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['status'] == StripeAccountStatus.TURNED_OFF
        assert resp.json['blocked'] is False
        assert resp.json['charges_enabled'] is False
        assert resp.json['payouts_enabled'] is False
        assert resp.json['location'] is None
        assert resp.json['onboarding_status'] is None
        assert resp.json['bcr_onboarding_status'] is None
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.HIDDEN

    @override_settings(POS__FAST_PAYOUTS=True)
    def test_get_without_stripe_account_and_payouts_not_visible(self):
        self.stripe_account.delete()
        self.pos.fast_payouts_visible = False
        self.pos.save()
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['status'] == StripeAccountStatus.TURNED_OFF
        assert resp.json['blocked'] is False
        assert resp.json['charges_enabled'] is False
        assert resp.json['payouts_enabled'] is False
        assert resp.json['location'] is None
        assert resp.json['onboarding_status'] is None
        assert resp.json['bcr_onboarding_status'] is None
        assert resp.json['fast_payouts_status'] == FastPayoutStatus.HIDDEN

    @mock.patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    @mock_stripe_account_delete
    def test_delete(self, _delete_mock):
        with (
            mock.patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock,
            mock.patch.object(Client, 'track') as analytics_track_mock,
            mock.patch.object(Client, 'identify') as analytics_identify_mock,
        ):
            assert not self.stripe_account.deleted

            extra_headers = _martech_extra_headers()
            resp = self.fetch(
                self.url,
                method='DELETE',
                extra_headers=extra_headers,
            )

            assert resp.code == status.HTTP_200_OK
            self.stripe_account.refresh_from_db()
            assert self.stripe_account.deleted

            user_id = id_to_external_api(self.business.owner.id)
            dict_assert(
                request_api_mock.call_args_list[0][1],
                {
                    'endpoint': '/p/collect',
                    'method': 'post',
                    'payload': {
                        'firebase_auth': {
                            'client_id': extra_headers['X-User-Pseudo-ID'],
                        },
                        'user_id': user_id,
                        'events': [
                            {
                                'params': {
                                    'email': self.business.owner.email,
                                },
                                'name': AnalyticEventEnums.BCR_RESET_ACCOUNT_VERIFY,
                            }
                        ],
                        'user_properties': {
                            'offer_type': {'value': Business.Package.UNKNOWN.label},
                            'app_version': {'value': BooksyAppVersions.B30},
                            'device_type': {'value': DeviceTypeName.DESKTOP},
                            'user_role': {'value': UserRoleEnum.OWNER},
                            'country': {'value': settings.API_COUNTRY},
                        },
                    },
                },
            )

            common_fields = {
                'country': settings.API_COUNTRY,
                'user_id': id_to_external_api(self.business.owner.id),
                'user_role': UserRoleEnum.OWNER,
                'email': self.business.owner.email,
                'phone': '',
                'offer_type': Business.Package.UNKNOWN.label,
                'business_id': id_to_external_api(self.business.id),
                'app_version': BooksyAppVersions.B30,
                'device_type': DeviceTypeName.DESKTOP,
                'business_phone': self.business.phone or '',
            }
            dict_assert(
                analytics_track_mock.call_args_list[0][1],
                {
                    'user_id': id_to_external_api(self.business.owner.id),
                    'anonymous_id': None,
                    'event': AnalyticEventEnums.BCR_RESET_ACCOUNT_VERIFY,
                    'properties': {
                        **common_fields,
                    },
                },
            )
            dict_assert(
                analytics_identify_mock.call_args_list[0][1],
                {
                    'user_id': id_to_external_api(self.business.owner.id),
                    'anonymous_id': None,
                    'traits': {
                        **common_fields,
                        'event_name': (
                            f'python.analytics_'
                            f'{AnalyticEventEnums.BCR_RESET_ACCOUNT_VERIFY.lower()}'
                            f'_segment_task'
                        ),
                    },
                },
            )


@mock_stripe_account_create
@mock_stripe_account_link_create
@pytest.mark.django_db
class StripeAccountLinkHandlerTests(BaseAsyncHTTPTest):
    url = "/business_api/me/businesses/{business_id}/stripe/account_link/"

    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.pos = baker.make(
            POS,
            business=self.business,
            stripe_terminal_enabled=True,
        )
        self.url = self.url.format(business_id=self.business.id)
        self.body = {
            "refresh_url": "https://example.com/refresh_url",
            "return_url": "https://example.com/return_url",
        }

    def test_post(self, account_link_mock, account_mock):
        extra_headers = _martech_extra_headers()
        resp = self.fetch(
            self.url,
            method='POST',
            body=self.body,
            extra_headers=extra_headers,
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json['url']
        assert StripeAccount.objects.filter(
            pos=self.pos,
            charges_enabled=False,
            payouts_enabled=False,
            account_type=StripeAccountType.CUSTOM,
        ).exists()
        assert StripeAccount.objects.count() == 1
        assert account_mock.call_count == 1
        assert account_link_mock.call_count == 1
        stripe_account = StripeAccount.objects.last()
        account_link_first_time_created = stripe_account.account_link_first_time_created
        assert bool(account_link_first_time_created)

        # make sure that consecutive calls wont create another account, just a new link to Stripe
        resp = self.fetch(
            self.url,
            method='POST',
            body=self.body,
            extra_headers=extra_headers,
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json['url']
        assert StripeAccount.objects.filter(
            pos=self.pos,
            charges_enabled=False,
            payouts_enabled=False,
            account_type=StripeAccountType.CUSTOM,
        ).exists()
        assert StripeAccount.objects.count() == 1
        assert account_mock.call_count == 1
        assert account_link_mock.call_count == 2
        # check account_link_first_time_created does not change after another link call
        stripe_account.refresh_from_db()
        assert account_link_first_time_created == stripe_account.account_link_first_time_created

        assert (
            DelayedGTMEventAuthData.objects.filter(
                business=self.business,
                event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_PENDING_ACCOUNT,
                client_id=extra_headers['X-User-Pseudo-ID'],
                event_sent=False,
            ).count()
            == 1
        )
        assert (
            DelayedGTMEventAuthData.objects.filter(
                business=self.business,
                event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_VERIFIED_ACCOUNT,
                client_id=extra_headers['X-User-Pseudo-ID'],
                event_sent=False,
            ).count()
            == 1
        )

    @mock.patch('lib.locks.StripeAccountCreationLock.lock')
    @mock.patch('lib.locks.StripeAccountCreationLock.try_to_unlock')
    def test_post_for_race_conditions__lock_is_being_used(
        self,
        unlock_mock,
        lock_mock,
        account_link_mock,
        account_mock,
    ):
        resp = self.fetch(self.url, method='POST', body=self.body)
        assert resp.code == status.HTTP_200_OK
        lock_mock.assert_called_once_with(self.pos.id)
        unlock_mock.assert_called_once()

    def test_post_for_race_conditions__lock_works(self, account_link_mock, account_mock):
        StripeAccountCreationLock.lock(self.pos.id)
        resp = self.fetch(self.url, method='POST', body=self.body)
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_post_as_trial(self, account_link_mock, account_mock):
        self.business.status = Business.Status.TRIAL
        self.business.save()
        extra_headers = _martech_extra_headers()
        resp = self.fetch(
            self.url,
            method='POST',
            body=self.body,
            extra_headers=extra_headers,
        )
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors'][0] == {
            'code': 'invalid',
            'description': 'Invalid subscription status',
        }
        assert not StripeAccount.objects.filter(
            pos__business_id=self.business.id,
        ).exists()

    @french_certification_enabled(certification_enabled=True)
    def test_business_without_seller_data_returns_400(
        self,
        _,
        __,
    ):
        resp = self.fetch(self.url, method='POST', body=self.body)
        assert resp.code == status.HTTP_400_BAD_REQUEST

    @french_certification_enabled(certification_enabled=True)
    def test_business_with_seller_data_returns_200(
        self,
        _,
        __,
    ):
        fc_seller_recipe.make(business=self.business)
        resp = self.fetch(self.url, method='POST', body=self.body)
        assert resp.code == status.HTTP_200_OK
