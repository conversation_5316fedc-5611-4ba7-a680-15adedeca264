import datetime
import json

import mock
import pytz
from django.conf import settings
from django.test import override_settings
from django.urls import reverse
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status
from rest_framework.test import APITestCase
from segment.analytics import Client

from country_config.enums import Country
from lib.feature_flag.feature.data_streaming import EnablePublishingEventsToKafka
from lib.feature_flag.feature.payment import (
    ReorganizePaymentTypeTilesEnabled,
    RestrictedSoonConsentFlag,
)
from lib.feature_flag.feature.payment import SendConnectedAccountUpdatedEventFlag
from lib.payments.enums import PaymentProviderCode
from lib.tests.utils import override_eppo_feature_flag
from lib.tests.utils import override_feature_flag
from lib.tools import id_to_external_api
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.business.models import Business
from webapps.business_consents.enums import ConsentCode, ConsentAction
from webapps.consts import IPHONE
from webapps.experiment_v3.exp.email_kyc_verified import EmailKYCVerifiedExperiment
from webapps.marketing.models import DelayedGTMEventAuthData
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import StripeAccountHolderSettings
from webapps.pos.enums import PaymentTypeEnum, TapToPayStatus
from webapps.pos.models import POS, PaymentType
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import AnalyticEventEnums, BooksyAppVersions, DeviceTypeName
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.stripe_integration.kafka_events.connected_account_updated import (
    ConnectedAccountUpdatedEvent,
    ConnectedAccountUpdatedEventKey,
    Requirements,
)
from webapps.stripe_integration.models import (
    StripeAccount,
    StripeConnectNotification,
    StripeLocation,
)
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_location_create,
    mock_stripe_webhook_construct_event,
)
from webapps.user.models import User


class BaseStripeConnectNotificationViewTestCase(APITestCase):
    @property
    def url(self):
        return reverse("stripe_integration_webhook_connect")

    def setUp(self, session_access_level=None):
        super().setUp()
        self.user = baker.make(User, email='<EMAIL>')
        self.business = baker.make(Business, phone='********', owner=self.user)
        self.pos = baker.make(POS, business=self.business)

    def get_account_data(
        self,
        external_id,
        account,
        hook_type,
        charges_enabled,
        payouts_enabled,
        tos_acceptance_date_timestamp=**********,
        pending_verification=None,
        errors=None,
    ):
        return {
            "id": external_id,
            "object": "event",
            "account": account,
            "api_version": "2020-08-27",
            "created": **********,
            "data": {
                "object": {
                    "id": account,
                    "object": "account",
                    "business_profile": {
                        "mcc": "7230",
                        "name": None,
                        "support_address": None,
                        "support_email": None,
                        "support_phone": None,
                        "support_url": None,
                        "url": "booksy.com",
                    },
                    "capabilities": {"card_payments": "active", "transfers": "active"},
                    "charges_enabled": charges_enabled,
                    "country": "US",
                    "default_currency": "usd",
                    "details_submitted": True,
                    "email": None,
                    "payouts_enabled": payouts_enabled,
                    "settings": {
                        "bacs_debit_payments": {},
                        "branding": {
                            "icon": None,
                            "logo": None,
                            "primary_color": None,
                            "secondary_color": None,
                        },
                        "card_issuing": {"tos_acceptance": {"date": None, "ip": None}},
                        "card_payments": {
                            "statement_descriptor_prefix": None,
                            "decline_on": {"avs_failure": False, "cvc_failure": False},
                        },
                        "dashboard": {"display_name": "Booksy", "timezone": "Etc/UTC"},
                        "payments": {
                            "statement_descriptor": "BOOKSY.COM",
                            "statement_descriptor_kana": None,
                            "statement_descriptor_kanji": None,
                        },
                        "sepa_debit_payments": {},
                        "payouts": {
                            "debit_negative_balances": True,
                            "schedule": {"delay_days": 2, "interval": "daily"},
                            "statement_descriptor": None,
                        },
                    },
                    "type": "express",
                    "created": **********,
                    "external_accounts": {
                        "object": "list",
                        "data": [
                            {
                                "id": "ba_1JFgUGQWmflvDigsH9dNvDer",
                                "object": "bank_account",
                                "account": account,
                                "account_holder_name": None,
                                "account_holder_type": None,
                                "available_payout_methods": ["standard"],
                                "bank_name": "STRIPE TEST BANK",
                                "country": "US",
                                "currency": "usd",
                                "default_for_currency": True,
                                "fingerprint": "bgPzvnzO5ebm2FY3",
                                "last4": "6789",
                                "metadata": {},
                                "routing_number": "*********",
                                "status": "new",
                            }
                        ],
                        "has_more": False,
                        "total_count": 1,
                        "url": f"/v1/accounts/{account}/external_accounts",
                    },
                    "login_links": {
                        "object": "list",
                        "total_count": 0,
                        "has_more": False,
                        "url": f"/v1/accounts/{account}/login_links",
                        "data": [],
                    },
                    "metadata": {},
                    "requirements": {
                        "current_deadline": None,
                        "currently_due": [],
                        "disabled_reason": None,
                        "errors": errors or [],
                        "eventually_due": [],
                        "past_due": [],
                        "pending_verification": pending_verification or [],
                    },
                    "tos_acceptance": {"date": tos_acceptance_date_timestamp},
                },
                "previous_attributes": {
                    "capabilities": {"card_payments": "inactive", "transfers": "inactive"},
                    "charges_enabled": False,
                    "details_submitted": False,
                    "payouts_enabled": False,
                    "requirements": {
                        "current_deadline": **********,
                        "currently_due": ["tos_acceptance.date", "tos_acceptance.ip"],
                        "disabled_reason": "requirements.past_due",
                        "eventually_due": ["tos_acceptance.date", "tos_acceptance.ip"],
                        "past_due": ["tos_acceptance.date", "tos_acceptance.ip"],
                    },
                    "tos_acceptance": {"date": None},
                },
            },
            "livemode": False,
            "pending_webhooks": 1,
            "request": {"id": "req_F09ZeMP0vHKj2Y", "idempotency_key": None},
            "type": hook_type,
        }


@mock.patch('webapps.stripe_integration.provider.StripeProvider.set_tip_settings')
@mock_stripe_location_create
@mock_stripe_webhook_construct_event
class StripeConnectNotificationViewTestCase(BaseStripeConnectNotificationViewTestCase):
    @property
    def url(self):
        return reverse("stripe_integration_webhook_connect")

    def setUp(self, session_access_level=None):
        super().setUp()
        self.stripe_account = baker.make(
            StripeAccount,
            external_id="acct_666",
            pos=self.pos,
            status=StripeAccountStatus.TURNED_OFF,
            charges_enabled=False,
            payouts_enabled=False,
        )
        EmailKYCVerifiedExperiment.initialize()
        self.business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )

    def _create_martech_auth_data_obj(
        self,
        event_type,
    ):
        app_instance_id = '123placek123'
        client_id = 'place_z_jagodami'
        booking_source = baker.make(BookingSources, name=IPHONE)
        return DelayedGTMEventAuthData.objects.create(
            business=self.business,
            booking_source=booking_source,
            event_type=event_type,
            app_instance_id=app_instance_id,
            client_id=client_id,
        )

    def _assert_martech(
        self,
        request_api_mock,
        event,
        event_name,
        analytics_track_mock,
        analytics_identify_mock,
        bcr_status,
    ):
        dict_assert(
            request_api_mock.call_args_list[0][1],
            {
                'endpoint': '/p/collect',
                'method': 'post',
                'payload': {
                    'firebase_auth': {
                        'client_id': event.client_id,
                    },
                    'user_id': id_to_external_api(self.business.owner.id),
                    'events': [
                        {
                            'params': {
                                'stripe_kyc_status': bcr_status,
                                'email': self.business.owner.email,
                            },
                            'name': event_name,
                        }
                    ],
                    'user_properties': {
                        'offer_type': {'value': Business.Package.UNKNOWN.label},
                        'device_type': {'value': DeviceTypeName.IOS},
                        'user_role': {'value': UserRoleEnum.OWNER},
                        'business_phone': {'value': self.business.phone or ''},
                        'country': {'value': settings.API_COUNTRY},
                    },
                },
            },
        )
        common_fields = {
            'country': settings.API_COUNTRY,
            'user_id': id_to_external_api(self.business.owner.id),
            'user_role': UserRoleEnum.OWNER,
            'email': self.business.owner.email,
            'phone': '',
            'offer_type': Business.Package.UNKNOWN.label,
            'business_id': id_to_external_api(self.business.id),
            'app_version': BooksyAppVersions.B30,
            'device_type': DeviceTypeName.IOS,
            'stripe_kyc_status': bcr_status,
            'business_phone': self.business.phone or '',
        }
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'anonymous_id': None,
                'event': event_name,
                'properties': {
                    **common_fields,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'anonymous_id': None,
                'traits': {
                    **common_fields,
                    'event_name': f'python.analytics_{event_name.lower()}_segment_task',
                },
            },
        )

        event.refresh_from_db()
        assert event.event_sent

    @mock.patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    def test_kyc_pending_account_martech_event(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        pending_data_auth_data = self._create_martech_auth_data_obj(
            event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_PENDING_ACCOUNT,
        )
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=False,
            payouts_enabled=False,
            pending_verification=True,
        )

        with (
            mock.patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock,
            mock.patch.object(Client, 'track') as analytics_track_mock,
            mock.patch.object(Client, 'identify') as analytics_identify_mock,
        ):
            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK

            self._assert_martech(
                request_api_mock=request_api_mock,
                event=pending_data_auth_data,
                analytics_track_mock=analytics_track_mock,
                analytics_identify_mock=analytics_identify_mock,
                event_name=AnalyticEventEnums.BCR_STRIPE_KYC_PENDING_ACCOUNT.value,
                bcr_status=StripeAccountStatus.VERIFICATION_PENDING.value,
            )

    @mock.patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    def test_kyc_not_verified_account_martech_event(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        not_verified_auth_data = self._create_martech_auth_data_obj(
            event_type=DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT,
        )
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=False,
            payouts_enabled=False,
            errors=True,
        )

        with (
            mock.patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock,
            mock.patch.object(Client, 'track') as analytics_track_mock,
            mock.patch.object(Client, 'identify') as analytics_identify_mock,
        ):
            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK

            self._assert_martech(
                request_api_mock=request_api_mock,
                event=not_verified_auth_data,
                event_name=AnalyticEventEnums.BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT.value,
                bcr_status=StripeAccountStatus.NOT_VERIFIED,
                analytics_track_mock=analytics_track_mock,
                analytics_identify_mock=analytics_identify_mock,
            )

    @mock.patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    def test_kyc_verified_account_martech_event(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        verified_auth_data = self._create_martech_auth_data_obj(
            DelayedGTMEventAuthData.EventType.BCR_STRIPE_KYC_VERIFIED_ACCOUNT
        )
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=True,
        )

        with (
            mock.patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock,
            mock.patch.object(Client, 'track') as analytics_track_mock,
            mock.patch.object(Client, 'identify') as analytics_identify_mock,
        ):
            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK

            self._assert_martech(
                request_api_mock=request_api_mock,
                analytics_track_mock=analytics_track_mock,
                analytics_identify_mock=analytics_identify_mock,
                event=verified_auth_data,
                event_name=AnalyticEventEnums.BCR_STRIPE_KYC_VERIFIED_ACCOUNT,
                bcr_status=StripeAccountStatus.VERIFIED,
            )

    @override_feature_flag({RestrictedSoonConsentFlag.flag_name: True})
    @mock.patch(
        'webapps.stripe_integration.webhooks_connect.BusinessConsentsPort.get_or_create_consent'
    )
    def test_restricted_soon_consent(
        self,
        _consent_mock,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=False,
            payouts_enabled=False,
            pending_verification=True,
        )
        data['data']['object']['requirements']['current_deadline'] = 666

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert _consent_mock.call_count == 1

    @override_feature_flag({RestrictedSoonConsentFlag.flag_name: False})
    @mock.patch(
        'webapps.stripe_integration.webhooks_connect.BusinessConsentsPort.get_or_create_consent'
    )
    def test_restricted_soon_consent__turned_off(
        self,
        _consent_mock,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=False,
            payouts_enabled=False,
            pending_verification=True,
        )
        data['data']['object']['requirements']['current_deadline'] = 666

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert _consent_mock.call_count == 0

    def test_pba_disable_feature_flags(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=True,
            tos_acceptance_date_timestamp=**********,
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        self.pos.refresh_from_db()
        self.assertEqual(self.pos.pay_by_app_status, POS.PAY_BY_APP_DISABLED)

    def test_pba_enabled(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    )
        self.pos.stripe_terminal_enabled = False
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()
        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=False,
            tos_acceptance_date_timestamp=**********,
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        self.pos.refresh_from_db()
        self.assertEqual(self.pos.pay_by_app_status, POS.PAY_BY_APP_ENABLED)
        self.assertEqual(
            self.pos.payment_types.filter(
                code=PaymentTypeEnum.STRIPE_TERMINAL,
                default=True,
            ).exists(),
            False,
        )
        self.assertEqual(StripeLocation.objects.exists(), True)

        self.pos.stripe_terminal_enabled = True
        self.pos.save(update_fields=['stripe_terminal_enabled'])
        self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        self.assertEqual(
            self.pos.payment_types.filter(
                code=PaymentTypeEnum.STRIPE_TERMINAL,
                default=True,
            ).exists(),
            True,
        )
        self.assertFalse(
            self.pos.payment_types.filter(
                code=PaymentTypeEnum.TAP_TO_PAY,
            ).exists(),
        )

    def test_stripe_terminal_enabled_when_bcr_fees_accepted(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    )
        self.pos.stripe_terminal_enabled = True
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        with override_feature_flag({ReorganizePaymentTypeTilesEnabled.flag_name: True}):
            # no BCR fees at first
            data = self.get_account_data(
                external_id="evt_1111",
                account="acct_666",
                hook_type="account.updated",
                charges_enabled=True,
                payouts_enabled=True,
                tos_acceptance_date_timestamp=**********,
            )

            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK
            self.pos.refresh_from_db()
            self.assertEqual(
                self.pos.payment_types.filter(
                    code=PaymentTypeEnum.STRIPE_TERMINAL,
                ).exists(),
                False,
            )
            self.assertEqual(StripeLocation.objects.exists(), True)

            # with BCR fees
            stripe_account_holder_settings = StripeAccountHolderSettings.objects.filter(
                account_holder_id=self.business_wallet.account_holder_id
            ).first()  # pylint: disable=line-too-long

            stripe_account_holder_settings.bcr_fees_accepted = True
            stripe_account_holder_settings.save()
            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK
            self.pos.refresh_from_db()
            self.assertEqual(
                self.pos.payment_types.filter(
                    code=PaymentTypeEnum.STRIPE_TERMINAL,
                ).exists(),
                True,
            )
            self.assertEqual(StripeLocation.objects.exists(), True)

    def test_pba_enabled_kyc_migrated(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    )
        self.pos.stripe_terminal_enabled = False
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=True,
            tos_acceptance_date_timestamp=**********,
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        self.pos.refresh_from_db()
        self.assertEqual(self.pos.pay_by_app_status, POS.PAY_BY_APP_ENABLED)
        self.assertEqual(
            self.pos.payment_types.filter(
                code=PaymentTypeEnum.STRIPE_TERMINAL,
            ).exists(),
            False,
        )

        self.pos.stripe_terminal_enabled = True
        self.pos.save(update_fields=['stripe_terminal_enabled'])
        self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        self.assertEqual(
            self.pos.payment_types.filter(
                code=PaymentTypeEnum.STRIPE_TERMINAL,
                default=True,
            ).exists(),
            True,
        )

    def test_pba_disabled(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    )
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.status = POS.PAY_BY_APP_ENABLED
        self.pos.save()

        data = self.get_account_data(
            external_id="evt_2222",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=False,
            payouts_enabled=False,
            tos_acceptance_date_timestamp=None,
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        self.pos.refresh_from_db()
        self.assertEqual(self.pos.pay_by_app_status, POS.PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS)

    @parameterized.expand([(True,), (False,)])
    @override_settings(POS__TAP_TO_PAY=True)
    def test_set_tap_to_pay_status_enabled(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
        account_verified,
    ):
        stripe_account_holder_settings = StripeAccountHolderSettings.objects.filter(
            account_holder_id=self.business_wallet.account_holder_id
        ).first()
        stripe_account_holder_settings.tap_to_pay_fees_accepted = True
        stripe_account_holder_settings.save()

        self.assertEqual(self.pos.tap_to_pay_status, TapToPayStatus.DISABLED)
        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=account_verified,
            payouts_enabled=account_verified,
            tos_acceptance_date_timestamp=**********,
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK

        self.pos.refresh_from_db()
        if account_verified:
            self.assertEqual(self.pos.tap_to_pay_status, TapToPayStatus.ENABLED)
        else:
            self.assertEqual(self.pos.tap_to_pay_status, TapToPayStatus.DISABLED)

    @parameterized.expand([True, False])
    def test_enable_blik(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
        settings_value,
    ):
        with override_settings(POS__BLIK=settings_value):
            data = self.get_account_data(
                external_id="evt_1111",
                account="acct_666",
                hook_type="account.updated",
                charges_enabled=True,
                payouts_enabled=False,
                tos_acceptance_date_timestamp=**********,
            )

            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK
            self.assertEqual(
                self.pos.payment_types.filter(
                    code=PaymentTypeEnum.BLIK,
                    enabled=True,
                ).exists(),
                settings_value,
            )

    def test_disable_blik(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        with override_settings(POS__BLIK=True):
            baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.BLIK)
            data = self.get_account_data(
                external_id="evt_1111",
                account="acct_666",
                hook_type="account.updated",
                charges_enabled=False,
                payouts_enabled=False,
                tos_acceptance_date_timestamp=**********,
            )

            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK
            self.assertEqual(
                self.pos.payment_types.filter(
                    code=PaymentTypeEnum.BLIK,
                ).exists(),
                False,
            )
            self.assertEqual(
                PaymentType.all_objects.filter(code=PaymentTypeEnum.BLIK, pos=self.pos).exists(),
                True,
            )

    def test_disable_keyed_in_payment(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        with override_settings(POS__KEYED_IN_PAYMENT=True):
            baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.KEYED_IN_PAYMENT)
            self.pos.stripe_terminal_enabled = False
            self.pos._force_stripe_pba = True  # pylint: disable=protected-access
            self.pos.save()
            data = self.get_account_data(
                external_id="evt_1111",
                account="acct_666",
                hook_type="account.updated",
                charges_enabled=False,
                payouts_enabled=False,
                tos_acceptance_date_timestamp=**********,
            )
            resp = self.client.post(
                self.url,
                data=json.dumps(data),
                content_type="application/json",
                **{"HTTP_stripe-signature": "CORRECT"},
            )
            assert resp.status_code == status.HTTP_200_OK
            self.pos.refresh_from_db()
            self.assertEqual(
                self.pos.payment_types.filter(
                    code=PaymentTypeEnum.KEYED_IN_PAYMENT,
                ).exists(),
                False,
            )
            self.assertEqual(
                PaymentType.all_objects.filter(
                    code=PaymentTypeEnum.KEYED_IN_PAYMENT, pos=self.pos
                ).exists(),
                True,
            )

    @override_settings(API_COUNTRY=Country.PL)
    def test_turn_on_stripe_based_on_consent(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        self.assertFalse(self.pos.pos_refactor_stage2_enabled)
        self.assertFalse(self.pos._force_stripe_pba)  # pylint: disable=protected-access
        baker.make(
            'BusinessConsent',
            business_id=self.business.id,
            consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG_V2,
            visible=True,
            decision=ConsentAction.AGREE,
        )

        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=False,
            tos_acceptance_date_timestamp=**********,
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.pos.refresh_from_db()
        self.assertTrue(self.pos.pos_refactor_stage2_enabled)
        self.assertTrue(self.pos._force_stripe_pba)  # pylint: disable=protected-access

    @mock.patch('webapps.payments.actions.KYCSuccessNotification.send')
    def _test_account_updated(self, notification_mock):
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=True,
            tos_acceptance_date_timestamp=123,
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "incorrect123"},
        )
        assert resp.status_code == status.HTTP_403_FORBIDDEN

        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=True,
            tos_acceptance_date_timestamp=**********,
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        notification = StripeConnectNotification.objects.filter(
            external_id="evt_1111", account__external_id="acct_666"
        ).last()
        assert notification is not None
        assert notification.handled_successfully is True
        stripe_acc = StripeAccount.objects.first()
        assert stripe_acc.charges_enabled is True
        assert stripe_acc.payouts_enabled is True
        assert stripe_acc.status == StripeAccountStatus.VERIFIED
        assert notification_mock.call_count == 1
        assert stripe_acc.tos_acceptance_date == datetime.datetime(
            2021, 12, 29, 8, 46, 11, tzinfo=pytz.utc
        )

        data = self.get_account_data(
            external_id="evt_2222",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=False,
            tos_acceptance_date_timestamp=None,
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        notification = StripeConnectNotification.objects.filter(
            external_id="evt_2222", account__external_id="acct_666"
        ).last()
        assert notification is not None
        assert notification.handled_successfully is True
        stripe_acc = StripeAccount.objects.first()
        assert stripe_acc.charges_enabled is True
        assert stripe_acc.payouts_enabled is False
        assert stripe_acc.status == StripeAccountStatus.NOT_VERIFIED
        assert stripe_acc.tos_acceptance_date is None

    @override_eppo_feature_flag(
        {
            SendConnectedAccountUpdatedEventFlag.flag_name: True,
            EnablePublishingEventsToKafka.flag_name: True,
        }
    )
    @mock.patch('webapps.stripe_integration.webhooks_connect._import_booksy_event_publishers')
    def test_publish_connected_account_updated_event(
        self,
        mocked_import_booksy_event_publishers,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        mock_produce = mock.Mock()
        mock_event_publisher = mock.Mock()
        mock_event_publisher.connected_account_updated_event_publisher.produce = mock_produce
        mocked_import_booksy_event_publishers.return_value = mock_event_publisher

        data = self.get_account_data(
            external_id="evt_1111",
            account="acct_666",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=False,
            tos_acceptance_date_timestamp=**********,
        )
        data['data']['object']['requirements'] = {
            "alternatives": [],
            "current_deadline": **********,
            "currently_due": [
                "business_type",
                "external_account",
            ],
            "disabled_reason": "requirements.past_due",
            "errors": [],
            "eventually_due": [],
            "past_due": [
                "tos_acceptance.date",
                "tos_acceptance.ip",
            ],
            "pending_verification": [],
        }
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK

        requirements = Requirements(
            currently_due=[
                'business_type',
                'external_account',
            ],
            past_due=[
                'tos_acceptance.date',
                'tos_acceptance.ip',
            ],
            eventually_due=[],
            current_deadline=datetime.datetime(2021, 7, 21, 14, 33, 53),
            disabled_reason='requirements.past_due',
        )
        key = ConnectedAccountUpdatedEventKey(business_id=self.business.id)
        message = ConnectedAccountUpdatedEvent(
            business_id=self.business.id,
            payment_provider_code=PaymentProviderCode.STRIPE,
            created=datetime.datetime(2021, 7, 21, 14, 33, 53),
            payouts_enabled=False,
            charges_enabled=True,
            requirements=requirements,
            capabilities={'card_payments': 'active', 'transfers': 'active'},
        )
        mock_produce.assert_called_with(key=key, message=message)


@mock.patch('webapps.stripe_integration.provider.StripeProvider.set_tip_settings')
@mock_stripe_location_create
@mock_stripe_webhook_construct_event
class StripeConnectNotificationViewNoAccountTestCase(BaseStripeConnectNotificationViewTestCase):
    def test_kyc_verified_account_no_account_in_db_with_fake_import_log(
        self,
        _set_tips_mock,
        _connection_token_mock,
        _location_mock,
    ):
        data = self.get_account_data(
            external_id="evt_0000",
            account="acct_777",
            hook_type="account.updated",
            charges_enabled=True,
            payouts_enabled=True,
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )

        assert resp.status_code == status.HTTP_200_OK
