import datetime
from unittest.mock import patch, MagicMock

import mock
import pytest
from dateutil.relativedelta import relativedelta
from django.db.models import Max
from django.shortcuts import reverse
from django.test import TestCase as DjangoTestCase
from django.test.utils import override_settings
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from pytest_django import asserts
from rest_framework import status
from rest_framework.test import APITestCase

from country_config import Country
from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from drf_api.service.tests.base import (
    AuthenticatedBusinessAPITestCase,
)
from drf_api.service.tests.base import AuthenticatedCustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.voucher import (
    VoucherOnlineSaleEnabledFlag,
)
from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.tests.utils import override_feature_flag
from service.tests import dict_assert
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import (
    business_recipe,
    resource_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.business.models import Business, Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.consts import WEB
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.pos.enums import (
    CARD_TYPE__APPLE_PAY,
    CARD_TYPE__GOOGLE_PAY,
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
)
from webapps.pos.enums.receipt_status import PAYMENT_SUCCESS, CALL_FOR_PAYMENT
from webapps.pos.models import (
    PaymentMethod,
    POS,
    VoucherTemplate,
    PaymentType,
    POSPlan,
    TransactionRow,
    Transaction,
    Receipt,
    PaymentRow,
)
from webapps.pos.provider.fake import _CARDS
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.voucher.enums import (
    FrenchVoucherTemplateValidTill,
    LegacyVoucherTemplateValidTill,
    OnlineVoucherTemplateValidTill,
    VoucherStatus,
    VoucherType,
)
from webapps.voucher.gift_card_content.gift_card_default_backgrounds import DEFAULT_BACKGROUND_URL
from webapps.voucher.models import (
    get_default_background_gc_image_id,
    Voucher,
    VoucherAdditionalInfo,
    VoucherOrder,
    VoucherServiceVariant,
    VoucherTemplateServiceVariant,
)
from webapps.voucher.utils import VoucherMigrationConfig


@pytest.mark.usefixtures('default_voucher_background')
class VoucherCheckoutViewTestCase(AuthenticatedCustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.headers.update(
            HTTP_X_FINGERPRINT="kokoko123",
            HTTP_USER_AGENT="test user agent",
        )
        cls.business = business_recipe.make(owner=cls.user)
        cls.url = reverse(
            'voucher_checkout',
            args=(cls.business.id,),
        )
        cls.pos = baker.make(
            POS,
            business=cls.business,
        )
        cls.user.email = '<EMAIL>'
        cls.user.cell_phone = '+***********'
        cls.user.save()

        cls.plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        cls.pos.recalculate_pos_plans()

        baker.make(PaymentType, pos=cls.pos, code=PaymentTypeEnum.PAY_BY_APP)

        cls.bci = baker.make(
            BusinessCustomerInfo,
            user=cls.user,
            business=cls.business,
        )

        cls.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            cls.business.id,
            statement_name=cls.business.name,
        )[0]
        cls.customer_wallet = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=cls.user.id,
            email=cls.user.email,
            phone=cls.user.cell_phone,
            statement_name='',
        )[0]

    def setUp(self):
        super().setUp()
        self.voucher_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            online_purchase=True,
        )
        self.card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        self.card.id = 1
        self.card.save()

    def _get_data(self):
        return {
            "voucher_template_id": self.voucher_template.id,
            "payment_method": 1,
        }

    @freeze_time(datetime.datetime(2023, 6, 10, 12, 0, 1))
    def test_voucher_checkout_200_payment_method(
        self,
    ):
        response = self.client.post(
            self.url,
            data=self._get_data(),
            format="json",
            **self.headers,
        )

        voucher_order: VoucherOrder = VoucherOrder.objects.get()
        assert voucher_order.customer.id == self.bci.id
        assert voucher_order.friends_phone == ''
        assert voucher_order.friends_name is None
        assert voucher_order.voucher_template.id == self.voucher_template.id

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data,
            {
                'three_d_data': None,
                "voucher_code": voucher_order.voucher.code,
                'transaction_id': voucher_order.voucher.transaction_rows.first().transaction.id,
            },
        )

        voucher: Voucher = voucher_order.voucher
        assert voucher.voucher_template.id == voucher_order.voucher_template.id
        assert voucher.pos.id == self.pos.id
        assert voucher.status == Voucher.ACTIVE
        assert voucher.waitting_for_recipient is False
        assert voucher.purchased_online is True

        assert voucher.voucher_changes.first().status == VoucherStatus.DRAFT
        assert voucher.voucher_changes.last().status == VoucherStatus.ACTIVE

        transaction_row: TransactionRow = TransactionRow.objects.get()
        assert transaction_row.voucher_id == voucher.id
        assert transaction_row.subbooking_id is None
        assert transaction_row.type == TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER

        txn: Transaction = transaction_row.transaction
        assert txn.transaction_type == Transaction.TRANSACTION_TYPE__PAYMENT

        receipts: (Receipt, Receipt) = txn.receipts.order_by('-id')
        payment_success_res, call_4_payment_res = receipts
        assert payment_success_res.status_code == PAYMENT_SUCCESS
        assert payment_success_res.already_paid

        assert call_4_payment_res.status_code == CALL_FOR_PAYMENT
        assert not call_4_payment_res.already_paid

        payment_row: PaymentRow = txn.payment_rows.get()
        assert payment_row.receipt.id == payment_success_res.id
        assert payment_row.payment_type.code == PaymentTypeEnum.PAY_BY_APP
        assert payment_row.status == PAYMENT_SUCCESS
        assert payment_row.basket_payment_id

    @parameterized.expand(
        [
            (VoucherType.EGIFT_CARD, True),
            (VoucherType.MEMBERSHIP, False),
            (VoucherType.PACKAGE, False),
        ]
    )
    @freeze_time(datetime.datetime(2023, 6, 10, 12, 0, 1))
    @patch('webapps.voucher.models.Voucher.send_to_customer')
    @patch('webapps.voucher.notifications.GiftCardSoldOnlineProviderNotification.send')
    def test_voucher_checkout_purchase_notification_sent(
        self,
        voucher_type,
        notification_sent,
        mocked_send_notification,
        send_to_customer_mock,
    ):
        self.voucher_template.type = voucher_type
        self.voucher_template.save()
        with DjangoTestCase.captureOnCommitCallbacks(execute=True):
            response = self.client.post(
                self.url,
                data=self._get_data(),
                format="json",
                **self.headers,
            )
        assert response.status_code == status.HTTP_200_OK
        voucher = Voucher.objects.get()
        assert voucher.status == Voucher.ACTIVE
        assert mocked_send_notification.called is notification_sent

    @freeze_time(datetime.datetime(2023, 6, 10, 12, 0, 1))
    def test_voucher_checkout_400_deleted_template(
        self,
    ):
        self.voucher_template.soft_delete()
        response = self.client.post(
            self.url,
            data=self._get_data(),
            format="json",
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        dict_assert(
            response.json()['errors'][0],
            {
                'field': 'voucher_template_id',
                'description': f'Invalid pk "{self.voucher_template.id}" - object does not exist.',
                'code': 'does_not_exist',
            },
        )
        assert not VoucherOrder.objects.exists()

    @freeze_time(datetime.datetime(2023, 6, 10, 12, 0, 1))
    def test_voucher_checkout_low_value(
        self,
    ):
        VoucherTemplate.objects.update(item_price=0.49)

        response = self.client.post(
            self.url,
            data=self._get_data(),
            format="json",
            **self.headers,
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {'payment_rows': ['Amount must be greater or equal to $0.50']}

    @mock.patch('webapps.pos.serializers.check_tokenized_payments_v2')
    @mock.patch('webapps.pos.provider.proxy.ProxyProvider.make_payment')
    def test_voucher_checkout_200_external_payment_method(
        self,
        fake_provider_make_payment,
        tokenized_payments_mock,
    ):
        tokenized_payments_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: True,
            CARD_TYPE__APPLE_PAY: True,
        }

        data = {
            "voucher_template_id": self.voucher_template.id,
            'external_payment_method': {
                'partner': CARD_TYPE__GOOGLE_PAY,
                'token': '123123123',
            },
        }

        response = self.client.post(
            self.url,
            data=data,
            format="json",
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        tokenized_payments_mock.assert_called_with(
            appointment_data=None,
            is_cancelation=False,
            stripe_enabled=False,
            allow_blik=False,
        )

        fake_provider_make_payment.assert_called_once()
        payment_method = fake_provider_make_payment.call_args.kwargs['payment_method']
        assert payment_method.card_type == CARD_TYPE__GOOGLE_PAY
        assert payment_method.token == "123123123"

    @mock.patch('webapps.pos.provider.fake.FakePaymentProvider.make_payment')
    def test_passed_device_data_n_trigger_to_payment_provider(
        self,
        fake_provider_make_payment,
    ):
        data = {
            "voucher_template_id": self.voucher_template.id,
            "payment_method": self.card.id,
        }

        self.client.post(
            self.url,
            data=data,
            format="json",
            **self.headers,
        )

        device_data = fake_provider_make_payment.call_args.kwargs['device_data']
        trigger = fake_provider_make_payment.call_args.kwargs['trigger']
        assert trigger == BasketPaymentAnalyticsTrigger.CUSTOMER__VOUCHER_ONLINE_PURCHASE
        assert device_data == DeviceDataDict(
            phone_number='+48 697 850 000', fingerprint='kokoko123', user_agent='test user agent'
        )

    @mock.patch('webapps.voucher.views.PaymentTransactionPartSerializer.get_three_d_data')
    def test_3ds_is_returned_if_exists(self, get_3d_data):
        three_d_secure_data = {
            "issuerUrl": "https://test.adyen.com/hpp/3d/validate.shtml",
            "md": "aXEwVTY5Uy85NWgwRmhUa3A2am5iQT09IVUva8SOxojY5qQlQnlQ",
            "paRequest": "eNpVUu1ygjAQfBXGByAhgFDmzAyWdvSHSP14ABpukCqgA",
            "pspReference": "7914643409856901",
            "resultCode": "RedirectShopper",
            "authCode": "65496",
        }

        get_3d_data.return_value = three_d_secure_data

        data = {
            "voucher_template_id": self.voucher_template.id,
            "payment_method": self.card.id,
        }

        response = self.client.post(
            self.url,
            data=data,
            format="json",
            **self.headers,
        )

        assert response.json()['three_d_data'] == three_d_secure_data

    @mock.patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_voucher_checkout_400_payment_not_possible(
        self,
        tokenized_payments_mock,
    ):
        tokenized_payments_mock.return_value = {
            CARD_TYPE__GOOGLE_PAY: False,
            CARD_TYPE__APPLE_PAY: True,
        }
        data = {
            'external_payment_method': {'partner': CARD_TYPE__GOOGLE_PAY, 'token': '123123123'},
            "voucher_template_id": self.voucher_template.id,
        }

        response = self.client.post(
            self.url,
            data=data,
            format="json",
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'external_payment_method.non_field_errors',
                        'description': 'Payment is not possible',
                        'code': 'payment_not_possible',
                    }
                ]
            },
        )
        tokenized_payments_mock.assert_called_with(
            appointment_data=None,
            is_cancelation=False,
            stripe_enabled=False,
            allow_blik=False,
        )

    @parameterized.expand(
        (
            (
                {
                    "payment_method": 1,
                    'external_payment_method': {
                        'partner': CARD_TYPE__GOOGLE_PAY,
                        'token': '123123123',
                    },
                },
            ),
            ({},),
        )
    )
    @mock.patch('webapps.pos.serializers.check_tokenized_payments_v2')
    def test_voucher_checkout_400_missing_method(
        self,
        payment_data,
        tokenized_payments_mock,
    ):
        data = {
            **payment_data,
            "voucher_template_id": self.voucher_template.id,
        }

        response = self.client.post(
            self.url,
            data=data,
            format="json",
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'non_field_errors',
                        'description': 'Payment_method'
                        ' xor external_payment_method should be provided',
                        'code': 'missing_method',
                    }
                ]
            },
        )
        if payment_data.keys():
            tokenized_payments_mock.assert_called_with(
                appointment_data=None,
                is_cancelation=False,
                stripe_enabled=False,
                allow_blik=False,
            )
        else:
            tokenized_payments_mock.assert_not_called()

    @mock.patch("webapps.voucher.views.VoucherOrderCreateSerializer")
    def test_voucher_checkout_create_voucher_order_serializer_call(
        self,
        voucher_order_serializer_mock,
    ):
        serializer_obj_mock = mock.Mock()
        serializer_obj_mock.save.return_value = baker.make(
            VoucherOrder, voucher_template=self.voucher_template, customer=self.bci
        )
        voucher_order_serializer_mock.return_value = serializer_obj_mock
        data = {
            "payment_method": 1,
            "voucher_template_id": self.voucher_template.id,
        }

        self.client.post(
            self.url,
            data=data,
            format="json",
            **self.headers,
        )

        voucher_order_serializer_mock.assert_called_with(
            data={
                "voucher_template": data["voucher_template_id"],
                "customer": self.bci.id,
            },
            context={
                "business": self.business,
                "pos": self.pos,
            },
        )
        serializer_obj_mock.is_valid.assert_called_once()


@pytest.mark.usefixtures('default_voucher_background')
class VoucherBackgroundListViewTestCase(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.business = business_recipe.make(owner=self.user)
        self.url = reverse(
            'voucher_business_images',
            args=(self.business.id,),
        )

    def test_200(self):
        biz_image = baker.make(
            Image,
            category=ImageTypeEnum.VOUCHER_GIFTCARD,
            image_url='http://kokoko_image.jpg',
            width=688,
            height=433,
            business=self.business,
            tags=[],
            staffers=[],
        )

        existing_images = (
            Image.objects.filter(category=ImageTypeEnum.VOUCHER_GIFTCARD)
            .values_list('business', flat=True)
            .order_by('business')
        )
        assert list(existing_images) == [self.business.id, None]

        # Another business
        another_business = business_recipe.make()
        baker.make(
            Image,
            category=ImageTypeEnum.VOUCHER_GIFTCARD,
            image_url='http://another_kokoko_image.jpg',
            width=688,
            height=433,
            business=another_business,
            tags=[],
            staffers=[],
        )

        response = self.client.get(
            self.url,
            **self.headers,
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            'voucher_background_images': [
                {
                    'id': get_default_background_gc_image_id(),
                    'background_image_url': DEFAULT_BACKGROUND_URL,
                },
                {'id': biz_image.id, 'background_image_url': biz_image.full_url},
            ]
        }

        # Test default
        response = self.client.get(
            self.url,
            data={'default': True},
            **self.headers,
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            'voucher_background_images': [
                {
                    'id': get_default_background_gc_image_id(),
                    'background_image_url': DEFAULT_BACKGROUND_URL,
                },
            ]
        }

        # Test default False
        response = self.client.get(
            self.url,
            data={'default': False},
            **self.headers,
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            'voucher_background_images': [
                {
                    'id': get_default_background_gc_image_id(),
                    'background_image_url': DEFAULT_BACKGROUND_URL,
                },
                {'id': biz_image.id, 'background_image_url': biz_image.full_url},
            ]
        }

        response = self.client.get(
            self.url + "?default=null",
            **self.headers,
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            'voucher_background_images': [
                {
                    'id': get_default_background_gc_image_id(),
                    'background_image_url': DEFAULT_BACKGROUND_URL,
                },
                {'id': biz_image.id, 'background_image_url': biz_image.full_url},
            ]
        }

    def test_default_invalid_value(self):
        for default in ['kokoko', 123123]:
            response = self.client.get(
                self.url,
                data={'default': default},
                **self.headers,
            )

            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == {'default': ['Must be a valid boolean.']}

    def test_no_access_token(self):
        response = self.client.get(
            self.url,
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_404(self):
        response = self.client.get(
            reverse(
                'voucher_business_images',
                args=(self.business.id << 2,),
            ),
            **self.headers,
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_no_enough_permissions(self):
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        self.resource.save()
        self.business.owner = user_recipe.make()
        self.business.save()
        response = self.client.get(
            self.url,
            **self.headers,
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND


@patch('webapps.qr_code_origami.storage.QRCodeFileStorage.client', new=MagicMock())
class BusinessGiftCardPDFDownloadTestCase(BaseBusinessApiTestCase):
    endpoint_name = 'business_voucher_detail_download'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.user = user_recipe.make()
        cls.business = business_recipe.make(owner=cls.user)
        cls.pos = baker.make(POS, business=cls.business)
        cls.voucher_template = baker.make(
            VoucherTemplate,
            pos=cls.pos,
            online_purchase=True,
            type=VoucherType.EGIFT_CARD,
        )
        cls.bci = baker.make(
            BusinessCustomerInfo,
            business=cls.business,
        )
        cls.voucher = baker.make(
            Voucher,
            pos=cls.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=cls.bci,
            voucher_template=cls.voucher_template,
        )
        baker.make(
            VoucherServiceVariant,
            voucher=cls.voucher,
            service_variant=service_variant_recipe.make(
                service=service_recipe.make(business=cls.business)
            ),
        )
        cls.url = reverse(
            cls.endpoint_name,
            kwargs={
                'business_pk': cls.business.id,
                'voucher_pk': cls.voucher.id,
            },
        )

    def test_gift_card_pdf_requires_authenticated(self):
        self.client.credentials()
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_gift_card_pdf_no_access_for_staffer(self):
        self._create_session_for_user(Resource.STAFF_ACCESS_LEVEL_STAFF)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_gift_card_pdf_should_fail_if_not_gc(self):
        package_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            online_purchase=True,
            type=VoucherType.PACKAGE,  # not a gift card
        )
        voucher = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=package_template,
        )
        url = reverse(
            self.endpoint_name,
            kwargs={'business_pk': self.business.id, 'voucher_pk': voucher.id},
        )

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_gift_card_pdf(self):
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        expected_file_name = f'gift-card-{self.voucher.code}.pdf'
        self.assertEqual(
            response.headers['Content-Disposition'], f'attachment; filename="{expected_file_name}"'
        )

    def test_gift_card_pdf_expired(self):
        gift_card_expired = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.EXPIRED,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
        )
        url = reverse(
            self.endpoint_name,
            kwargs={'business_pk': self.business.id, 'voucher_pk': gift_card_expired.id},
        )

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)


class CustomerGiftCardViewSetTestCase(
    AuthenticatedCustomerAPITestCase
):  # pylint: disable=too-many-instance-attributes
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.url = reverse("customer_vouchers_wallet")
        self.business = baker.make(Business, owner=self.user)
        self.business_image = baker.make(
            Image, business_id=self.business.id, category=ImageTypeEnum.LOGO, image_url="foo.bar"
        )
        self.business_2 = baker.make(Business, owner=self.user)
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )
        self.bci_2 = baker.make(
            BusinessCustomerInfo,
            business=self.business_2,
            user=self.user,
        )
        self.pos = baker.make(POS, business=self.business)
        self.pos_2 = baker.make(POS, business=self.business_2)
        self.voucher_template_background_image = baker.make(Image, image_url='foo.baz')
        self.voucher_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            online_purchase=True,
            type=VoucherType.EGIFT_CARD,
            background_image=self.voucher_template_background_image,
        )
        self.voucher_template_2 = baker.make(
            VoucherTemplate,
            pos=self.pos_2,
            online_purchase=True,
            type=VoucherType.EGIFT_CARD,
        )
        self.voucher_template_package = baker.make(
            VoucherTemplate,
            pos=self.pos,
            online_purchase=True,
            type=VoucherType.PACKAGE,
        )

        self.voucher = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
        )
        self.voucher_2 = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
        )
        self.voucher_3 = baker.make(
            Voucher,
            pos=self.pos_2,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci_2,
            voucher_template=self.voucher_template_2,
        )
        self.voucher_expired = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.EXPIRED,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
        )
        self.voucher_reedemed = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.EXPIRED,
            current_balance=0,
            customer=self.bci,
            voucher_template=self.voucher_template,
        )
        self.voucher_archive = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=0,
            customer=self.bci,
            voucher_template=self.voucher_template,
        )
        self.voucher_package = baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template_package,
        )

    def test_customer_gift_cards_list(self):
        response = self.client.get(
            self.url,
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        voucher_ids = [voucher["id"] for voucher in response.data["results"]]
        self.assertEqual(
            sorted(voucher_ids),
            sorted([self.voucher.id, self.voucher_2.id, self.voucher_3.id]),
        )
        checked = False

        for row in response.data["results"]:
            if row["id"] == self.voucher.id:
                self.assertEqual(
                    row["business"]["logo"], 'https://img.booksy.pm/test-bucket/foo.bar'
                )
                self.assertEqual(
                    row["background_image_url"], 'https://img.booksy.pm/test-bucket/foo.baz'
                )
                checked = True
        self.assertEqual(checked, True)

    def test_customer_voucher_services_source(self):
        VoucherTemplateServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(), voucher_template=self.voucher_template
        )
        voucher_service_variant = VoucherServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(duration=relativedelta(minutes=16)),
            voucher=self.voucher,
            amount=2,
            item_price=19.28,
            initial_amount=30,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        voucher_services = response.data["results"][2]['services']
        self.assertEqual(len(voucher_services), 1)

        self.assertDictEqual(
            voucher_services[0],
            {
                'service_variant_id': voucher_service_variant.service_variant.id,
                'name': voucher_service_variant.service_variant.service.name,
                'service_variant_duration': 16,
                'service_variant_name': voucher_service_variant.service_variant.label,
            },
        )

    def test_customer_gift_cards_list_archive(self):
        response = self.client.get(
            self.url + "?archive=true",
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)  # one expired + two redeemed
        self.assertEqual(response.data["results"][0]["id"], self.voucher_archive.id)

    def test_customer_gift_cards_list_pagination(self):
        baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
            _quantity=30,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 20)

    def test_customer_gift_cards_list_num_queries(self):
        baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
            _quantity=30,
        )
        with asserts.assertNumQueries(13):  # pylint: disable=not-context-manager
            self.client.get(
                self.url,
                **self.headers,
            )

    def test_customer_gift_cards_list_pagination_all(self):
        Voucher.objects.all().delete()
        baker.make(
            Voucher,
            pos=self.pos,
            status=VoucherStatus.ACTIVE,
            current_balance=1,
            customer=self.bci,
            voucher_template=self.voucher_template,
            _quantity=30,
        )
        response = self.client.get(
            self.url + "?per_page=all",
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 30)
        self.assertEqual(len(response.data["results"]), 30)
        self.assertEqual(response.data["per_page"], 30)

    def test_customer_gift_cards_retrieve_voucher(self):
        response = self.client.get(
            reverse("customer_vouchers_wallet_detail", args=(self.voucher.id,)),
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data["voucher"]
        self.assertEqual(data["id"], self.voucher.id)
        self.assertEqual(data["business"]["logo"], 'https://img.booksy.pm/test-bucket/foo.bar')
        self.assertEqual(data["background_image_url"], 'https://img.booksy.pm/test-bucket/foo.baz')

    def test_customer_gift_cards_retrieve_services_source(self):
        VoucherTemplateServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(), voucher_template=self.voucher_template
        )
        voucher_service_variant = VoucherServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(duration=relativedelta(minutes=16)),
            voucher=self.voucher,
            amount=2,
            item_price=19.28,
            initial_amount=30,
        )
        response = self.client.get(
            reverse("customer_vouchers_wallet_detail", args=(self.voucher.id,)),
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data["voucher"]
        self.assertEqual(data["id"], self.voucher.id)
        self.assertEqual(data["business"]["logo"], 'https://img.booksy.pm/test-bucket/foo.bar')
        self.assertEqual(data["background_image_url"], 'https://img.booksy.pm/test-bucket/foo.baz')

        self.assertDictEqual(
            data['services'][0],
            {
                'service_variant_id': voucher_service_variant.service_variant.id,
                'name': voucher_service_variant.service_variant.service.name,
                'service_variant_duration': 16,
                'service_variant_name': voucher_service_variant.service_variant.label,
            },
        )

    def test_customer_gift_cards_retrieve_voucher_archive(self):
        response = self.client.get(
            reverse("customer_vouchers_wallet_detail", args=(self.voucher_archive.id,)),
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["voucher"]["id"], self.voucher_archive.id)

    def test_customer_gift_cards_retrieve_voucher_not_gift_card(self):
        response = self.client.get(
            reverse("customer_vouchers_wallet_detail", args=(self.voucher_package.id,)),
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_customer_gift_cards_retrieve_voucher_not_active(self):
        response = self.client.get(
            reverse("customer_vouchers_wallet_detail", args=(self.voucher_expired.id,)),
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_customer_gift_cards_retrieve_redeem_status(self):
        response = self.client.get(
            reverse("customer_vouchers_wallet_detail", args=(self.voucher_reedemed.id,)),
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # pylint: disable=protected-access
        self.assertEqual(response.data["voucher"]["status"], Voucher._REDEEMED)
        self.assertEqual(response.data["voucher"]["status_label"], 'Redeemed')

    @patch('webapps.qr_code_origami.storage.QRCodeFileStorage.client')
    def test_customer_gift_card_retrieve_voucher_pdf(self, _storage_client_mock):
        url = reverse('customer_vouchers_wallet_detail_download', args=(self.voucher.id,))
        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        expected_file_name = f'gift-card-{self.voucher.code}.pdf'
        self.assertEqual(
            response.headers['Content-Disposition'], f'attachment; filename="{expected_file_name}"'
        )


class VoucherPurchaseMethodsSettingsBaseTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )

        cls.headers = {'HTTP_X_API_KEY': cls.booking_source.api_key}
        cls.user = user_recipe.make()

    def setUp(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)
        self.business = business_recipe.make()
        self.business_2 = baker.make(Business, region=self.business.region)
        self.url = reverse("voucher_purchase_methods_settings", args=(self.business.id,))
        self.business_owner = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
            name='Kokoko Owner',
        )
        self.pos = baker.make(
            POS,
            business=self.business,
        )


class VoucherPurchaseMethodsSettingsTestCase(VoucherPurchaseMethodsSettingsBaseTestCase):
    def test_not_authenticated(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @override_settings(API_COUNTRY=Country.FR)
    def test_retrieve_no_enforce(self):
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        assert purchase_methods is None

    @override_settings(API_COUNTRY=Country.FR)
    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    def test_retrieve_enforce(self):
        # pylint: disable=protected-access
        self.pos._force_stripe_pba = True
        self.pos.save()
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        self.assertFalse(purchase_methods['pba_sale'])
        self.assertIsNone(purchase_methods['in_person'])

    def test_voucher_additional_info_created_on_success(self):
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertIsNotNone(VoucherAdditionalInfo.objects.get(pos__business=self.business))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    @override_settings(API_COUNTRY=Country.PL)
    def test_flag_is_false(self):
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {'purchase_methods': None})

    def test_access_from_other_business(self):
        url = reverse(
            "voucher_purchase_methods_settings",
            args=(self.business_2.id,),
        )
        response = self.client.get(
            url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_access_from_no_existing_biz(self):
        rev = reverse(
            "voucher_purchase_methods_settings",
            args=(Business.objects.aggregate(Max('id'))['id__max'] << 2,),
        )
        response = self.client.get(rev, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_no_enough_permissions(self):
        self.business_owner.staff_access_level = Resource.STAFF_ACCESS_LEVEL_ADVANCED
        self.business_owner.save()
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.business_owner.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.business_owner.save()
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_pos_is_required(self):
        _ = resource_recipe.make(
            business=self.business_2,
            staff_user=self.user,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
            name='Kokoko Owner',
        )
        self.assertIsNone(self.business_2.pos)
        url = reverse(
            "voucher_purchase_methods_settings",
            args=(self.business_2.id,),
        )
        response = self.client.get(
            url,
            **self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


@override_settings(API_COUNTRY=Country.FR)
@override_settings(POS__PAY_BY_APP=True)
@override_feature_flag(
    {
        VoucherOnlineSaleEnabledFlag: True,
    }
)
class VoucherPurchaseMethodsSettingsTestCaseAvailableOnlyForKYCBusiness(
    VoucherPurchaseMethodsSettingsBaseTestCase
):
    def test_retrieve_kyc_business_pba_disabled(self):
        self.plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()
        self.pos.recalculate_pos_plans()

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.pos.disable_pay_by_app()
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        dict_assert(
            purchase_methods,
            {
                'pba_sale': False,
                'in_person': None,
            },
        )

    def test_retrieve_kyc_business_pba_enabled(self):
        self.plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()
        self.pos.recalculate_pos_plans()

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        dict_assert(
            purchase_methods,
            {
                'pba_sale': True,
                'in_person': {
                    'active': True,
                    'valid_till': None,
                },
            },
        )

    def test_retrieve_adyen_business_pba_enabled_manually_france_case(self):
        self.pos.enable_pay_by_app()
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        dict_assert(
            purchase_methods,
            {
                'pba_sale': True,
                'in_person': {
                    'active': True,
                    'valid_till': None,
                },
            },
        )

    def test_retrieve_no_kyc_business(self):
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        self.assertIsNone(purchase_methods)

    @override_settings(API_COUNTRY=Country.PL)
    def test_retrieve_no_kyc_business_online_gc_disabled(self):
        self.voucher_additional_info = baker.make(
            VoucherAdditionalInfo,
            pos=self.pos,
        )
        response = self.client.get(
            self.url,
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        purchase_methods = response.json().get('purchase_methods')
        self.assertIsNone(purchase_methods)


class VoucherConfigViewTestCase(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse(
            'voucher_config',
        )

    @parameterized.expand(
        (
            (Country.PL, False, True),
            (Country.FR, True, False),
            (Country.US, False, True),
            (Country.GB, False, True),
        )
    )
    def test_200_depends_on_environ(self, country, price_equal_value, valid_till_enabled):
        with override_settings(API_COUNTRY=country):
            response = self.client.get(
                self.url,
                **self.headers,
            )

            default_voucher_image = DEFAULT_BACKGROUND_URL

            assert response.status_code == status.HTTP_200_OK
            if VoucherMigrationConfig.is_online():
                if VoucherMigrationConfig.is_strict():
                    validity_periods = [
                        {'label': '6 months from purchase', 'value': 'months_6'},
                        {'label': '9 months from purchase', 'value': 'months_9'},
                        {
                            'label': '12 months from purchase',
                            'value': 'year_1',
                            'default': True,
                        },
                        {'label': '24 months from purchase', 'value': 'months_24'},
                    ]
                else:  # moderate
                    if country == 'us':
                        validity_periods = [
                            {'label': '6 months from purchase', 'value': 'months_6'},
                            {'label': '9 months from purchase', 'value': 'months_9'},
                            {'label': '12 months from purchase', 'value': 'year_1'},
                            {'label': '24 months from purchase', 'value': 'months_24'},
                            {'label': 'Never', 'value': 'U', 'default': True},
                        ]
                    else:
                        validity_periods = [
                            {'label': '6 months from purchase', 'value': 'months_6'},
                            {'label': '9 months from purchase', 'value': 'months_9'},
                            {
                                'label': '12 months from purchase',
                                'value': 'year_1',
                                'default': True,
                            },
                            {'label': '24 months from purchase', 'value': 'months_24'},
                            {'label': 'Never', 'value': 'U'},
                        ]
                dict_assert(
                    response.json(),
                    {
                        'voucher_config': {
                            'default_background_image': get_default_background_gc_image_id(),
                            'default_background_image_url': default_voucher_image,
                            'ensure_price_value_equality': price_equal_value,
                            'valid_till_enabled': valid_till_enabled,
                            'validity_periods': validity_periods,
                            'processing_fee_note': '',
                        }
                    },
                )
            else:
                dict_assert(
                    response.json(),
                    {
                        'voucher_config': {
                            'default_background_image': get_default_background_gc_image_id(),
                            'default_background_image_url': default_voucher_image,
                            'ensure_price_value_equality': price_equal_value,
                            'valid_till_enabled': valid_till_enabled,
                            'validity_periods': [
                                {'label': '30 days from purchase', 'value': 'days_30'},
                                {'label': '90 days from purchase', 'value': 'days_90'},
                                {'label': '6 months from purchase', 'value': 'months_6'},
                                {
                                    'label': '1 year from purchase',
                                    'value': 'year_1',
                                    'default': True,
                                },
                                {'label': 'End of month', 'value': 'M'},
                                {'label': 'End of year', 'value': 'Y'},
                                {'label': 'Never', 'value': 'U'},
                            ],
                            'processing_fee_note': '',
                        }
                    },
                )

    @parameterized.expand(
        (
            (
                Country.PL,
                LegacyVoucherTemplateValidTill,
            ),
            (
                Country.US,
                OnlineVoucherTemplateValidTill,
            ),
            (
                Country.GB,
                OnlineVoucherTemplateValidTill,
            ),
            (
                Country.FR,
                FrenchVoucherTemplateValidTill,
            ),
        )
    )
    def test_validity_periods_flag(
        self,
        country: Country,
        expected_validity_periods,
    ):
        with override_settings(API_COUNTRY=country):
            response = self.client.get(self.url, **self.headers)
            print(response.json()['voucher_config']['validity_periods'])
            assert response.json()['voucher_config']['validity_periods'] == (
                expected_validity_periods.to_json()
            )

    @parameterized.expand(
        (
            (
                Country.US,
                [
                    {'label': '6 months from purchase', 'value': 'months_6'},
                    {'label': '9 months from purchase', 'value': 'months_9'},
                    {'label': '12 months from purchase', 'value': 'year_1'},
                    {'label': '24 months from purchase', 'value': 'months_24'},
                    {
                        'label': 'Never',
                        'value': 'U',
                        'default': True,
                    },
                ],
            ),
            (
                Country.GB,
                [
                    {'label': '6 months from purchase', 'value': 'months_6'},
                    {'label': '9 months from purchase', 'value': 'months_9'},
                    {
                        'label': '12 months from purchase',
                        'value': 'year_1',
                        'default': True,
                    },
                    {'label': '24 months from purchase', 'value': 'months_24'},
                    {'label': 'Never', 'value': 'U'},
                ],
            ),
        )
    )
    def test_validity_periods_moderate_depends_on_api_country(
        self,
        api_country,
        expected_validity_periods,
    ):
        with override_settings(API_COUNTRY=api_country):
            response = self.client.get(self.url, **self.headers)

        assert response.json()['voucher_config']['validity_periods'] == expected_validity_periods

    @parameterized.expand(
        (
            (Country.PL, True),
            (Country.US, True),
            (Country.FR, False),
        )
    )
    def test_valid_till_enabled_depends_on_env_and_flag(self, country, valid_till_enabled):
        with override_settings(API_COUNTRY=country):
            response = self.client.get(
                self.url,
                **self.headers,
            )
            assert response.json()['voucher_config']['valid_till_enabled'] == valid_till_enabled

    @override_settings(API_COUNTRY=Country.FR)
    def test_200_depends_on_feature_flag_ensure_price_value_equality(self):
        response = self.client.get(
            self.url,
            **self.headers,
        )

        assert response.status_code == status.HTTP_200_OK
        dict_assert(
            response.json(),
            {
                'voucher_config': {
                    'default_background_image': get_default_background_gc_image_id(),
                    'default_background_image_url': DEFAULT_BACKGROUND_URL,
                    'ensure_price_value_equality': True,
                }
            },
        )
        assert response.json()['voucher_config']['validity_periods']

    def test_not_authenticated(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @parameterized.expand(
        (
            (
                Country.US,
                'The processing fee applies.',
            ),
            (
                Country.GB,
                'The processing fee applies.',
            ),
            (
                Country.FR,
                '',
            ),
        )
    )
    @override_feature_flag({VoucherOnlineSaleEnabledFlag: True})
    def test_processing_fee_note_depends_on_api_country(
        self,
        api_country,
        expected_processing_fee_note,
    ):
        with (override_settings(API_COUNTRY=api_country),):
            response = self.client.get(self.url, **self.headers)

        assert (
            response.json()['voucher_config']['processing_fee_note'] == expected_processing_fee_note
        )

    @parameterized.expand(
        (
            (
                Country.US,
                True,
            ),
            (
                Country.GB,
                False,
            ),
            (
                Country.FR,
                False,
            ),
        )
    )
    @override_feature_flag({VoucherOnlineSaleEnabledFlag: True})
    def test_valid_till_hint_visible_depends_on_api_country(
        self,
        api_country,
        expected_valid_till_hint_visible,
    ):
        with override_settings(API_COUNTRY=api_country):
            response = self.client.get(self.url, **self.headers)

        assert (
            response.json()['voucher_config']['valid_till_hint_visible']
            == expected_valid_till_hint_visible
        )
