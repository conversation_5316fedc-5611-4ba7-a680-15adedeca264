import datetime
import unittest
from decimal import Decimal
from unittest import mock

import pytest
from dateutil.relativedelta import relativedelta
from django.test import override_settings
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from rest_framework.fields import empty

from country_config import Country
from lib.feature_flag.feature.voucher import (
    SendGiftCardExpirationEventFlag,
    VoucherInPersonSaleEnabledTillDateFlag,
    VoucherOnlineSaleEnabledFlag,
)
from lib.tests.utils import override_feature_flag
from webapps.business.baker_recipes import business_recipe, service_variant_recipe, service_recipe
from webapps.business.models import Business, Service, ServiceVariant
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import POS, PaymentRow, PaymentType, Receipt, Transaction
from webapps.user.models import User
from webapps.voucher.customer_serializers import VoucherPDFDetailsSerializer
from webapps.voucher.enums import VoucherType, VoucherStatus
from webapps.voucher.models import (
    Voucher,
    VoucherAdditionalInfo,
    VoucherServiceVariant,
    VoucherTemplate,
    VoucherTemplateServiceVariant,
)
from webapps.voucher.order_serializers import VoucherPurchaseMethodsSerializer
from webapps.voucher.serializers import (
    VoucherDetailSerializer,
    VoucherServiceSerializer,
)
from webapps.voucher.utils import VoucherMigrationConfig

pytestmark = pytest.mark.django_db


@pytest.mark.usefixtures('default_voucher_background')
class VoucherDetailSerializerTests(unittest.TestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make(Business)
        self.pos = baker.make(POS, business=self.business)
        self.voucher_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            value=Decimal('100.00'),
            type=VoucherType.EGIFT_CARD,
        )
        self.voucher = baker.make(
            Voucher,
            pos=self.pos,
            voucher_template=self.voucher_template,
            current_balance=Decimal('100.00'),
            valid_from=datetime.date(2023, 9, 10),
            valid_till=datetime.date(2024, 9, 10),
        )

        self.user = baker.make(User)
        self.context = {'business': self.business, 'pos': self.pos, 'operator': self.user}
        self.voucher_data = VoucherDetailSerializer(
            instance=self.voucher, context=self.context
        ).data

    def _get_serializer(self, data=empty, instance=None):
        serializer = VoucherDetailSerializer(
            data=data,
            instance=instance,
            context=self.context,
        )
        return serializer

    def test_redeemed_value(self):
        payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        serializer = VoucherDetailSerializer(instance=self.voucher, context=self.context)
        assert serializer.data['redeemed_value'] == 0

        baker.make(
            PaymentRow, voucher=self.voucher, amount=Decimal('12.34'), payment_type=payment_type
        )
        voucher = Voucher.objects.get(id=self.voucher.id)
        serializer = self._get_serializer(instance=voucher)
        assert serializer.data['redeemed_value'] == Decimal('12.34')

    def test_redeem_history(self):
        pba_pt = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        gift_card_pt = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.GIFT_CARD)
        voucher: Voucher = baker.make(
            Voucher,
            pos=self.pos,
            voucher_template=self.voucher_template,
            current_balance=Decimal('100.00'),
        )

        transaction = baker.make(
            Transaction,
            pos=self.pos,
        )

        receipt = baker.make(
            Receipt, transaction=transaction, status_code=receipt_status.CALL_FOR_PAYMENT
        )

        pr = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.CALL_FOR_PAYMENT,
            payment_type=pba_pt,
        )
        pr1 = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.PAYMENT_SUCCESS,
            payment_type=gift_card_pt,
            voucher=voucher,
        )

        receipt2 = baker.make(
            Receipt, transaction=transaction, status_code=receipt_status.PAYMENT_SUCCESS
        )
        baker.make(
            PaymentRow,
            receipt=receipt2,
            status=receipt_status.PAYMENT_SUCCESS,
            payment_type=pba_pt,
            parent_payment_row=pr,
        )
        baker.make(
            PaymentRow,
            receipt=receipt2,
            status=receipt_status.PAYMENT_SUCCESS,
            payment_type=gift_card_pt,
            parent_payment_row=pr1,
            voucher=voucher,
        )

        transaction.latest_receipt = receipt2
        transaction.save()

        serializer = self._get_serializer(instance=voucher)
        assert len(serializer.data['redeem_history']) == 1

    def test_is_valid_for_all_services(self):
        voucher = baker.make(
            Voucher,
            pos=self.pos,
            voucher_template=self.voucher_template,
            current_balance=Decimal('100.00'),
        )

        service = baker.make(Service, business=self.business, name='Service A')
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=30),
            active=True,
            price=Decimal('40.00'),
        )
        baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=40),
            active=True,
            price=Decimal('50.00'),
        )

        baker.make(
            VoucherServiceVariant,
            voucher=voucher,
            service_variant=service_variant,
            amount=9,
            initial_amount=10,
            item_price=Decimal('10.00'),
        )

        serializer = self._get_serializer(instance=voucher)

        assert serializer.data['services_number'] == 1

        self.voucher.is_valid_for_all_services = True
        self.voucher.save()
        serializer = VoucherDetailSerializer(instance=self.voucher)
        assert serializer.data['services_number'] == 2

    @override_settings(API_COUNTRY=Country.PL)
    def test_gc_edit_current_balance_disabled_flag_legacy(self):
        self.voucher_data['current_balance'] = '50.00'
        serializer = self._get_serializer(data=self.voucher_data, instance=self.voucher)
        assert serializer.is_valid() is True
        voucher = serializer.save()
        assert voucher.current_balance == Decimal(50.00)

    @override_settings(API_COUNTRY=Country.FR)
    def test_gc_edit_current_balance_disabled_flag_on(self):
        self.voucher_data['current_balance'] = '50.00'
        serializer = self._get_serializer(data=self.voucher_data, instance=self.voucher)
        assert serializer.is_valid() is False
        assert serializer.errors['current_balance'][0].code == 'edition_not_permitted'

    @override_settings(API_COUNTRY=Country.PL)
    def test_gc_edit_valid_from_disabled_flag_off(self):
        self.voucher_data['valid_from'] = '2023-09-01'
        serializer = self._get_serializer(data=self.voucher_data, instance=self.voucher)
        assert serializer.is_valid() is True
        voucher = serializer.save()
        assert voucher.valid_from == datetime.date(2023, 9, 1)

    @override_settings(API_COUNTRY=Country.FR)
    def test_gc_edit_valid_from_disabled_flag_on(self):
        self.voucher_data['valid_from'] = '2023-09-01'
        serializer = self._get_serializer(data=self.voucher_data, instance=self.voucher)
        assert serializer.is_valid() is False
        assert serializer.errors['valid_from'][0].code == 'edition_not_permitted'

    @parameterized.expand(
        [
            (Country.PL,),
            (Country.US,),
        ]
    )
    def test_gc_edit_valid_till(self, country):
        self.voucher_data['valid_till'] = '2025-09-01'
        with override_settings(API_COUNTRY=country):
            serializer = self._get_serializer(data=self.voucher_data, instance=self.voucher)
            assert serializer.is_valid() is True
            voucher = serializer.save()

        assert voucher.valid_till == datetime.date(2025, 9, 1)

    @parameterized.expand(
        [
            (Country.PL, VoucherType.MEMBERSHIP),
            (Country.US, VoucherType.MEMBERSHIP),
            (Country.GB, VoucherType.MEMBERSHIP),
            (Country.FR, VoucherType.MEMBERSHIP),
            (Country.PL, VoucherType.PACKAGE),
            (Country.US, VoucherType.PACKAGE),
            (Country.GB, VoucherType.PACKAGE),
            (Country.FR, VoucherType.PACKAGE),
        ]
    )
    def test_membership_package_edit_valid_from_valid_till(
        self,
        country,
        voucher_type,
    ):
        self.voucher_template.type = voucher_type
        self.voucher_template.save()

        voucher = baker.make(
            Voucher,
            pos=self.pos,
            voucher_template=self.voucher_template,
            valid_from=datetime.date(2023, 9, 10),
            valid_till=datetime.date(2024, 9, 10),
        )

        voucher_data = self._get_serializer(instance=voucher).data
        voucher_data['valid_from'] = '2023-09-01'
        voucher_data['valid_till'] = '2025-09-01'

        with override_settings(API_COUNTRY=country):
            serializer = self._get_serializer(data=voucher_data, instance=voucher)
            assert serializer.is_valid() is True
            voucher = serializer.save()

        assert voucher.valid_from == datetime.date(2023, 9, 1)
        assert voucher.valid_till == datetime.date(2025, 9, 1)

    @parameterized.expand(
        [
            (True, Voucher.VOUCHER_TYPE__MEMBERSHIP),
            (True, Voucher.VOUCHER_TYPE__PACKAGE),
            (False, Voucher.VOUCHER_TYPE__EGIFT_CARD),
            (False, Voucher.VOUCHER_TYPE__MEMBERSHIP),
            (False, Voucher.VOUCHER_TYPE__PACKAGE),
        ]
    )
    @mock.patch('lib.events.EventSignal.send')
    @freeze_time(datetime.datetime(2023, 9, 22))
    def test_gc_expiration_event_not_sent_manual_expiration(
        self,
        send_gf_expired_event_flag_value,
        voucher_type,
        send_signal_patched,
    ):
        self.voucher_template.type = voucher_type
        self.voucher_template.save()

        with override_feature_flag(
            {SendGiftCardExpirationEventFlag: send_gf_expired_event_flag_value},
        ):
            self.voucher.update_status()
            assert self.voucher.status == VoucherStatus.ACTIVE

            voucher_data = self._get_serializer(instance=self.voucher).data
            voucher_data['valid_till'] = '2023-09-21'
            serializer = self._get_serializer(data=voucher_data, instance=self.voucher)
            assert serializer.is_valid() is True
            voucher = serializer.save()

            assert voucher.status == VoucherStatus.EXPIRED
            assert not send_signal_patched.called

    @override_feature_flag({SendGiftCardExpirationEventFlag: True})
    @mock.patch('lib.events.EventSignal.send')
    @freeze_time(datetime.datetime(2023, 9, 22))
    def test_gc_expiration_event_sent_manual_expiration(
        self,
        send_signal_patched,
    ):
        self.voucher.update_status()
        assert self.voucher.status == VoucherStatus.ACTIVE

        voucher_data = self._get_serializer(instance=self.voucher).data
        voucher_data['valid_till'] = '2023-09-21'
        serializer = self._get_serializer(data=voucher_data, instance=self.voucher)
        assert serializer.is_valid() is True
        voucher = serializer.save()

        assert voucher.status == VoucherStatus.EXPIRED

        send_signal_patched.assert_called_once_with(
            self.business.id,
            voucher_id=self.voucher.id,
            user_id=self.user.id,
        )


@pytest.mark.usefixtures('default_voucher_background')
class VoucherServiceSerializerTests(unittest.TestCase):
    def setUp(self) -> None:
        super().setUp()
        business = baker.make(Business)
        pos = baker.make(POS, business=business)
        self.service = baker.make(Service, business=business, name='Service A')
        self.service_variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=30),
            active=True,
            price=Decimal('40.00'),
            label="Service variant name",
        )
        self.voucher = baker.make(Voucher, pos=pos)

    def test_service_variant_is_active(self):
        vsv = baker.make(
            VoucherServiceVariant,
            voucher=self.voucher,
            service_variant=self.service_variant,
        )

        serializer = VoucherServiceSerializer(instance=vsv)
        assert serializer.data['service_variant_is_active']

        self.service_variant.active = False
        self.service_variant.save()
        vsv.refresh_from_db()

        serializer = VoucherServiceSerializer(instance=vsv)
        assert not serializer.data['service_variant_is_active']

    def test_color(self):
        vsv = baker.make(
            VoucherServiceVariant,
            voucher=self.voucher,
            service_variant=self.service_variant,
        )
        serializer = VoucherServiceSerializer(instance=vsv)
        assert serializer.data['color']

        self.service.color = 5
        self.service.save()
        vsv.refresh_from_db()
        serializer = VoucherServiceSerializer(instance=vsv)
        assert serializer.data['color'] == 5

    def test_to_representation(self):
        self.service.color = 5
        self.service.save()
        vsv = baker.make(
            VoucherServiceVariant,
            voucher=self.voucher,
            service_variant=self.service_variant,
            amount=9,
            initial_amount=10,
            item_price=Decimal('10.00'),
        )
        serializer = VoucherServiceSerializer(instance=vsv)
        self.assertDictEqual(
            serializer.data,
            {
                'id': vsv.id,
                'service_variant_id': self.service_variant.id,
                'service_variant_series_id': self.service_variant.series,
                'amount': 9,
                'initial_amount': 10,
                'item_price': '10.00',
                'name': self.service.name,
                'service_variant_is_active': True,
                'service_variant_price': '40.00',
                'service_variant_name': 'Service variant name',
                'color': 5,
                'duration': 30.0,
            },
        )


class TestVoucherPurchaseMethodsSerializer:
    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: False,
        }
    )
    @pytest.mark.parametrize(
        'country',
        [
            Country.PL,
            Country.US,
            Country.GB,
            Country.FR,
        ],
    )
    def test_purchase_methods_per_flag_strict_enforce_not_enabled(self, country):
        with override_settings(API_COUNTRY=country):
            pos = pos_recipe.make(business=business_recipe.make())
            voucher_additional_info = VoucherAdditionalInfo.objects.create(pos=pos)
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods'] is None

    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    @pytest.mark.parametrize(
        'country',
        [
            Country.PL,
            Country.US,
            Country.GB,
        ],
    )
    def test_purchase_methods_per_flag(self, country):
        with override_settings(API_COUNTRY=country):
            pos = pos_recipe.make(
                business=business_recipe.make(),
                _force_stripe_pba=True,
            )
            voucher_additional_info = VoucherAdditionalInfo.objects.create(pos=pos)
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            if VoucherMigrationConfig.is_online():
                assert serialized_data['purchase_methods']
            else:
                assert serialized_data['purchase_methods'] is None

    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    @override_settings(API_COUNTRY=Country.FR)
    def test_purchase_methods_per_flag_fr_not_stripe(self):
        pos = pos_recipe.make(business=business_recipe.make())
        voucher_additional_info = VoucherAdditionalInfo.objects.create(pos=pos)
        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods'] is None

    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    @override_settings(API_COUNTRY=Country.FR)
    def test_online(self):
        pos = pos_recipe.make(
            business=business_recipe.make(),
            pay_by_app_status=POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL,
            _force_stripe_pba=True,
        )
        voucher_additional_info = VoucherAdditionalInfo.objects.create(pos=pos)
        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods']['pba_sale'] is False

        pos.pay_by_app_status = POS.PAY_BY_APP_ENABLED
        pos.save()
        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods']['pba_sale']
        assert serialized_data['purchase_methods']['in_person']

    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    @override_settings(API_COUNTRY=Country.FR)
    def test_in_person_active_via_pba(self):
        pos = pos_recipe.make(
            business=business_recipe.make(),
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
            _force_stripe_pba=True,
        )
        voucher_additional_info = VoucherAdditionalInfo.objects.create(
            pos=pos,
        )
        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods']['in_person']['active'] is True

    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    @override_settings(API_COUNTRY=Country.FR)
    @freeze_time(datetime.datetime(2023, 6, 10, 15, 0, 0))
    def test_in_person_valid_till(self):
        pos = pos_recipe.make(
            business=business_recipe.make(),
            pay_by_app_status=POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL,
            _force_stripe_pba=True,
        )
        voucher_additional_info = VoucherAdditionalInfo.objects.create(pos=pos)

        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods']['in_person'] is None

        pos.pay_by_app_status = POS.PAY_BY_APP_ENABLED
        pos.save()
        voucher_additional_info.refresh_from_db()

        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods']['in_person'] == {
            'active': True,
            'valid_till': None,
        }

        with override_feature_flag({VoucherInPersonSaleEnabledTillDateFlag: "2023-06-11"}):
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods']['pba_sale'] is True
            assert serialized_data['purchase_methods']['in_person'] == {
                'active': True,
                'valid_till': None,
            }

        with override_feature_flag({VoucherInPersonSaleEnabledTillDateFlag: "2023-05-11"}):
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods']['in_person'] == {
                'active': True,
                'valid_till': None,
            }

        pos.pay_by_app_status = POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL
        pos.save()
        voucher_additional_info.refresh_from_db()

        with override_feature_flag({VoucherInPersonSaleEnabledTillDateFlag: "2023-06-11"}):
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods']['pba_sale'] is False
            assert serialized_data['purchase_methods']['in_person'] is None

        with override_feature_flag({VoucherInPersonSaleEnabledTillDateFlag: "2023-05-11"}):
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods']['in_person'] is None

    @override_feature_flag(
        {
            VoucherOnlineSaleEnabledFlag: True,
        }
    )
    @override_settings(API_COUNTRY=Country.FR)
    @freeze_time(datetime.datetime(2023, 6, 10, 15, 0, 0))
    def test_in_person_valid_till_by_previous_experience(self):
        pos = pos_recipe.make(
            business=business_recipe.make(),
            pay_by_app_status=POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL,
            _force_stripe_pba=True,
        )
        voucher_additional_info = VoucherAdditionalInfo.objects.create(
            pos=pos, sold_vouchers_before_migration_threshold=True
        )

        serialized_data = VoucherPurchaseMethodsSerializer(instance=voucher_additional_info).data
        assert serialized_data['purchase_methods']['in_person'] == {
            "active": False,
            "valid_till": None,
        }
        assert serialized_data['purchase_methods']['pba_sale'] is False

        with override_feature_flag({VoucherInPersonSaleEnabledTillDateFlag: "2023-06-11"}):
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods']['in_person'] == {
                'active': True,
                'valid_till': "2023-06-11",
            }

        with override_feature_flag({VoucherInPersonSaleEnabledTillDateFlag: "2023-05-11"}):
            serialized_data = VoucherPurchaseMethodsSerializer(
                instance=voucher_additional_info
            ).data
            assert serialized_data['purchase_methods']['in_person'] == {
                'active': False,
                'valid_till': None,
            }


class TestVoucherPDFDetailSerializer(unittest.TestCase):
    def test_voucher_pdf_serializer_has_keys_to_render_pdf(self):
        pos = pos_recipe.make(
            business=business_recipe.make(),
            pay_by_app_status=POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL,
        )
        voucher_template = baker.make(
            VoucherTemplate,
            pos=pos,
            value=Decimal('100.00'),
        )
        gift_card = baker.make(
            Voucher,
            status=VoucherStatus.ACTIVE,
            pos=pos,
            current_balance=1,
            voucher_template=voucher_template,
        )
        baker.make(
            VoucherServiceVariant,
            voucher=gift_card,
            service_variant=service_variant_recipe.make(
                service=service_recipe.make(business=pos.business)
            ),
        )
        serializer = VoucherPDFDetailsSerializer(gift_card)
        self.assertSetEqual(
            set(serializer.data.keys()),
            {
                'name',
                'value',
                'business',
                'description',
                'services',
                'valid_till',
                'background_image_url',
            },
        )
        # assert expected date format for the PDF
        self.assertEqual(serializer.data['valid_till'], gift_card.valid_till.strftime('%d/%m/%Y'))
        self.assertEqual(len(serializer.data['services']), 1)
        self.assertSetEqual(
            set(serializer.data['services'][0].keys()), {'name', 'service_variant_name'}
        )
        minimum_business_keys = {'name', 'address', 'logo'}
        self.assertSetEqual(
            serializer.data['business'].keys() & minimum_business_keys, minimum_business_keys
        )

    def test_voucher_pdf_serializer_services(self):
        pos = pos_recipe.make(
            business=business_recipe.make(),
            pay_by_app_status=POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL,
        )
        voucher_template = baker.make(
            VoucherTemplate,
            pos=pos,
            value=Decimal('100.00'),
        )
        # Add service to a TEMPLATE.
        template_service = baker.make(
            VoucherTemplateServiceVariant,
            voucher_template=voucher_template,
            service_variant=service_variant_recipe.make(
                service=service_recipe.make(business=pos.business)
            ),
        )

        # Gift card doesn't have services assigned
        voucher = baker.make(
            Voucher,
            status=VoucherStatus.ACTIVE,
            pos=pos,
            current_balance=1,
            voucher_template=voucher_template,
        )
        serializer = VoucherPDFDetailsSerializer(voucher)
        self.assertEqual(len(serializer.data['services']), 0)

        template_service.delete()  # modify the template's services
        baker.make(
            VoucherServiceVariant,
            voucher=voucher,
            service_variant=service_variant_recipe.make(
                service=service_recipe.make(business=pos.business)
            ),
        )

        voucher.refresh_from_db()
        serializer = VoucherPDFDetailsSerializer(voucher)
        self.assertEqual(len(serializer.data['services']), 1)
