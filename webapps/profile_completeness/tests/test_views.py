import re
from copy import deepcopy

import pytest
import responses
from django.urls import reverse_lazy
from mock import patch
from model_bakery import baker
from rest_framework import status
from rest_framework.reverse import reverse
from rest_framework.test import APITestCase

from drf_api.lib.base_drf_test_case import BusinessManagerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.payment import ShowNewFinancialCenterFlag
from lib.payment_providers.mocks import (
    get_provider_account_details_mock,
    get_provider_account_details_turned_off_mock,
    get_provider_account_details_kyc_pending_mock,
    get_provider_account_details_kyc_pending_info_missing_mock,
    get_provider_account_details_not_verified_mock,
)
from lib.test_utils import user_recipe
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.consts import ANDROID_SOLO
from webapps.market_pay.baker_recipes import account_holder_recipe
from webapps.market_pay.enums import AccountHolderStatus
from webapps.notification.enums import NotificationTarget
from webapps.pos.baker_recipes import pos_recipe, payment_type_recipe
from webapps.pos.models import POS
from webapps.profile_completeness.consts import TILES
from webapps.profile_completeness.enums import TierLevel
from webapps.profile_completeness.helpers import (
    get_tiers_for_business,
)
from webapps.profile_completeness.models import (
    CompletedTier,
    CompletedStep,
    ProfileCompleteness,
    ProfileCompletenessStats,
)
from webapps.profile_completeness.steps import (
    UpgradeVoicemailGreetingsStep,
)
from webapps.profile_completeness.steps import get_offline_steps
from webapps.stripe_integration.baker_recipes import stripe_account_recipe
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.user.enums import AuthOriginEnum


class TierViewAPITestCase(BusinessManagerAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse('profile_completeness_tiers', kwargs={'business_pk': self.business.id})

    def test_get(self):
        with patch('webapps.profile_completeness.serializers.StaticRootS3Boto3Storage') as storage:
            storage.return_value.exists.return_value = False
            response = self.client.get(self.url)
            resp_json = response.json()
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        resp_tiers = resp_json['tiers']
        self.assertEqual(len(resp_tiers), len(TierLevel))

        tiers = get_tiers_for_business(self.business)
        self.assertEqual(len(resp_tiers), len(tiers))
        for resp_tier, tier in zip(resp_tiers, tiers):
            self.assertEqual(resp_tier['description'], tier.description)
            self.assertEqual(resp_tier['title'], tier.title)
            self.assertEqual(resp_tier['completed'], 0)
            self.assertEqual(resp_tier['steps_count'], len(tier.steps))
            if tier.title == 'Novice':
                self.assertEqual(resp_tier['active'], True)
            else:
                self.assertEqual(resp_tier['active'], False)
            self.assertIsNotNone(resp_tier['background'])
            self.assertEqual(len(resp_tier['steps']), len(tier.steps))

            for resp_step, step in zip(resp_tier['steps'], tier.steps):
                self.assertEqual(resp_step['step_internal_name'], step.internal_name)
                self.assertEqual(resp_step['title'], step.title)
                self.assertEqual(resp_step['checked'], step.checked)
                self.assertEqual(resp_step['checked'], False)
                self.assertEqual(resp_step['offline'], step.offline)
                self.assertEqual(resp_step['offline_title'], step.offline_title)
                self.assertEqual(resp_step['offline_description'], step.offline_description)

                if resp_step['step_internal_name'] == UpgradeVoicemailGreetingsStep.internal_name:
                    assert (
                        resp_step['offline_link'] == 'https://booksy.com/blog/gb/2020/10/08/'
                        'update-your-voicemail-to-let-clients'
                        '-know-to-use-booksy/'
                    )
                else:
                    self.assertIsNone(resp_step['offline_link'])
                self.assertEqual(resp_step['target'], step.target)

            self.assertEqual(len(resp_tier['rewards']), len(tier.rewards))
            for resp_reward, reward in zip(resp_tier['rewards'], tier.rewards):
                self.assertEqual(resp_reward['title'], reward.title)
                self.assertEqual(resp_reward['target'], reward.target)

            self.assertTrue(ProfileCompleteness.objects.filter(business=self.business).exists())
            self.assertFalse(CompletedStep.objects.filter(business=self.business).exists())
            self.assertFalse(CompletedTier.objects.filter(business=self.business).exists())


class TierActiveViewAPITestCase(BusinessManagerAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse(
            'profile_completeness_tiers_active', kwargs={'business_pk': self.business.id}
        )

    def test_get(self):
        with patch('webapps.profile_completeness.serializers.StaticRootS3Boto3Storage') as storage:
            storage.return_value.exists.return_value = False
            response = self.client.get(self.url)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertTrue(ProfileCompleteness.objects.filter(business=self.business).exists())
        self.assertFalse(CompletedStep.objects.filter(business=self.business).exists())
        self.assertFalse(CompletedTier.objects.filter(business=self.business).exists())
        self.assertFalse(ProfileCompletenessStats.objects.filter(business=self.business).exists())


class StepFinishViewAPITestCase(BusinessManagerAPITestCase):
    def setUp(self):
        super().setUp()
        self.profile, _ = ProfileCompleteness.objects.get_or_create(
            business=self.business,
        )

    def url(self, step_internal_name):
        return reverse(
            'profile_completeness_step_finish',
            kwargs={'business_pk': self.business.id, 'step_internal_name': step_internal_name},
        )

    def test_post_success(self):
        for step in get_offline_steps():
            self.profile.tier_level = step.tier_level
            self.profile.save()
            response = self.client.post(self.url(step_internal_name=step.internal_name), data={})
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)

            self.assertTrue(ProfileCompleteness.objects.filter(business=self.business).exists())
            self.assertTrue(
                CompletedStep.objects.filter(
                    business=self.business, step_internal_name=step.internal_name
                ).exists()
            )
            self.assertFalse(CompletedTier.objects.filter(business=self.business).exists())
            self.assertFalse(
                ProfileCompletenessStats.objects.filter(business=self.business).exists()
            )

    def test_invalid_step_name(self):
        response = self.client.post(self.url(step_internal_name='invalid_step_name'), data={})
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertTrue(ProfileCompleteness.objects.filter(business=self.business).exists())
        self.assertFalse(
            CompletedStep.objects.filter(
                business=self.business, step_internal_name='invalid_step_name'
            ).exists()
        )


@pytest.mark.django_db
class TestPaymentActivatorsTilesView(APITestCase):
    endpoint_name = 'payment_activators_tiles'

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=ANDROID_SOLO,
        )
        cls.headers = {'HTTP_X_API_KEY': cls.booking_source.api_key}
        cls.user = user_recipe.make()
        cls.TILES_BASE_TEMPLATE = deepcopy(TILES)

    @staticmethod
    def add_responses_for_balance():
        responses.add(
            responses.GET,
            re.compile(r'https://api.stripe.com/v1/balance'),
            status=status.HTTP_200_OK,
            headers={
                "Content-Type": "application/json",
            },
            json={
                "object": "balance",
                "available": [{"amount": 0, "currency": "usd", "source_types": {"card": 0}}],
                "connect_reserved": [{"amount": 0, "currency": "usd"}],
                "instant_available": [
                    {"amount": 6, "currency": "usd", "source_types": {"card": 7}},
                    {"amount": 6, "currency": "gbp", "source_types": {"bank_account": 10048}},
                ],
                "livemode": False,
                "pending": [{"amount": 0, "currency": "usd", "source_types": {"card": 0}}],
            },
        )

    def create_pos_and_stripe_account_object_for_business(
        self, account_status: StripeAccountStatus = StripeAccountStatus.VERIFIED
    ):
        pos = pos_recipe.make(
            business=self.business, _force_stripe_pba=True,
        )
        account_holder_recipe.make(
            pos=pos,
            status=AccountHolderStatus.ACTIVE.value,
            ever_passed_kyc=True,
            payout_allowed=True,
        )
        stripe_account = stripe_account_recipe.make(pos=pos, status=account_status)
        return pos, stripe_account

    def setUp(self) -> None:
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)
        self.business = business_recipe.make(status=Business.Status.PAID)
        self.resource = baker.make(
            'business.Resource',
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )

    def test_get_tiles_data_no_active_sub(self):
        pos_recipe.make(
            business=self.business, _force_stripe_pba=True,
        )
        self.business.status = Business.Status.TRIAL
        self.business.save()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(
            resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE_SUBSCRIBE
        )

    @override_eppo_feature_flag({ShowNewFinancialCenterFlag.flag_name: True})
    def test_get_tiles_new_financial_center_true_stripe_pos(self):
        pos_recipe.make(
            business=self.business, _force_stripe_pba=True
        )
        self.business.status = Business.Status.TRIAL
        self.business.save()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 0)

    @override_eppo_feature_flag({ShowNewFinancialCenterFlag.flag_name: False})
    def test_get_tiles_new_financial_center_false_stripe_pos(self):
        pos_recipe.make(
            business=self.business, _force_stripe_pba=True
        )
        self.business.status = Business.Status.TRIAL
        self.business.save()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(
            resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE_SUBSCRIBE
        )

    @responses.activate
    def test_get_tiles_data_kyc_not_started(self):
        pos_recipe.make(
            business=self.business, _force_stripe_pba=True,
        )
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(
            resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE_ENABLE
        )

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_turned_off_mock(),
    )
    @responses.activate
    def test_get_tiles_data_kyc_not_passed(self, get_stripe_account_details_mock):
        self.create_pos_and_stripe_account_object_for_business(
            account_status=StripeAccountStatus.TURNED_OFF
        )
        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(
            resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE_ENABLE
        )

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_turned_off_mock,
    )
    def test_get_tiles_data_kyc_not_passed_lower_access_level(
        self, get_stripe_account_details_mock
    ):
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.resource.save()

        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 0)

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_kyc_pending_info_missing_mock(),
    )
    @responses.activate
    def test_get_tiles_data_kyc_pending_info_missing(self, get_stripe_account_details_mock):
        self.create_pos_and_stripe_account_object_for_business(
            account_status=StripeAccountStatus.VERIFICATION_PENDING
        )
        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(
            resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE_PENDING_KYC
        )

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_kyc_pending_mock(),
    )
    @responses.activate
    def test_get_tiles_data_kyc_pending(self, get_stripe_account_details_mock):
        self.create_pos_and_stripe_account_object_for_business(
            account_status=StripeAccountStatus.VERIFICATION_PENDING
        )
        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(
            resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE_PENDING_KYC
        )

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_not_verified_mock(),
    )
    @responses.activate
    def test_get_tiles_data_kyc_not_verified(self, get_stripe_account_details_mock):
        self.create_pos_and_stripe_account_object_for_business(
            account_status=StripeAccountStatus.NOT_VERIFIED
        )
        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(resp['tiles_data'][0]['target'], NotificationTarget.KYC_DASHBOARD)

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_mock(),
    )
    @responses.activate
    def test_get_tiles_data_kyc_passed(self, get_stripe_account_details_mock):
        self.create_pos_and_stripe_account_object_for_business()
        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE)
        #   check if tiles base template was not changed after request
        self.assertEqual(self.TILES_BASE_TEMPLATE, TILES)

    @responses.activate
    def test_get_tiles_data_kyc_passed_lower_access_level(self):
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.resource.save()

        self.create_pos_and_stripe_account_object_for_business()
        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 0)

    @patch(
        'webapps.profile_completeness.profile_tiles.get_stripe_account_details',
        return_value=get_provider_account_details_mock(),
    )
    @responses.activate
    def test_get_tiles_data_pay_by_app_enabled(self, get_stripe_account_details_mock):
        pos, _ = self.create_pos_and_stripe_account_object_for_business()
        pos.pay_by_app_status = POS.PAY_BY_APP_ENABLED
        payment_type_recipe.make(pos=pos)
        pos.save()

        self.add_responses_for_balance()
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id})
        resp = self.client.get(url, **self.headers).json()

        self.assertEqual(len(resp['tiles_data']), 1)
        self.assertEqual(resp['tiles_data'][0]['target'], NotificationTarget.PAYMENTS_BALANCE)

    def test_get_tiles_business_not_found(self):
        url = reverse_lazy(self.endpoint_name, kwargs={'business_pk': self.business.id + 1})
        resp = self.client.get(url, **self.headers)

        self.assertEqual(resp.status_code, status.HTTP_404_NOT_FOUND)
